{"host": "0.0.0.0", "port": 9924, "ffmpeg": "ffmpeg", "dev_storageDir": "/Users/<USER>/data", "storageDir": "/data/xclabel/static/storage", "logging": {"max_file_size_mb": 20, "backup_count": 30, "log_level": "INFO", "console_output": true, "request_logging": true, "db_logging": true}, "yolo8": {"install_dir": "./model/yolov8", "venv": "venv/bin/activate", "name": "yolo", "model": "yolov8n.pt"}, "yolo11": {"install_dir": "./model/yolov11", "venv": "venv/bin/activate", "name": "yolo", "model": "yolo11n.pt"}}