<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除确认功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #c82333;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>删除确认功能测试页面</h1>
    <p>此页面用于测试系统中所有删除操作的二次确认功能。</p>

    <div class="test-section">
        <h3>1. 用户删除确认测试</h3>
        <p>测试用户删除功能的确认对话框</p>
        <button class="test-button" onclick="testUserDelete()">测试用户删除</button>
        <div id="user-status" class="status" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>2. 任务删除确认测试</h3>
        <p>测试任务删除功能的确认对话框</p>
        <button class="test-button" onclick="testTaskDelete()">测试任务删除</button>
        <div id="task-status" class="status" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>3. 训练删除确认测试</h3>
        <p>测试训练任务删除功能的确认对话框</p>
        <button class="test-button" onclick="testTrainDelete()">测试训练删除</button>
        <div id="train-status" class="status" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>4. 样本删除确认测试</h3>
        <p>测试样本删除功能的确认对话框</p>
        <button class="test-button" onclick="testSampleDelete()">测试样本删除</button>
        <div id="sample-status" class="status" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>5. 标注删除确认测试</h3>
        <p>测试标注删除功能的确认对话框</p>
        <button class="test-button" onclick="testAnnotationDelete()">测试标注删除</button>
        <div id="annotation-status" class="status" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>6. 训练测试删除确认测试</h3>
        <p>测试训练测试结果删除功能的确认对话框</p>
        <button class="test-button" onclick="testTrainTestDelete()">测试训练测试删除</button>
        <div id="traintest-status" class="status" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>7. 标签删除确认测试</h3>
        <p>测试标签删除功能的确认对话框</p>
        <button class="test-button" onclick="testLabelDelete()">测试标签删除</button>
        <div id="label-status" class="status" style="display:none;"></div>
    </div>

    <script>
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'status ' + type;
            element.style.display = 'block';
        }

        // 模拟用户删除确认
        function testUserDelete() {
            if (confirm('确定要删除这个用户吗？删除后无法恢复！')) {
                showStatus('user-status', '✓ 用户确认删除 - 确认对话框正常工作', 'success');
            } else {
                showStatus('user-status', '✓ 用户取消删除 - 确认对话框正常工作', 'warning');
            }
        }

        // 模拟任务删除确认
        function testTaskDelete() {
            if (confirm('确定要删除这个任务吗？删除后无法恢复！')) {
                showStatus('task-status', '✓ 用户确认删除 - 确认对话框正常工作', 'success');
            } else {
                showStatus('task-status', '✓ 用户取消删除 - 确认对话框正常工作', 'warning');
            }
        }

        // 模拟训练删除确认
        function testTrainDelete() {
            if (confirm('确定要删除这个训练任务吗？删除后无法恢复！')) {
                showStatus('train-status', '✓ 用户确认删除 - 确认对话框正常工作', 'success');
            } else {
                showStatus('train-status', '✓ 用户取消删除 - 确认对话框正常工作', 'warning');
            }
        }

        // 模拟样本删除确认
        function testSampleDelete() {
            if (confirm('确定要删除这个样本吗？删除后无法恢复！')) {
                showStatus('sample-status', '✓ 用户确认删除 - 确认对话框正常工作', 'success');
            } else {
                showStatus('sample-status', '✓ 用户取消删除 - 确认对话框正常工作', 'warning');
            }
        }

        // 模拟标注删除确认
        function testAnnotationDelete() {
            if (confirm('确定要删除当前样本的标注吗？删除后无法恢复！')) {
                showStatus('annotation-status', '✓ 用户确认删除 - 确认对话框正常工作', 'success');
            } else {
                showStatus('annotation-status', '✓ 用户取消删除 - 确认对话框正常工作', 'warning');
            }
        }

        // 模拟训练测试删除确认
        function testTrainTestDelete() {
            if (confirm('确定要删除这个测试结果吗？删除后无法恢复！')) {
                showStatus('traintest-status', '✓ 用户确认删除 - 确认对话框正常工作', 'success');
            } else {
                showStatus('traintest-status', '✓ 用户取消删除 - 确认对话框正常工作', 'warning');
            }
        }

        // 模拟标签删除确认
        function testLabelDelete() {
            if (confirm('确定要删除这个标签吗？删除后无法恢复！')) {
                showStatus('label-status', '✓ 用户确认删除 - 确认对话框正常工作', 'success');
            } else {
                showStatus('label-status', '✓ 用户取消删除 - 确认对话框正常工作', 'warning');
            }
        }
    </script>
</body>
</html>
