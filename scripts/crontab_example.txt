# XCLabel 日志管理 Cron 配置示例
# 
# 使用方法:
# 1. 复制此文件内容到你的crontab配置中
# 2. 修改路径为你的实际项目路径
# 3. 根据需要调整执行频率
#
# 编辑crontab: crontab -e
# 查看crontab: crontab -l

# 每天凌晨2点执行日志清理
0 2 * * * /usr/bin/python3 /path/to/your/project/scripts/log_cleanup.py >> /path/to/your/project/log/cleanup.log 2>&1

# 每周日凌晨3点执行日志压缩（可选，如果不想每天压缩的话）
# 0 3 * * 0 /usr/bin/python3 /path/to/your/project/app/utils/LogManager.py --log-dir /path/to/your/project/log --action compress >> /path/to/your/project/log/compress.log 2>&1

# 每月1号凌晨4点执行深度清理（删除超过60天的日志）
# 0 4 1 * * /usr/bin/python3 /path/to/your/project/app/utils/LogManager.py --log-dir /path/to/your/project/log --action clean --max-age 60 >> /path/to/your/project/log/deep_clean.log 2>&1

# 注意事项:
# 1. 请将 /path/to/your/project 替换为你的实际项目路径
# 2. 确保Python路径正确 (可以用 which python3 查看)
# 3. 确保脚本有执行权限: chmod +x /path/to/your/project/scripts/log_cleanup.py
# 4. 建议先手动执行一次脚本确保正常工作
# 5. 日志清理的输出会记录到 cleanup.log 文件中

# Cron时间格式说明:
# 分钟(0-59) 小时(0-23) 日(1-31) 月(1-12) 星期(0-7, 0和7都表示星期日)
# 
# 示例:
# 0 2 * * *     每天凌晨2点
# 30 1 * * 0    每周日凌晨1点30分
# 0 0 1 * *     每月1号凌晨0点
# */30 * * * *  每30分钟
# 0 */6 * * *   每6小时
