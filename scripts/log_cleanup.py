#!/usr/bin/env python3
"""
日志清理脚本
定期清理过期的日志文件，可以通过cron定时执行
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'framework.settings')
django.setup()

from app.utils.LogManager import LogManager
from app.utils.Config import Config
from framework.settings import BASE_DIR


def main():
    """主函数"""
    try:
        # 读取配置
        config = Config(filepath=os.path.join(BASE_DIR, "config.json"))
        log_config = config.logging
        
        # 创建日志管理器
        log_dir = os.path.join(BASE_DIR, "log")
        log_manager = LogManager(
            log_dir=log_dir,
            max_age_days=log_config["backup_count"],  # 使用backup_count作为保留天数
            max_size_mb=log_config["max_file_size_mb"]
        )
        
        print(f"开始清理日志文件...")
        print(f"日志目录: {log_dir}")
        print(f"保留天数: {log_config['backup_count']}")
        print(f"文件大小限制: {log_config['max_file_size_mb']} MB")
        
        # 获取当前日志统计
        stats_before = log_manager.get_log_stats()
        print(f"\n清理前统计:")
        print(f"  文件数量: {stats_before['total_files']}")
        print(f"  总大小: {stats_before['total_size_mb']} MB")
        
        # 清理过期日志
        clean_result = log_manager.clean_old_logs(dry_run=False)
        print(f"\n清理结果:")
        print(f"  删除文件数: {clean_result['deleted_count']}")
        print(f"  释放空间: {clean_result['total_size_freed_mb']} MB")
        
        if clean_result['deleted_files']:
            print(f"\n删除的文件:")
            for file_info in clean_result['deleted_files']:
                print(f"  {file_info['file']}: {file_info['size_mb']} MB, {file_info['age_days']} 天")
        
        # 压缩7天以上的日志文件
        compress_result = log_manager.compress_old_logs(days_threshold=7, dry_run=False)
        print(f"\n压缩结果:")
        print(f"  压缩文件数: {compress_result['compressed_count']}")
        print(f"  节省空间: {compress_result['total_size_saved_mb']} MB")
        
        if compress_result['compressed_files']:
            print(f"\n压缩的文件:")
            for file_info in compress_result['compressed_files']:
                print(f"  {file_info['original_file']} -> {file_info['compressed_file']}")
                print(f"    压缩率: {file_info['compression_ratio']}%, 节省: {file_info['size_saved_mb']} MB")
        
        # 获取清理后的统计
        stats_after = log_manager.get_log_stats()
        print(f"\n清理后统计:")
        print(f"  文件数量: {stats_after['total_files']}")
        print(f"  总大小: {stats_after['total_size_mb']} MB")
        
        print(f"\n日志清理完成!")
        
    except Exception as e:
        print(f"日志清理失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
