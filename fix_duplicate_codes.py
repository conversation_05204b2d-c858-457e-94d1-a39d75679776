#!/usr/bin/env python3
"""
修复数据库中重复的样本代码
"""
import os
import sys
import django
from datetime import datetime
import uuid

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'framework.settings')
django.setup()

from app.models import TaskSample

def generate_unique_sample_code():
    """
    生成唯一的样本代码
    """
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f")[:-3]  # 毫秒级时间戳
    uuid_part = str(uuid.uuid4()).replace('-', '')[:8]
    return f"sample{timestamp}{uuid_part}"

def find_duplicate_codes():
    """
    查找重复的样本代码
    """
    print("正在查找重复的样本代码...")
    
    # 查询所有样本代码及其出现次数
    from django.db import connection
    cursor = connection.cursor()
    cursor.execute("""
        SELECT code, COUNT(*) as count, GROUP_CONCAT(id) as ids
        FROM xc_task_sample 
        GROUP BY code 
        HAVING COUNT(*) > 1
        ORDER BY count DESC
    """)
    
    duplicates = cursor.fetchall()
    
    if not duplicates:
        print("✅ 没有发现重复的样本代码")
        return []
    
    print(f"🔍 发现 {len(duplicates)} 个重复的样本代码:")
    for code, count, ids in duplicates:
        print(f"  - 代码: {code}, 重复次数: {count}, ID列表: {ids}")
    
    return duplicates

def fix_duplicate_codes(dry_run=True):
    """
    修复重复的样本代码
    """
    duplicates = find_duplicate_codes()
    
    if not duplicates:
        return
    
    print(f"\n{'🔧 开始修复重复代码' if not dry_run else '📋 预览修复操作'}...")
    
    fixed_count = 0
    for code, count, ids_str in duplicates:
        ids = [int(id_str) for id_str in ids_str.split(',')]
        
        # 保留第一个记录，修复其他记录
        keep_id = ids[0]
        fix_ids = ids[1:]
        
        print(f"\n处理重复代码: {code}")
        print(f"  保留记录 ID: {keep_id}")
        print(f"  需要修复的记录 ID: {fix_ids}")
        
        for fix_id in fix_ids:
            new_code = generate_unique_sample_code()
            
            if dry_run:
                print(f"    [预览] ID {fix_id}: {code} -> {new_code}")
            else:
                try:
                    sample = TaskSample.objects.get(id=fix_id)
                    old_code = sample.code
                    sample.code = new_code
                    sample.save()
                    print(f"    ✅ ID {fix_id}: {old_code} -> {new_code}")
                    fixed_count += 1
                except TaskSample.DoesNotExist:
                    print(f"    ❌ ID {fix_id}: 记录不存在")
                except Exception as e:
                    print(f"    ❌ ID {fix_id}: 修复失败 - {e}")
    
    if not dry_run:
        print(f"\n🎉 修复完成！共修复了 {fixed_count} 个重复记录")
    else:
        print(f"\n📋 预览完成！发现 {sum(len(ids_str.split(',')) - 1 for _, _, ids_str in duplicates)} 个需要修复的记录")
        print("运行 'python fix_duplicate_codes.py --fix' 来执行实际修复")

def main():
    import argparse
    parser = argparse.ArgumentParser(description='修复数据库中重复的样本代码')
    parser.add_argument('--fix', action='store_true', help='执行实际修复（默认为预览模式）')
    args = parser.parse_args()
    
    print("🔍 样本代码重复检查和修复工具")
    print("=" * 50)
    
    if args.fix:
        confirm = input("⚠️  确定要执行修复操作吗？这将修改数据库中的数据。(y/N): ")
        if confirm.lower() != 'y':
            print("❌ 操作已取消")
            return
        fix_duplicate_codes(dry_run=False)
    else:
        fix_duplicate_codes(dry_run=True)

if __name__ == '__main__':
    main()
