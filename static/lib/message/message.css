        body
        {
            background-color: #ddd;
        }
        .sweetalert_content
        {
            background-color: white;
            font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            width: 478px;
            padding: 17px;
            border-radius: 5px;
            text-align: center;
            position: fixed;
            left: 50%;
            top: 50%;
            margin-left: -256px;
            margin-top: -200px;
            overflow: hidden;
            z-index: 99999;
        }
        .sweetalert_content h2
        {
            color: #575757;
            font-size: 30px;
            text-align: center;
            font-weight: 600;
            text-transform: none;
            position: relative;
            margin: 25px 0;
            padding: 0;
            line-height: 40px;
        }
        .sweetalert_content p
        {
            color: #797979;
            font-size: 16px;
            text-align: center;
            font-weight: 300;
            position: relative;
            text-align: inherit;
            float: none;
            margin: 0;
            padding: 0;
            line-height: normal;
        }
        .sweetalert_content .confirm
        {
            background-color: #414959;
            color: white;
            border: none;
            box-shadow: none;
            font-size: 16px;
            font-weight: 500;
            -webkit-border-radius: 4px;
            border-radius: 5px;
            padding: 8px 30px;
            margin: 25px 5px 0 5px;
            cursor: pointer;
        }

        .sweetalert_content .confirm:focus
        {
            outline: none;
            box-shadow: 0 0 2px rgba(128, 179, 235, 0.5), inset 0 0 0 1px rgba(0, 0, 0, 0.05);
        }
        .sweetalert_content .confirm:hover
        {
            background-color: #a1d9f2;
        }
        .sweetalert_content .confirm:active
        {
            background-color: #81ccee;
        }
        .confirm
        {
            display: inline-block;
            background-color: rgb(21, 8, 37);
            box-shadow: 0px 0px 2px rgba(174, 222, 244, 0.8), 0px 0px 0px 1px rgba(0, 0, 0, 0.05) inset;
        }
          .cc
        {
            border-radius: 5px;
            padding: 12px 32px;
            color:#fff;
            -webkit-border-radius: 4px;
            border:1px solid rgb(236, 108, 98);
            display: inline-block; background-color: rgb(236, 108, 98); box-shadow: 0px 0px 2px rgba(236, 108, 98, 0.8), 0px 0px 0px 1px rgba(0, 0, 0, 0.05) inset;
        }
        .cc:hover
        {
            background-color:#e94f43;
        }
           .dd
        {
            margin: 26px 5px 0 5px;
            border-radius: 5px;
            padding: 12px 32px;
            color:#fff;
            -webkit-border-radius: 4px;
            border:1px solid #ddd;
            display: inline-block; background-color: #ddd; box-shadow: 0px 0px 2px #ddd, 0px 0px 0px 1px #ddd inset;
        }
        .dd:hover
        {
            background-color:#b7b6b6;
        }
