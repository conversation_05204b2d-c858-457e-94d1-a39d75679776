let mLabels = [];
let mLastUsedLabelIndex = -1; // 记录最后使用的标签索引

function f_setLabels(labels) {
	mLabels = labels;
	// 当标签列表更新时，重新初始化最后使用的标签索引
	mLastUsedLabelIndex = f_getLastUsedLabelIndex();
}

// 获取最后使用的标签索引
function f_getLastUsedLabelIndex() {
	try {
		const stored = localStorage.getItem('ggeclabel_last_used_label_index');
		if (stored !== null) {
			const index = parseInt(stored);
			// 验证索引是否有效
			if (index >= 0 && index < mLabels.length) {
				return index;
			}
		}
	} catch (e) {
		console.log('Error reading last used label index:', e);
	}
	return -1; // 返回-1表示没有有效的最后使用标签
}

// 设置最后使用的标签索引
function f_setLastUsedLabelIndex(index) {
	try {
		if (index >= 0 && index < mLabels.length) {
			mLastUsedLabelIndex = index;
			localStorage.setItem('ggeclabel_last_used_label_index', index.toString());
		}
	} catch (e) {
		console.log('Error saving last used label index:', e);
	}
}

// 获取默认标签索引（优先使用最后使用的标签，否则使用第一个标签）
function f_getDefaultLabelIndex() {
	if (!mLabels || mLabels.length === 0) {
		return -1; // 没有可用标签
	}

	// 首先尝试获取最后使用的标签索引
	const lastUsedIndex = f_getLastUsedLabelIndex();
	if (lastUsedIndex >= 0 && lastUsedIndex < mLabels.length) {
		return lastUsedIndex;
	}

	// 如果没有有效的最后使用标签，使用第一个标签
	return 0;
}
class LabelImage {
	constructor(options) {
		// 画布宽度
		this.cWidth = options.canvas.clientWidth;
		// 画布高度
		this.cHeight = options.canvas.clientHeight;
		// 缩略图宽度
		this.sWidth = 0;
		// 缩略图高度
		this.sHeight = 0;
		// 图片宽度
		this.iWidth = 0;
		// 图片高度
		this.iHeight = 0;
		// 图片拖拽至边缘最小显示
		this.appearSize = 180;
		// 缩放布进
		this.scaleStep = 0.02;
		// 最小缩放比例
		this.minScale = 0.2;
		// 最大缩放比例
		this.maxScale = 8;
		// 图片在画板中的横坐标
		this.x = 0;
		// 图片在画板中的纵坐标
		this.y = 0;
		// 鼠标当前画板中的横坐标
		this.mouseX = 0;
		// 鼠标当前画板中的纵坐标
		this.mouseY = 0;
		// 拖动过程中，鼠标前一次移动的横坐标
		this.prevX = 0;
		// 拖动过程中，鼠标前一次移动的纵坐标
		this.prevY = 0;
		// 缩放比例
		this.scale = 0;
		// 鼠标在图片中的横坐标
		this.ix = 0;
		// 鼠标在图片中的纵坐标
		this.iy = 0;
		// 矩形框起点横坐标
		this.rectX = 0;
		// 矩形框起点纵坐标
		this.rectY = 0;
		// 绘制多边形的圆点半径
		this.radius = 6;

		// 绘制线段宽度
		this.lineWidth = 1;

		//绘制区域模块透明度
		this.opacity = 0.45;

		// 定时器
		this.timer = null;

		// 结果是否被修改
		this.isModify = false;

		// 是否全屏
		this.isFullScreen = false;

		// 是否移动图像标注圆点
		this.isDragCircle = false;

		// 当前点击圆点index
		this.snapCircleIndex = 0;

		// 用于在拖拽或者缩放时，让绘制至存储面板的数据，只绘制一次
		this.drawFlag = true;

		// 监听滚动条缩放是否结束的定时器
		this.mousewheelTimer = null;


		this.Arrays = {

			// 图片标注展示数据集
			imageAnnotateShower: [],

			// 图片标注存储数据集
			imageAnnotateMemory: [],

			// 标注集操作 result list index
			resultIndex: 0,

		};
		this.Nodes = {
			// 图片节点
			image: null,
			// 画布节点
			canvas: options.canvas,
			// 缩略图节点
			scaleCanvas: options.scaleCanvas,
			// 缩放比例面板
			scalePanel: options.scalePanel,
			// 画布上下文
			ctx: options.canvas.getContext('2d'),
			// 缩略图画板上下文
			sCtx: null,
			// 缩略图方框
			scaleRect: null,
			// 存储图像数据的画布
			bCanvas: null,
			// 存储图像数据的上下文
			bCtx: null,
			// 绘图部分主外层函数
			canvasMain: options.canvasMain,
			// 标记结果数据集
			resultGroup: options.resultGroup,
			// 十字线开关按钮
			crossLine: options.crossLine,
			// 标注结果开关按钮
			labelShower: options.labelShower,
			// 屏幕快照按钮
			screenShot: options.screenShot,
			// 颜色选取器节点数据
			colorHex: options.colorHex,
			// 清空标注内容
			// clearScreen: options.clearScreen,
			// 撤销，返回上一步
			// returnUp: options.returnUp,
			// 标签管理
			toolTagsManager: options.toolTagsManager
		};
		this.Features = {
			// 拖动开关
			dragOn: true,
			// 矩形标注开关
			rectOn: false,
			// 多边形标注开关
			polygonOn: false,
			// 标签管理工具
			tagsOn: false,
			// 十字线开关
			crossOn: false,
			// 标注结果显示
			labelOn: true,
		};
		this.Initial();
	}

	//----初始化节点参数，绑定各自事件
	Initial = () => {
		let _nodes = this.Nodes;
		_nodes.scaleRect = document.createElement('div');
		_nodes.scaleRect.className = "scaleWindow";
		Object.assign(_nodes.scaleRect.style, {position: "absolute", border: "1px solid red", boxSizing: "border-box"});
		_nodes.scaleCanvas.appendChild(_nodes.scaleRect);
		_nodes.canvas.addEventListener('mousedown', this.CanvasMouseDown);
		_nodes.canvas.addEventListener('mousewheel', this.MouseWheel);
		_nodes.canvas.addEventListener("DOMMouseScroll", this.MouseWheel); // 兼容Firefox 滚动条事件

		// 设置canvas的实例引用，用于右键菜单
		_nodes.canvas.labelImageInstance = this;
		_nodes.canvas.addEventListener('contextmenu', LabelImage.NoRightMenu.bind(this));

		_nodes.scaleCanvas.addEventListener('click', this.ScaleCanvasClick);
		_nodes.crossLine.addEventListener('click', this.CrossHairSwitch);
		_nodes.labelShower.addEventListener('click', this.IsShowLabels);
		_nodes.screenShot.addEventListener('click', this.ScreenShot);
		_nodes.canvas.addEventListener('mousemove', this.CanvasMouseMove);
		_nodes.resultGroup.addEventListener('mouseover', this.ResultListOperation);
		_nodes.toolTagsManager.addEventListener('click', this.ManageLabels);

		// 创建右键标签选择菜单
		this.CreateRightClickMenu();
	};

	//----设置图片并初始化画板信息
	// tip: point
	SetImage = (src, memory=false) => {
		let _nodes = this.Nodes;
		_nodes.image = new Image();
		_nodes.image.crossOrigin = 'anonymous';
		_nodes.image.src = src;
		//监听图片加载
		_nodes.image.addEventListener('load', () => {

			this.iWidth = _nodes.image.width;
			this.iHeight = _nodes.image.height;
			//获取原有节点
			let beforeCanvas = _nodes.scaleCanvas.querySelectorAll('canvas');
			let bodyCanvas = document.querySelector(".bodyCanvas");

			//删除原有节点
			if (beforeCanvas.length > 0) {
				_nodes.scaleCanvas.removeChild(beforeCanvas[0]);
			}
			if (bodyCanvas) {
				document.body.removeChild(bodyCanvas)
			}

			//初始化上一张图片标注数据
			for (let i=this.Nodes.resultGroup.children.length-1; i>=0; i--) {
				this.Nodes.resultGroup.removeChild(this.Nodes.resultGroup.children[i]);
			}

			document.querySelector('.resultLength').innerHTML = "0";
			this.Arrays.imageAnnotateShower.splice(0, this.Arrays.imageAnnotateShower.length);
			this.Arrays.imageAnnotateMemory.splice(0, this.Arrays.imageAnnotateMemory.length);

			//创建缩略图画板
			let sCanvas = document.createElement('canvas');
			_nodes.sCtx = sCanvas.getContext('2d');
			sCanvas.style.display = "block";
			this.sWidth = parseInt(_nodes.scaleCanvas.getBoundingClientRect().width);
			this.sHeight = parseInt(this.sWidth * this.iHeight / this.iWidth);
			sCanvas.width = this.sWidth;
			sCanvas.height = this.sHeight;
			_nodes.scaleCanvas.appendChild(sCanvas);

			// 创建数据存储面板
			_nodes.bCanvas = document.createElement('canvas');
			_nodes.bCanvas.width = this.iWidth;
			_nodes.bCanvas.height = this.iHeight;
			_nodes.bCanvas.style.display = "none";
			_nodes.bCanvas.className = "bodyCanvas";
			_nodes.bCtx = _nodes.bCanvas.getContext('2d');
			_nodes.bCtx.drawImage(_nodes.image, 0, 0, this.iWidth, this.iHeight);
			_nodes.bCtx.translate(0.5, 0.5);
			document.body.appendChild(_nodes.bCanvas);

			this.scale = 1;

			// 图片初始定位
			// 初始化自适应缩放图片并居中
			if (this.iWidth > this.cWidth || this.iHeight > this.cHeight) {
				this.scale = this.iWidth - this.cWidth > this.iHeight - this.cHeight ? this.cWidth / this.iWidth : this.cHeight / this.iHeight;
			}
			let initImgX = (this.cWidth - this.iWidth * this.scale) / 2;
			let initImgY = (this.cHeight - this.iHeight * this.scale) / 2;
			this.SetXY(initImgX, initImgY);


			if (memory) {
				this.Arrays.imageAnnotateMemory = memory;
				this.ReplaceAnnotateShow();
				this.RepaintResultList();

				this.Arrays.imageAnnotateMemory.forEach((memory,index) => {
					//this.RecordOperation('add', '绘制', index, JSON.stringify(memory));
				});

			}
		});
	};

	//----设置功能参数
	SetFeatures = (f, value) => {
		if (f === "crossOn" || f === "labelOn") {
			this.Features[f] = value;
		}
		else {
			for (let key in this.Features) {
				if (key !== "crossOn" && key !== "labelOn") {
					this.Features[key] = false;
				}
			}
		}
		this.Features[f] = value;


		// 清空标注结果列表中classList
		let resultList = this.Nodes.resultGroup.getElementsByClassName("result_list");
		for (let i=0; i<resultList.length; i++) {
			resultList[i].classList.remove("active");
		}
		this.Arrays.resultIndex = 0;
		this.DrawSavedAnnotateInfoToShow(this.Arrays.resultIndex);
	};

	//----更新画板数据, 将存储面板数据绘制到展示面板已经缩略图面板
	UpdateCanvas = () => {
		let _nodes = this.Nodes;
		_nodes.ctx.clearRect(0, 0, this.cWidth, this.cHeight);
		_nodes.sCtx.clearRect(0, 0, this.sWidth, this.sWidth * this.iHeight / this.iHeight);

		_nodes.ctx.drawImage(_nodes.bCanvas, -this.x/this.scale, -this.y/this.scale, this.cWidth/this.scale, this.cHeight/this.scale, 0, 0, this.cWidth, this.cHeight);
		_nodes.sCtx.drawImage(_nodes.bCanvas, 0, 0, this.iWidth, this.iHeight, 0, 0, this.sWidth, this.sHeight);

		// 将缩略图方框区域绘制到画布
		let width = this.sWidth * this.cWidth / this.iWidth / this.scale;
		let height = width * this.cHeight / this.cWidth;
		let left = -this.x * this.sWidth / (this.iWidth * this.scale);
		let top = -this.y * this.sWidth / (this.iWidth * this.scale);
		// 将方框宽度固定在缩略图面板中
		if (width + left >= this.sWidth) {
			width = this.sWidth - left;
			left = this.sWidth - width;
			if (width >= this.sWidth) {
				width = this.sWidth;
				left = 0;
			}
		}
		else if (left <= 0) {
			width += left;
			left = 0;
		}

		// 将方框高度固定在缩略图面板中
		if (height + top >= this.sHeight) {
			height = this.sHeight - top;
			top = this.sHeight - height;
			if (height >= this.sHeight) {
				height = this.sHeight;
				top = 0;
			}
		}
		else if (top <= 0){
			height += top;
			top = 0;
		}

		_nodes.scaleRect.style.left = left + "px";
		_nodes.scaleRect.style.top = top + "px";
		if (width !== Number(_nodes.scaleRect.style.width)) {
			_nodes.scaleRect.style.width = width + "px";
			_nodes.scaleRect.style.height = height + "px";
		}

		_nodes.scalePanel.innerText = (this.scale * 100).toFixed(2) + "%";
	};

	//----画板跟随鼠标十字线绘制函数
	MouseMoveCrossHair = () => {
		let _nodes = this.Nodes;
		_nodes.ctx.setLineDash([6, 3]);
		_nodes.ctx.lineWidth = 1;
		//_nodes.ctx.strokeStyle = "#333";
		_nodes.ctx.strokeStyle = "#f10505";
		_nodes.ctx.beginPath();
		// 横线
		_nodes.ctx.moveTo(0, this.mouseY);
		_nodes.ctx.lineTo(this.cWidth, this.mouseY);
		_nodes.ctx.stroke();
		// 纵线
		_nodes.ctx.moveTo(this.mouseX, 0);
		_nodes.ctx.lineTo(this.mouseX, this.cHeight);
		_nodes.ctx.stroke();
		_nodes.ctx.closePath();
	};

	//----鼠标跟随十字线开关按钮操作函数
	CrossHairSwitch = () => {
		let _nodes = this.Nodes;
		if (_nodes.crossLine.className.indexOf('focus') > -1) {
			_nodes.crossLine.childNodes[1].checked = false;
			_nodes.crossLine.classList.remove('focus');
			this.SetFeatures('crossOn', false);
			_nodes.canvas.removeEventListener('mousemove', this.MouseMoveCrossHairLocation);
		}
		else {
			_nodes.crossLine.childNodes[1].checked = true;
			_nodes.crossLine.classList.add('focus');
			this.SetFeatures('crossOn', true);
			_nodes.canvas.addEventListener('mousemove', this.MouseMoveCrossHairLocation);
		}
	};

	//----鼠标移动十字线定位函数
	MouseMoveCrossHairLocation = () => {
		// 更新鼠标当前位置十字线
		if (this.Features.crossOn) {
			this.DrawSavedAnnotateInfoToShow();
			this.MouseMoveCrossHair();
		}
	};

	//----监听画板鼠标移动
	CanvasMouseMove = (e) => {
		// console.log("CanvasMouseMove()");


		let _nodes = this.Nodes;
		let _arrays = this.Arrays;
		this.GetMouseInCanvasLocation(e);
		if (_arrays.resultIndex !== 0) {
			if(_arrays.imageAnnotateShower.length > _arrays.resultIndex){
				let __d = _arrays.imageAnnotateShower[_arrays.resultIndex-1]
				let imageIndexShow = __d.content;
				if (imageIndexShow.length > 0) {
					for (let i=0; i<imageIndexShow.length; i++) {
						// 使用勾股定理计算鼠标当前位置是否处于当前点上
						let distanceFromCenter = Math.sqrt(Math.pow(imageIndexShow[i].x - this.mouseX, 2) + Math.pow(imageIndexShow[i].y - this.mouseY, 2));
						// 改变圆点颜色动画
						if (distanceFromCenter <= this.radius){
							_nodes.canvas.style.cursor = "grabbing";
							return;
						}
						else {
							_nodes.canvas.style.cursor = "crosshair";
						}
					}
				}
			}

		}
	};

	//----监听画板鼠标点击
	CanvasMouseDown = (e) => {
		console.log("CanvasMouseDown()");

		let _nodes = this.Nodes;
		let _arrays = this.Arrays;
		this.GetMouseInCanvasLocation(e);
		if (e.button === 0) {
			this.isDragCircle = false;
			if (_arrays.resultIndex !== 0) {
				if(_arrays.imageAnnotateShower.length > _arrays.resultIndex - 1) {
					let imageIndex = _arrays.imageAnnotateShower[_arrays.resultIndex - 1].content;
					if (imageIndex.length > 0) {
						for (let i = 0; i < imageIndex.length; i++) {
							// 使用勾股定理计算鼠标当前位置是否处于当前点上
							let distanceFromCenter = Math.sqrt(Math.pow(imageIndex[i].x - this.mouseX, 2) + Math.pow(imageIndex[i].y - this.mouseY, 2));
							if (distanceFromCenter <= this.radius) {
								this.isDragCircle = true;
								this.snapCircleIndex = i;
								if (_arrays.imageAnnotateShower[_arrays.resultIndex - 1].contentType === "rect") {
									this.Nodes.canvas.addEventListener('mousemove', this.DragRectCircleRepaintRect);
									this.Nodes.canvas.addEventListener('mouseup', this.RemoveDragRectCircle);
								} else if (_arrays.imageAnnotateShower[_arrays.resultIndex - 1].contentType === "polygon") {
									this.Nodes.canvas.addEventListener('mousemove', this.CircleDrag);
									this.Nodes.canvas.addEventListener('mouseup', this.RemoveCircleDrag);
								}
								return;
							} else {
								this.isDragCircle = false;
							}
						}
					}
				}
			}
			if (!this.isDragCircle){
				if (this.Features.dragOn) {
					// 是否开启拖拽模式
					let prevP = this.CalculateChange(e, _nodes.canvas);
					this.prevX = prevP.x;
					this.prevY = prevP.y;
					_nodes.canvas.addEventListener('mousemove', this.ImageDrag);
					_nodes.canvas.addEventListener('mouseup', this.RemoveImageDrag);
				}
				else if (this.Features.rectOn) {
					// 是否开启绘制矩形功能
					if (this.Arrays.resultIndex === 0) {
						_nodes.ctx.lineWidth = 1;
						_nodes.ctx.strokeStyle = "#ff0000";
						_nodes.ctx.fillStyle = "rgba(255,0,0,"+ this.opacity +")";
						this.rectX = this.mouseX;
						this.rectY = this.mouseY;
						this.Nodes.canvas.addEventListener('mousemove', this.MouseMoveDrawRect);
						this.Nodes.canvas.addEventListener('mouseup', this.MouseUpRemoveDrawRect);
					}
				}
				else if (this.Features.polygonOn) {
					// 是否开启绘制多边形功能
					let resultList = _nodes.resultGroup.getElementsByClassName("result_list");
					let isActive = false;
					for (let i=0; i<resultList.length; i++) {
						// 循环结果列表判断是否点击某一个结果，若是，则改变焦点
						if (resultList[i].className.indexOf("active") > -1) {
							_arrays.resultIndex = resultList[i].id;
							isActive = true;
						}
					}
					if (!isActive) {
						_arrays.resultIndex = 0;
					}
					if (_arrays.resultIndex === 0) {
						// 未选定标签结果，创建新标签
						this.CreateNewResultList(this.mouseX, this.mouseY, "polygon");
					}
					if (!this.isDragCircle) {
						let index = _arrays.resultIndex - 1;
						// 保存坐标点
						_arrays.imageAnnotateShower[index].content.push({x: this.mouseX, y: this.mouseY});
						this.CalcRectMask(_arrays.imageAnnotateShower[index].content);
						this.ReplaceAnnotateMemory();
						this.DrawSavedAnnotateInfoToShow();
					}
				}
			}
		}
		else if (e.button === 2) {
			// 长按右击直接开启拖拽模式
			let prevP = this.CalculateChange(e, _nodes.canvas);
			this.prevX = prevP.x;
			this.prevY = prevP.y;
			_nodes.canvas.addEventListener('mousemove', this.ImageDrag);
			_nodes.canvas.addEventListener('mouseup', this.RemoveImageDrag);
		}
	};

	//----通过已保存的坐标点计算矩形蒙层位置与大小，以及标签位置, 添加至数组列表中
	CalcRectMask = (arrays) => {
		if (arrays.length >= 2) {
			// 保存边缘矩形框坐标点
			let xMin = arrays[0].x,
				xMax = arrays[0].x,
				yMin = arrays[0].y,
				yMax = arrays[0].y
			;
			arrays.forEach((item) => {
				xMin = xMin < item.x ? xMin : item.x;
				xMax = xMax > item.x ? xMax : item.x;
				yMin = yMin < item.y ? yMin : item.y;
				yMax = yMax > item.y ? yMax : item.y;
			});
			this.Arrays.imageAnnotateShower[this.Arrays.resultIndex-1].rectMask = {
				"xMin": xMin,
				"yMin": yMin,
				"width": xMax - xMin,
				"height": yMax - yMin
			};
			// 计算已创建的标签居中显示
			let labelX = (xMax - xMin) / 2 + xMin;
			let labelY = (yMax - yMin) / 2 + yMin;
			this.Arrays.imageAnnotateShower[this.Arrays.resultIndex-1].labelLocation.x = labelX;
			this.Arrays.imageAnnotateShower[this.Arrays.resultIndex-1].labelLocation.y = labelY;
		}
	};

	//----绘制矩形的方法
	DrawRect = (ctx, x, y, width, height, color, rgb) => {
		ctx.lineWidth = 2;//xc-add

		ctx.strokeStyle = color;
		ctx.strokeRect(x, y, width, height);

		//ctx.fillStyle = "rgba("+ rgb +"," + this.opacity + ")"; //xc-remove
		//ctx.fillStyle = "transparent";//xc-add 设置画布填充为透明（目的是不填充）
		//ctx.fillRect(x, y, width, height);
	};

	//----绘制圆点的方法
	DrawCircle = (ctx, x, y, color) => {
		ctx.beginPath();
		ctx.fillStyle = "#000";
		ctx.arc(x, y, this.radius, 0, 2*Math.PI);
		ctx.fill();
		ctx.beginPath();
		ctx.fillStyle = color;
		ctx.arc(x, y, this.radius/3, 0, 2*Math.PI);
		ctx.fill();
	};

	//----绘制标签的方法
	DrawRectLabel = (ctx, x, y, color, name, index) => {
		ctx.font = "12px Verdana";
		let txtWidth = ctx.measureText(name).width;
		ctx.fillStyle = "rgba(255,255,255, 0.7)";
		ctx.fillRect(x - txtWidth/2 -8, y - 10, txtWidth + 16, 20);
		ctx.fillStyle = color;
		ctx.fillText(name, x - txtWidth/2, y + 4);
	};

	//----绘制已保存的标定信息（在数据操作更新时渲染）绘至数据展示画板
	DrawSavedAnnotateInfoToShow = (resultIndex) => {
		let _arrays = this.Arrays;
		let _nodes = this.Nodes;
		_nodes.ctx.clearRect(0, 0, this.cWidth, this.cHeight);
		_nodes.ctx.drawImage(_nodes.bCanvas, -this.x/this.scale, -this.y/this.scale, this.cWidth/this.scale, this.cHeight/this.scale, 0, 0, this.cWidth, this.cHeight);
		_nodes.ctx.setLineDash([0,0]);
		_arrays.imageAnnotateShower.forEach((item, index) => {
			if (item.contentType === "polygon") {
				// 绘制闭合线条
				_nodes.ctx.beginPath();
				_nodes.ctx.lineWidth = this.lineWidth;
				_nodes.ctx.moveTo(item.content[0].x, item.content[0].y);
				item.content.forEach((line) => {
					_nodes.ctx.lineTo(line.x, line.y);
				});
				_nodes.ctx.fillStyle = "rgba("+ item.labels.labelColorRGB +"," + this.opacity + ")";
				_nodes.ctx.fill();
				_nodes.ctx.strokeStyle = "rgba("+ item.labels.labelColorRGB +"," + this.opacity + ")";
				_nodes.ctx.stroke();
			}
			else if (item.contentType === "rect") {
				this.DrawRect(_nodes.ctx, item.rectMask.xMin, item.rectMask.yMin, item.rectMask.width, item.rectMask.height, item.labels.labelColor, item.labels.labelColorRGB);
			}
			if (_arrays.resultIndex !== 0 && _arrays.resultIndex-1 === index) {
				item.content.forEach((circle) => {
					// 绘制圆点
					this.DrawCircle(_nodes.ctx, circle.x, circle.y, "#20c3f9");
				});
			}
			if (item.content.length >= 2 && item.labels.visibility) {
				// 绘制标签
				this.DrawRectLabel(_nodes.ctx, item.labelLocation.x, item.labelLocation.y, item.labels.labelColor, item.labels.labelName, index + 1);
			}
			// 绘制矩形蒙层
			if (resultIndex && resultIndex-1 === index) {
				_nodes.ctx.beginPath();
				_nodes.ctx.lineWidth = 2;
				_nodes.ctx.strokeStyle = "#fffd4d";
				_nodes.ctx.fillStyle = "rgba(255, 253, 77, 0.3)";
				_nodes.ctx.strokeRect(item.rectMask.xMin, item.rectMask.yMin, item.rectMask.width, item.rectMask.height);
				_nodes.ctx.fillRect(item.rectMask.xMin, item.rectMask.yMin, item.rectMask.width, item.rectMask.height);
				_nodes.ctx.closePath();
			}
		});
	};

	//----绘制已保存的标定信息（只在拖拽和缩放画布时渲染）绘画至数据存储面板
	DrawSavedAnnotateInfoToMemory = (isRender) => {
		let _arrays = this.Arrays;
		let _nodes = this.Nodes;
		_nodes.bCtx.clearRect(0, 0, this.iWidth, this.iHeight);
		_nodes.bCtx.drawImage(_nodes.image, 0, 0, this.iWidth, this.iHeight);
		if (isRender) {
			_arrays.imageAnnotateMemory.forEach( (item, index) => {
				if (item.contentType === "polygon") {
					// 绘制闭合线条
					_nodes.bCtx.beginPath();
					_nodes.bCtx.lineWidth = this.lineWidth;
					_nodes.bCtx.moveTo(item.content[0].x, item.content[0].y);
					item.content.forEach((line) => {
						_nodes.bCtx.lineTo(line.x, line.y);
					});
					_nodes.bCtx.fillStyle = "rgba("+ item.labels.labelColorRGB +"," + this.opacity + ")";
					_nodes.bCtx.fill();
					_nodes.bCtx.strokeStyle = "rgba("+ item.labels.labelColorRGB +"," + this.opacity + ")";
					_nodes.bCtx.stroke();
				}
				else if (item.contentType === "rect") {
					this.DrawRect(_nodes.bCtx, item.rectMask.xMin, item.rectMask.yMin, item.rectMask.width, item.rectMask.height, item.labels.labelColor, item.labels.labelColorRGB);
				}
				if (_arrays.resultIndex !== 0 && _arrays.resultIndex-1 === index) {
					item.content.forEach((circle) => {
						// 绘制圆点
						this.DrawCircle(_nodes.bCtx, circle.x, circle.y, "#20c3f9");
					});
				}
				if (item.content.length >= 2 && item.labels.visibility) {
					// 绘制标签
					this.DrawRectLabel(_nodes.bCtx, item.labelLocation.x, item.labelLocation.y, item.labels.labelColor, item.labels.labelName, index + 1);
				}
			});
		}
		this.UpdateCanvas();
		!isRender && this.DrawSavedAnnotateInfoToShow();

	};

	//----圆点拖拽事件，并且重新绘制边缘轨迹点
	CircleDrag = (e) => {
		this.GetMouseInCanvasLocation(e);
		let imageIndex = this.Arrays.imageAnnotateShower[this.Arrays.resultIndex-1].content;
		imageIndex[this.snapCircleIndex].x = this.mouseX;
		imageIndex[this.snapCircleIndex].y = this.mouseY;
		this.DrawSavedAnnotateInfoToShow();
	};

	//----移除圆点拖拽事件, 并重新绘制一遍最新状态
	RemoveCircleDrag = () => {
		let index = this.Arrays.resultIndex - 1;
		this.Nodes.canvas.removeEventListener('mousemove', this.CircleDrag);
		this.Nodes.canvas.removeEventListener('mouseup', this.RemoveCircleDrag);
		// 移除圆点拖拽事件之后，改变被拖拽圆点在矩形蒙层数据中的坐标
		this.CalcRectMask(this.Arrays.imageAnnotateShower[index].content);
		this.DrawSavedAnnotateInfoToShow();
		this.ReplaceAnnotateMemory();

	};

	//----图片拖拽事件函数
	ImageDrag = (e) => {
		let _nodes = this.Nodes;
		let p = this.CalculateChange(e, _nodes.canvas);
		let offsetX = (p.x - this.prevX);
		let offsetY = (p.y - this.prevY);
		this.SetXY(this.x + offsetX, this.y + offsetY);
		this.prevX = p.x;
		this.prevY = p.y;
		if (this.drawFlag) {
			this.DrawSavedAnnotateInfoToMemory(true);
			this.drawFlag = false;
		}
	};

	//----移除鼠标拖拽图片事件函数, 并将最新数据绘制到存储面板中
	RemoveImageDrag = () => {
		this.ReplaceAnnotateShow();
		this.DrawSavedAnnotateInfoToMemory(false);
		this.drawFlag = true;
		this.Nodes.canvas.removeEventListener('mousemove', this.ImageDrag);
		this.Nodes.canvas.removeEventListener('mouseup', this.RemoveImageDrag);
	};

	//----鼠标移动绘制矩形事件
	MouseMoveDrawRect = (e) => {
		console.log("MouseMoveDrawRect()");

		this.GetMouseInCanvasLocation(e);
		this.DrawSavedAnnotateInfoToShow();
		//this.Nodes.ctx.strokeStyle = "#ff0000";
		//this.Nodes.ctx.fillStyle = "rgba(255,0,0,"+ this.opacity +")";
		//xc-add-start
		this.Nodes.ctx.strokeStyle = "#00ff00";
		this.Nodes.ctx.fillStyle = "rgba(0,255,0,"+ this.opacity +")";
		//xc-add-end

		this.Nodes.ctx.strokeRect(this.rectX, this.rectY, this.mouseX-this.rectX, this.mouseY-this.rectY);
		this.Nodes.ctx.fillRect(this.rectX, this.rectY, this.mouseX-this.rectX, this.mouseY-this.rectY);
	};

	//----绘制矩形时鼠标抬起后移除监听函数
	MouseUpRemoveDrawRect = () => {
		console.log("[ok] MouseUpRemoveDrawRect()");

		if (this.mouseX - this.rectX >= 5 || this.rectX - this.mouseX >= 5) {  // 判断矩形绘制距离大于五才认定为有效绘制
			// 保存绘图数据，检查是否需要自动分配标签
			let autoAssign = this.autoAssignNextLabel || false;
			this.CreateNewResultList(this.mouseX, this.mouseY, "rect", autoAssign);
			this.DrawSavedAnnotateInfoToShow();
			this.ReplaceAnnotateMemory();

			// 重置自动分配标签标记
			this.autoAssignNextLabel = false;
		}

		this.Nodes.canvas.removeEventListener('mousemove', this.MouseMoveDrawRect);
		this.Nodes.canvas.removeEventListener('mouseup', this.MouseUpRemoveDrawRect);

		//xc-add-start 20250209
		 mLabelImage.SetFeatures('rectOn', true);
		//xc-add-end 20250209

	};

	//----拖拽矩形圆点时改变矩形十个点坐标
	DragRectCircleChangeLocation = (content, circleIndex) => {
		switch (circleIndex) {
			case 0:
				content[1].y = this.mouseY;
				content[3].x = this.mouseX;
				break;
			case 1:
				content[0].y = this.mouseY;
				content[2].x = this.mouseX;
				break;
			case 2:
				content[1].x = this.mouseX;
				content[3].y = this.mouseY;
				break;
			case 3:
				content[0].x = this.mouseX;
				content[2].y = this.mouseY;
				break;
			default:
				break;
		}
	};

	//----拖拽矩形圆点时重新绘制矩形事件
	DragRectCircleRepaintRect = (e) => {
		this.GetMouseInCanvasLocation(e);
		let imageIndex = this.Arrays.imageAnnotateShower[this.Arrays.resultIndex-1].content;
		this.Nodes.ctx.fillStyle = "rgba("+ this.Arrays.imageAnnotateShower[this.Arrays.resultIndex-1].labels.labelColorRGB +","+ this.opacity +")";
		imageIndex[this.snapCircleIndex].x = this.mouseX;
		imageIndex[this.snapCircleIndex].y = this.mouseY;
		this.DragRectCircleChangeLocation(imageIndex, this.snapCircleIndex);
		this.CalcRectMask(imageIndex);
		this.DrawSavedAnnotateInfoToShow();
	};

	//----移除矩形圆点拖拽事件，并将最新数据绘制到存储面板中
	RemoveDragRectCircle = () => {
		this.ReplaceAnnotateMemory();
		this.DrawSavedAnnotateInfoToMemory(false);
		this.drawFlag = true;
		this.Nodes.canvas.removeEventListener('mousemove', this.DragRectCircleRepaintRect);
		this.Nodes.canvas.removeEventListener('mouseup', this.RemoveDragRectCircle);

	};

	//----重新绘制已保存的图像标注记录与标签（删除修改之后重新渲染整体模块）
	RepaintResultList = () => {
		console.log("RepaintResultList()");
		// 先清空标签, 之后再重新渲染
		this.Arrays.resultIndex = 0;
		this.DrawSavedAnnotateInfoToShow();
		this.ReplaceAnnotateMemory();
		this.Nodes.resultGroup.innerHTML = "";
		if (this.Arrays.imageAnnotateShower.length > 0) {
			let _index = 0;
			this.Arrays.imageAnnotateShower.forEach((item, index) => {
				// 创建结果标签
				_index = ++index;
				let eyeIconClass = item.labels.visibility ? "icon-eye-open" : "icon-eye-close";
				let resultListBody = document.createElement('div');
				resultListBody.className = "result_list";
				resultListBody.id = _index;
				resultListBody.innerHTML = '<span class="result_no">'+ _index +'</span>' +
					'<span class="result_color" style="background: '+ item.labels.labelColor +';"></span>' +
					'<input class="result_Name" value="'+ item.labels.labelName +'" disabled>' +
					'<i class="editLabelName icon-pencil"></i>' +
					'<i class="deleteLabel icon-trash"></i>' +
					'<i class="isShowLabel '+ eyeIconClass +'"></i>';
				this.Nodes.resultGroup.appendChild(resultListBody);
			});
			document.querySelector('.resultLength').innerHTML = _index;
		}
	};

	//----创建新的标定结果标签
	CreateNewResultList = (lx, ly, contentType, autoAssignLabel = false) => {
		let _nodes = this.Nodes;
		let _arrays = this.Arrays;
		let eyeIconClass = _nodes.labelShower.children[0].checked ? "icon-eye-open" : "icon-eye-close";
		let resultLength = document.querySelectorAll('.result_list').length + 1;
		let resultListBody = document.createElement('div');
		resultListBody.className = "result_list active";
		resultListBody.id = resultLength;

		// 确定标签信息
		let labelInfo = {
			labelName: "未命名",
			labelColor: "red",
			labelColorRGB: "255,0,0"
		};

		// 如果需要自动分配标签且有可用标签
		if (autoAssignLabel && mLabels && mLabels.length > 0) {
			let selectedLabelIndex;

			// 优先使用右键选择的标签
			if (this.selectedLabelIndex !== undefined && this.selectedLabelIndex >= 0 && this.selectedLabelIndex < mLabels.length) {
				selectedLabelIndex = this.selectedLabelIndex;
				// 清除选择的标签索引，避免影响后续操作
				this.selectedLabelIndex = undefined;
			} else {
				// 使用默认标签逻辑：优先使用最后使用的标签，否则使用第一个标签
				selectedLabelIndex = f_getDefaultLabelIndex();
			}

			if (selectedLabelIndex >= 0) {
				let selectedLabel = mLabels[selectedLabelIndex];
				labelInfo = {
					labelName: selectedLabel.labelName,
					labelColor: selectedLabel.labelColor,
					labelColorRGB: selectedLabel.labelColorR + "," + selectedLabel.labelColorG + "," + selectedLabel.labelColorB
				};
				// 记录这次使用的标签索引
				f_setLastUsedLabelIndex(selectedLabelIndex);
			}
		}

		resultListBody.innerHTML = '<span class="result_no">'+ resultLength +'</span>' +
			'<span class="result_color"></span>' +
			'<input class="result_Name" value="' + labelInfo.labelName + '" disabled>' +
			'<i class="editLabelName icon-pencil"></i>' +
			'<i class="deleteLabel icon-trash"></i>' +
			'<i class="isShowLabel '+ eyeIconClass +'"></i>';
		_nodes.resultGroup.appendChild(resultListBody);

		// 轮询获取当前ResultIndex;
		let resultList = _nodes.resultGroup.getElementsByClassName("result_list");
		for (let i=0; i<resultList.length; i++) {
			if (resultList[i].className.indexOf("active") > -1) {
				_arrays.resultIndex = resultList[i].id;
			}
		}

		if (contentType === "rect") {
			let rectMask = {
				xMin: this.rectX,
				yMin: this.rectY,
				width: this.mouseX - this.rectX,
				height: this.mouseY - this.rectY
			};
			this.Arrays.imageAnnotateShower.push({
				content: [
					{x: this.rectX, y: this.rectY},
					{x: this.mouseX, y: this.rectY},
					{x: this.mouseX, y: this.mouseY},
					{x: this.rectX, y: this.mouseY},
				],
				labels: {
					labelName: labelInfo.labelName,
					labelColor: labelInfo.labelColor,
					labelColorRGB: labelInfo.labelColorRGB,
					visibility: _nodes.labelShower.children[0].checked,
				},
				labelLocation: this.ComputerLabelLocation(rectMask),
				rectMask,
				contentType: contentType,
			});
			this.ReplaceAnnotateMemory();
		}
		else if (contentType === "polygon") {
			this.Arrays.imageAnnotateShower.push(
				{
					"labels":{
						labelName: labelInfo.labelName,
						labelColor: labelInfo.labelColor,
						labelColorRGB: labelInfo.labelColorRGB,
						visibility: _nodes.labelShower.children[0].checked,
					},
					"labelLocation": {
						x: lx,
						y: ly
					},
					"contentType": contentType,
					"content": [],
					"rectMask": {},
				}
			);
		}
		document.querySelector('.resultLength').innerHTML = resultLength;
	};

	//----通过快捷键创建新标注（自动分配默认标签）
	CreateNewAnnotationWithDefaultLabel = () => {
		// 检查是否有可用标签
		if (!mLabels || mLabels.length === 0) {
			return false; // 没有可用标签
		}

		// 设置为矩形绘制模式
		this.SetFeatures('rectOn', true);

		// 设置标记，下次创建矩形时自动分配标签
		this.autoAssignNextLabel = true;

		return true; // 成功设置
	};

	//----创建右键标签选择菜单
	CreateRightClickMenu = () => {
		// 创建右键菜单容器
		let rightClickMenu = document.createElement('div');
		rightClickMenu.className = 'right-click-label-menu';
		rightClickMenu.style.cssText = `
			position: fixed;
			background: white;
			border: 1px solid #ccc;
			border-radius: 4px;
			box-shadow: 0 2px 8px rgba(0,0,0,0.15);
			padding: 4px 0;
			z-index: 9999;
			display: none;
			min-width: 120px;
			max-height: 300px;
			overflow-y: auto;
		`;

		// 添加到body
		document.body.appendChild(rightClickMenu);
		this.rightClickMenu = rightClickMenu;

		// 点击其他地方隐藏菜单
		document.addEventListener('click', (e) => {
			if (!rightClickMenu.contains(e.target)) {
				rightClickMenu.classList.remove('show');
				rightClickMenu.style.display = 'none';
			}
		});
	};

	//----显示右键标签选择菜单
	ShowRightClickLabelMenu = (event) => {
		// 如果没有标签，不显示菜单
		if (!mLabels || mLabels.length === 0) {
			return;
		}

		// 获取鼠标位置
		this.GetMouseInCanvasLocation(event);

		// 检查是否点击在某个标注框上
		let clickedAnnotationIndex = this.GetClickedAnnotationIndex(this.mouseX, this.mouseY);

		// 如果没有点击在标注框上，不显示菜单
		if (clickedAnnotationIndex === -1) {
			return;
		}

		// 记录要修改标签的标注索引
		this.rightClickAnnotationIndex = clickedAnnotationIndex;

		// 清空菜单内容
		this.rightClickMenu.innerHTML = '';

		// 创建菜单项
		mLabels.forEach((label, index) => {
			let menuItem = document.createElement('div');
			menuItem.className = 'right-click-menu-item';
			menuItem.style.cssText = `
				padding: 8px 16px;
				cursor: pointer;
				border-left: 3px solid ${label.labelColor};
				color: #333;
				font-size: 14px;
				transition: background-color 0.2s;
			`;
			menuItem.textContent = label.labelName;

			// 如果是当前标注的标签，高亮显示
			const currentAnnotation = this.Arrays.imageAnnotateShower[clickedAnnotationIndex];
			if (currentAnnotation && currentAnnotation.labels.labelName === label.labelName) {
				menuItem.style.backgroundColor = '#e3f2fd';
				menuItem.style.fontWeight = 'bold';
			}

			// 鼠标悬停效果
			menuItem.addEventListener('mouseenter', () => {
				menuItem.style.backgroundColor = '#f5f5f5';
			});
			menuItem.addEventListener('mouseleave', () => {
				if (currentAnnotation && currentAnnotation.labels.labelName === label.labelName) {
					menuItem.style.backgroundColor = '#e3f2fd';
				} else {
					menuItem.style.backgroundColor = 'transparent';
				}
			});

			// 点击选择标签
			menuItem.addEventListener('click', () => {
				this.ApplyLabelToAnnotation(index, clickedAnnotationIndex);
				this.rightClickMenu.classList.remove('show');
				this.rightClickMenu.style.display = 'none';
			});

			this.rightClickMenu.appendChild(menuItem);
		});

		// 显示菜单
		this.rightClickMenu.style.display = 'block';
		this.rightClickMenu.style.left = event.clientX + 'px';
		this.rightClickMenu.style.top = event.clientY + 'px';

		// 添加动画效果
		this.rightClickMenu.classList.add('show');

		// 确保菜单不超出屏幕边界
		const rect = this.rightClickMenu.getBoundingClientRect();
		if (rect.right > window.innerWidth) {
			this.rightClickMenu.style.left = (event.clientX - rect.width) + 'px';
		}
		if (rect.bottom > window.innerHeight) {
			this.rightClickMenu.style.top = (event.clientY - rect.height) + 'px';
		}
	};

	//----检测点击位置是否在某个标注框上
	GetClickedAnnotationIndex = (mouseX, mouseY) => {
		for (let i = this.Arrays.imageAnnotateShower.length - 1; i >= 0; i--) {
			const annotation = this.Arrays.imageAnnotateShower[i];
			if (annotation.contentType === "rect" && annotation.rectMask) {
				const rect = annotation.rectMask;
				// 检查点击是否在矩形范围内
				if (mouseX >= rect.xMin && mouseX <= rect.xMin + rect.width &&
					mouseY >= rect.yMin && mouseY <= rect.yMin + rect.height) {
					return i;
				}
			} else if (annotation.contentType === "polygon" && annotation.content) {
				// 对于多边形，使用射线法检测点是否在多边形内
				if (this.IsPointInPolygon(mouseX, mouseY, annotation.content)) {
					return i;
				}
			}
		}
		return -1; // 没有点击在任何标注上
	};

	//----检测点是否在多边形内（射线法）
	IsPointInPolygon = (x, y, polygon) => {
		let inside = false;
		for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
			if (((polygon[i].y > y) !== (polygon[j].y > y)) &&
				(x < (polygon[j].x - polygon[i].x) * (y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
				inside = !inside;
			}
		}
		return inside;
	};

	//----应用标签到指定的标注
	ApplyLabelToAnnotation = (labelIndex, annotationIndex) => {
		if (labelIndex >= 0 && labelIndex < mLabels.length &&
			annotationIndex >= 0 && annotationIndex < this.Arrays.imageAnnotateShower.length) {

			const selectedLabel = mLabels[labelIndex];
			const annotation = this.Arrays.imageAnnotateShower[annotationIndex];

			// 更新标注的标签信息
			annotation.labels.labelName = selectedLabel.labelName;
			annotation.labels.labelColor = selectedLabel.labelColor;
			annotation.labels.labelColorRGB = selectedLabel.labelColorR + "," + selectedLabel.labelColorG + "," + selectedLabel.labelColorB;

			// 记录选择的标签为最后使用的标签
			f_setLastUsedLabelIndex(labelIndex);

			// 重新绘制和更新
			this.RepaintResultList();
			this.DrawSavedAnnotateInfoToShow();
			this.ReplaceAnnotateMemory();

			// 提示用户
			if (typeof myAlert === 'function') {
				myAlert(`已将标签"${selectedLabel.labelName}"应用到标注`, "success", 1500);
			} else {
				console.log(`已将标签"${selectedLabel.labelName}"应用到标注`);
			}
		}
	};

	//----删除某个已标定结果标签
	DeleteSomeResultLabel = (index) => {
		this.ReplaceAnnotateMemory();
		this.Arrays.imageAnnotateShower.splice(index, 1);
		this.RepaintResultList();
	};

	//----已标定结果列表交互操作
	ResultListOperation = () => {
		let _self = this;
		let resultList = this.Nodes.resultGroup.getElementsByClassName("result_list");
		for (let i=0; i<resultList.length; i++) {
			resultList[i].index = i;
			resultList[i].onmouseover = function() {
				let hoverIndex = resultList[this.index].id;
				_self.DrawSavedAnnotateInfoToShow(hoverIndex);
			};
			resultList[i].onmouseout = function() {
				_self.DrawSavedAnnotateInfoToShow();
			};
			resultList[i].onclick = function(event) {
				let target = event.target;
				let pageY = event.pageY - 35;
				switch (target.classList[0]) {
					case "deleteLabel":
						// 添加确认提示框
						if (confirm('确定要删除这个标注吗？删除后无法恢复！')) {
							_self.DeleteSomeResultLabel(i);
						}
						break;
					case "editLabelName":
						_self.getCreatedLabels(resultList[i], pageY, i);
						break;
					case "result_Name":
						for (let j=0; j<resultList.length; j++) {
							resultList[j].classList.remove('active');
						}
						resultList[this.index].classList.add('active');
						_self.Arrays.resultIndex = this.index + 1;
						_self.DrawSavedAnnotateInfoToShow();
						break;
					case "isShowLabel":
						if(target.classList.value.indexOf('icon-eye-open') > -1) {
							target.className = "isShowLabel icon-eye-close";
							_self.Arrays.imageAnnotateShower[this.index].labels.visibility = false;
						}
						else {
							target.className = "isShowLabel icon-eye-open";
							_self.Arrays.imageAnnotateShower[this.index].labels.visibility = true;
						}
						_self.DrawSavedAnnotateInfoToShow();
						break;
					default:
						break;
				}
			};
		}
	};

	//----获取已经创建的标签列表
	getCreatedLabels = (node, pageY, resultIndex) => {
		let _self = this;
		let resultSelectLabel = document.querySelector('.resultSelectLabel');
		let selectLabelUL = resultSelectLabel.querySelector('.selectLabel-ul');  //标签选择UL
		let closeLabel = resultSelectLabel.querySelector('.closeLabelManage');
		let selectLabelTip = resultSelectLabel.querySelector('.selectLabelTip');
		//加载标签数据
		selectLabelUL.innerHTML = "";
		let labels = mLabels;
		if (labels.length > 0) {
			selectLabelTip.style.display = "none";
			let fragment = document.createDocumentFragment();
			labels.forEach((item, index) => {
				let labelLi = document.createElement("li");
				labelLi.innerText = item.labelName;
				labelLi.value = item.labelColor;
				labelLi.setAttribute("data-index", index);
				labelLi.setAttribute("data-r", item.labelColorR);
				labelLi.setAttribute("data-g", item.labelColorG);
				labelLi.setAttribute("data-b", item.labelColorB);
				labelLi.style.color = item.labelColor;
				labelLi.style.borderColor = item.labelColor;
				fragment.appendChild(labelLi);
				labelLi.onmouseover = function() {
					labelLi.style.color = "#fff";
					labelLi.style.background = item.labelColor;
				};
				labelLi.onmouseleave = function() {
					labelLi.style.color = item.labelColor;
					labelLi.style.background = "transparent";
				};
				labelLi.onclick = function() {
					_self.Arrays.imageAnnotateShower[resultIndex].labels.labelName = item.labelName;
					_self.Arrays.imageAnnotateShower[resultIndex].labels.labelColor = item.labelColor;
					_self.Arrays.imageAnnotateShower[resultIndex].labels.labelColorRGB = item.labelColorR + "," + item.labelColorG + "," + item.labelColorB;
					resultSelectLabel.classList.remove("focus");
					resultSelectLabel.classList.add("blur");
					_self.Arrays.resultIndex = 0;
					_self.RepaintResultList();

					// 记录用户选择的标签作为最后使用的标签
					f_setLastUsedLabelIndex(index);
				};
			});
			selectLabelUL.appendChild(fragment);
		}
		else {
			selectLabelTip.style.display = "block";
		}
		// 判断是否显示标签管理
		if (resultSelectLabel.className.indexOf("focus") === -1) {
			resultSelectLabel.classList.remove("blur");
			resultSelectLabel.classList.add("focus");
			resultSelectLabel.style.top = pageY + "px";
		}else {
			resultSelectLabel.classList.remove("focus");
			resultSelectLabel.classList.add("blur");
		}

		// 关闭标签管理
		closeLabel.onclick = function() {
			resultSelectLabel.classList.remove("focus");
			resultSelectLabel.classList.add("blur");
		}
	};

	//----标签管理
	ManageLabels = () => {
		let labelManage = document.querySelector('.labelManage');   // 标签管理父节点
		let closeLabel = labelManage.querySelector('.closeLabelManage');  // 关闭标签管理窗口节点
		let labelManegeUL = labelManage.querySelector(".labelManage-ul");  // 标签管理列表父节点
		let labelManageInfo = labelManage.querySelector(".labelManage-Info");  // 标签列表模块
		let labelManageCreateInfo = labelManage.querySelector(".labelManage-create");   // 标签编辑模块
		let addLabel = labelManage.querySelector('.addLabel');
		let addLabelName = labelManage.querySelector('.labelCreate-nameInput');
		let addLabelColor = labelManage.querySelector('#colorHex');
		let labelManageCreate = labelManage.querySelector('.labelManage-createButton');  // 添加标签节点
		let closeAdd = labelManage.querySelector('.closeAdd'); // 取消添加标签
		let removeLabel = labelManage.querySelector('.removeLabel'); // 删除标签
		let colorPicker = labelManage.querySelector('#colorPicker'); // 选取颜色模块
		let labelTip = labelManage.querySelector('.labelTip');  // 标签提示
		let input = document.getElementById('colorHex');
		let labelManageTitle = document.querySelector('.labelManage-Title');
		let flag = false;
		let flagIndex = 0;

		let labels = mLabels;

		if (labels.length > 0 ) {
			eachLabels(labels)
		}
		else {
			labelTip.style.display = "block";
		}
		function eachLabels(labelList) {
			//加载标签数据
			labelTip.style.display = "none";
			labelManegeUL.innerHTML = "";
			let fragment = document.createDocumentFragment();
			labelList.forEach((item, index) => {
				let labelLi = document.createElement("li");
				labelLi.innerText = item.labelName;
				labelLi.value = item.labelColor;
				labelLi.setAttribute("data-index", index);
				labelLi.setAttribute("data-r", item.labelColorR);
				labelLi.setAttribute("data-g", item.labelColorG);
				labelLi.setAttribute("data-b", item.labelColorB);
				labelLi.style.color = item.labelColor;
				labelLi.style.borderColor = item.labelColor;
				fragment.appendChild(labelLi);

				labelLi.onmouseover = function() {
					labelLi.style.color = "#fff";
					labelLi.style.background = item.labelColor;
				};
				labelLi.onmouseleave = function() {
					labelLi.style.color = item.labelColor;
					labelLi.style.background = "transparent";
				};
				labelLi.onclick = function() {
					addLabelName.value = item.labelName;
					colorPicker.style.background = item.labelColor;
					input.value = item.labelColor;
					flag = true;
					flagIndex = index;
					labelManageTitle.innerText = "编辑标签";
					labelManageInfo.style.display = "none";
					labelManageCreateInfo.style.display = "block";
					removeLabel.style.display = "block";
				};
			});
			labelManegeUL.appendChild(fragment);
		}

		// 添加标签事件
		labelManageCreate.onclick = function() {
			flag = false;
			labelManageTitle.innerText = "创建标签";
			addLabelName.value = "";
			labelManageInfo.style.display = "none";
			labelManageCreateInfo.style.display = "block";
			removeLabel.style.display = "none";
		};
		closeAdd.onclick = function() {
			labelManageInfo.style.display = "block";
			labelManageCreateInfo.style.display = "none";
			eachLabels(labels)
		};

		removeLabel.onclick = function() {
			// 添加确认提示框
			if (!confirm('确定要删除这个标签吗？删除后无法恢复！')) {
				return;
			}
			labels.splice(flagIndex, 1);
			f_setLabels(labels);
			labelManageInfo.style.display = "block";
			labelManageCreateInfo.style.display = "none";
			eachLabels(labels);
		};

		colorPicker.onclick = function() {
			let colorDiv = document.querySelector('.colorDiv');
			if (!colorDiv) {
				Colorpicker.create({
					bindClass:'colorPicker',
					change: function(elem,hex,rgb){
						elem.style.backgroundColor = hex;
						input.value = hex;
						input.setAttribute('data-r', rgb.r);
						input.setAttribute('data-g', rgb.g);
						input.setAttribute('data-b', rgb.b);
					}
				});
				document.querySelector('.colorDiv').style.display = "block";
			}
		};


		addLabel.onclick = function() {
			if (!!addLabelName.value){
				if (flag) {
					labels[flagIndex].labelName = addLabelName.value;
					labels[flagIndex].labelColor = addLabelColor.value;
					labels[flagIndex].labelColorR = addLabelColor.getAttribute("data-r");
					labels[flagIndex].labelColorG = addLabelColor.getAttribute("data-g");
					labels[flagIndex].labelColorB = addLabelColor.getAttribute("data-b");
					f_setLabels(labels);
					//alert("修改成功");
					labelManageInfo.style.display = "block";
					labelManageCreateInfo.style.display = "none";
					eachLabels(labels);
				} else {
					let createData = {
						"labelName": addLabelName.value,
						"labelColor": addLabelColor.value,
						"labelColorR": addLabelColor.getAttribute("data-r"),
						"labelColorG": addLabelColor.getAttribute("data-g"),
						"labelColorB": addLabelColor.getAttribute("data-b")
					};
					labels.push(createData);
					f_setLabels(labels);
					addLabelName.value = "";
					//alert("添加成功");
					labelManageInfo.style.display = "block";
					labelManageCreateInfo.style.display = "none";
					eachLabels(labels);
				}

			}
			else {
				alert("请填写标签名称");
			}

		};

		// 判断是否显示标签管理
		if (labelManage.className.indexOf("focus") === -1) {
			labelManage.classList.remove("blur");
			labelManage.classList.add("focus");
		}else {
			labelManage.classList.remove("focus");
			labelManage.classList.add("blur");
		}

		// 关闭标签管理
		closeLabel.onclick = function() {
			labelManage.classList.remove("focus");
			labelManage.classList.add("blur");
		}
	};


	//----控制是否显示标签
	IsShowLabels = () => {
		let _nodes = this.Nodes;
		let annotates = this.Arrays.imageAnnotateShower;
		let resultList = document.querySelectorAll('.result_list');
		if (resultList.length > 0) {
			if (_nodes.labelShower.className.indexOf('focus') > -1) {
				// 隐藏标注结果
				_nodes.labelShower.children[0].checked = false;
				_nodes.labelShower.classList.remove('focus');
				resultList.forEach((item, index) => {
					item.childNodes[5].className = "isShowLabel icon-eye-close";
					annotates[index].labels.visibility = false;
				});
			}
			else {
				// 显示标注结果
				_nodes.labelShower.children[0].checked = true;
				_nodes.labelShower.classList.add('focus');
				resultList.forEach((item, index) => {
					item.childNodes[5].className = "isShowLabel icon-eye-open";
					annotates[index].labels.visibility = true;
				});
			}
		}
		this.DrawSavedAnnotateInfoToShow();
	};

	//----屏幕快照事件
	ScreenShot = () => {
		this.DrawSavedAnnotateInfoToMemory(true);
		let imgData = this.Nodes.bCanvas.toDataURL('image/jpeg');
		let windowOpen = window.open('about:blank','image from canvas');
		windowOpen.document.write("<img alt='' src='"+ imgData +"'>");
		this.DrawSavedAnnotateInfoToMemory(false);
	};

	//----缩略图画布点击定位函数
	ScaleCanvasClick = (e) => {
		let p = this.CalculateChange(e, this.Nodes.scaleCanvas);
		let tmpX = this.cWidth / 2 - this.iWidth * this.scale * p.x / this.sWidth;
		let tmpY = this.cHeight / 2 - this.iWidth * this.scale * p.x / this.sWidth * p.y / p.x;
		this.SetXY(tmpX, tmpY);
		this.ReplaceAnnotateShow();
	};

	//----滚动条缩放事件
	MouseWheel = (e) => {
		let wd = e.wheelDelta || e.detail;
		let newScale = this.scale * (1 + (wd > 0 ? this.scaleStep : -this.scaleStep));
		newScale = newScale < this.minScale ? this.minScale : newScale;
		newScale = newScale > this.maxScale ? this.maxScale : newScale;

		if (newScale !== this.scale) {
			let p = this.CalculateChange(e, this.Nodes.canvas);
			let newX = (this.x - p.x) * newScale / this.scale + p.x;
			let newY = (this.y - p.y) * newScale / this.scale + p.y;
			this.scale = newScale;
			this.SetXY(newX, newY);
		}
		clearTimeout(this.mousewheelTimer);
		this.mousewheelTimer = setTimeout(() => {
			this.IsMouseWheelEnd()
		}, 500);
		if (this.drawFlag) {
			this.DrawSavedAnnotateInfoToMemory(true);
			this.drawFlag = false;
		}
	};
	//----监听滚动条缩放是否结束
	IsMouseWheelEnd = () => {
		this.ReplaceAnnotateShow();
		this.DrawSavedAnnotateInfoToMemory(false);
		this.drawFlag = true;
	};

	//----设置图片位置，防止图片被拖出画布
	SetXY = (vx, vy) => {
		if (vx < this.appearSize - this.iWidth * this.scale) {
			this.x = this.appearSize - this.iWidth * this.scale;
		}
		else if (vx > this.cWidth - this.appearSize) {
			this.x = this.cWidth - this.appearSize;
		}
		else {
			this.x = vx;
		}

		if (vy < this.appearSize - this.iHeight * this.scale) {
			this.y = this.appearSize - this.iHeight * this.scale;
		}
		else if (vy > this.cHeight - this.appearSize) {
			this.y = this.cHeight - this.appearSize;
		}
		else {
			this.y = vy;
		}
		this.UpdateCanvas();
	};

	//----Y坐标点装换， 防止绘制到图片外
	YPointReplace = (y) => {
		if (y < this.y) {
			y = this.y
		}
		else if (y > this.iHeight * this.scale + this.y) {
			y = this.iHeight * this.scale + this.y
		}
		return y
	};
	//----X坐标点装换， 防止绘制到图片外
	XPointReplace = (x) => {
		if (x < this.x) {
			x = this.x
		}
		else if (x > this.iWidth * this.scale + this.x) {
			x = this.iWidth * this.scale + this.x
		}
		return x
	};

	//----获取更新鼠标在当前展示画板中的位置
	GetMouseInCanvasLocation = (e) => {
		//console.log("GetMouseInCanvasLocation()");

		this.mouseX = this.XPointReplace(e.layerX || e.offsetX);
		this.mouseY = this.YPointReplace(e.layerY || e.offsetY);
	};

	//----获取鼠标当前相对所在存储面板图片中的位置
	GetMouseInImageLocation = (location) => {
		let prevP = this.CalculateChange(location, this.Nodes.canvas);
		// 鼠标点击在当前图像的位置
		this.ix = Math.floor((prevP.x -this.x) / this.scale);
		if (this.ix < 0) {
			this.ix = 0;
		}
		else if (this.ix > this.iWidth) {
			this.ix = this.iWidth;
		}
		this.iy = Math.floor((prevP.y -this.y) / this.scale);
		if (this.iy < 0) {
			this.iy = 0;
		}
		else if (this.iy > this.iHeight) {
			this.iy = this.iHeight;
		}
	};

	//----计算更新鼠标相对容器的位置
	CalculateChange = (e, container, skip) => {
		!skip && e.preventDefault();
		const containerWidth = container.clientWidth;
		const containerHeight = container.clientHeight;
		const x = typeof e.pageX === "number" ? e.pageX : e.touches[0].pageX;
		const y = typeof e.pageY === "number" ? e.pageY : e.touches[0].pageY;
		let left = x- (container.getBoundingClientRect().left + window.pageXOffset);
		let top = y - (container.getBoundingClientRect().top + window.pageYOffset);

		if (left < 0) {
			left = 0
		}
		else if (left > containerWidth) {
			left = containerWidth
		}

		if (top < 0) {
			top = 0;
		}
		else if (top > containerHeight) {
			top = containerHeight;
		}

		return {
			x: left,
			y: top
		}
	};

	//----计算标签相对于当前标定范围的位置
	ComputerLabelLocation = (rectMask) => {
		let x = rectMask.width / 2 + rectMask.xMin;
		let y = rectMask.height / 2 + rectMask.yMin;
		return {x, y}
	};

	//----按缩放程度修改数据存储面板数据（坐标点转换过程）
	ReplaceAnnotateMemory = () => {
		this.Arrays.imageAnnotateMemory.splice(0, this.Arrays.imageAnnotateMemory.length);
		this.Arrays.imageAnnotateShower.map((item)=> {
			let content = [];
			item.content.forEach(contents => {
				content.push({
					"x": (contents.x - this.x) / this.scale,
					"y": (contents.y - this.y) / this.scale,
				})
			});
			let rectMask = {
				"xMin": (item.rectMask.xMin - this.x) / this.scale,
				"yMin": (item.rectMask.yMin - this.y) / this.scale,
				"width": item.rectMask.width / this.scale,
				"height": item.rectMask.height / this.scale,
			};
			this.Arrays.imageAnnotateMemory.push({
				content,
				rectMask,
				labels: item.labels,
				labelLocation: this.ComputerLabelLocation(rectMask),
				contentType: item.contentType
			});
		});
	};
	//----按缩放程度修改数据展示面板数据（坐标点转换过程）
	ReplaceAnnotateShow = () => {
		this.Arrays.imageAnnotateShower.splice(0, this.Arrays.imageAnnotateShower.length);
		this.Arrays.imageAnnotateMemory.map((item, index)=> {
			let content = [];
			item.content.forEach(contents => {
				content.push({
					"x": contents.x * this.scale + this.x,
					"y": contents.y * this.scale + this.y,
				})
			});
			let rectMask = {
				"xMin": item.rectMask.xMin * this.scale + this.x,
				"yMin": item.rectMask.yMin * this.scale + this.y,
				"width": item.rectMask.width * this.scale,
				"height": item.rectMask.height * this.scale,
			};
			this.Arrays.imageAnnotateShower.push({
				content,
				rectMask,
				labels: item.labels,
				labelLocation: this.ComputerLabelLocation(rectMask),
				contentType: item.contentType
			});
		});
	};

	/*
		画板右键菜单事件 - 显示标签选择菜单
	 */
	static NoRightMenu(event) {
		event.preventDefault();

		// 获取当前LabelImage实例
		const labelImageInstance = event.currentTarget.labelImageInstance;
		if (labelImageInstance && labelImageInstance.Arrays.imageAnnotateShower.length > 0) {
			labelImageInstance.ShowRightClickLabelMenu(event);
		}
	}
}