body,h1,h2,h3,h4,h5,h6,hr,p,blockquote,dl,dt,dd,ul,ol,li,pre,form,fieldset,legend,button,input,textarea,th,td{ margin:0; padding:0; }
img,input{ border:0;}
.clear:after { visibility:hidden; display:block; font-size:0; content:"."; clear:both; height:0;}
.clear { zoom:1;}
li{ list-style:none}
i,em{ font-style:normal;}
table{border-collapse: collapse;border: none;width: 100%; table-layout: fixed;}
body,html{
    font-size: 0;
    overflow-y: hidden;
    overflow-x: hidden;
    height: 100%;
}

@keyframes rotate {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-moz-keyframes rotate {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-ms-keyframes rotate {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
.inline-block {
    display: inline-block;
    vertical-align: top;
}
.button {
    display: inline-block;
    vertical-align: top;
    border: 1px solid transparent;
    width: 55px;
    height: 30px;
    line-height: 30px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    font-size: 12px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.button.btn-primary {
    background: #428bca;
    color: #fff;
}
.button.btn-success {
    background: #00ca6b;
    color: #fff;
}
.button.btn-default {
    background: #fff;
    color: #333;
    border: 1px solid #333;
}
.button.btn-error {
    background: #ca2700;
    color: #fff;
}
.removeLabel {
    margin-left: 10px;
}
.LabelImage {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
}
/*
      头部工具栏
     */
.toolHead {
    width: 100%;
    background: -webkit-linear-gradient(left, #091f35, #091f35);/* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #091f35, #091f35);/* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #091f35, #091f35);/* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #091f35, #091f35);/* 标准的语法 */
    height: 60px;
    position: relative;
}
.toolHead .toolMuster {
    width: 100%;
    height: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
}
.toolMuster > .assistNav {
        position: absolute;
    top: 0;
    left: 0;
    width: 120px;
}
.toolMuster > .assistNav > .logo {
    font-size: 22px;
    color: white;
    line-height: 60px;
    cursor: pointer;
    text-align: center;
    margin-left: 20px;
}

.toolMuster > .pageNav {
    position: absolute;
    top: 0;
    left: 120px;
    right: 0;
    /*margin: auto;*/
    width: 280px;
    height: 60px;
}

.pageNav > .pageControl {
    width: 100%;
    text-align: center;
}
.pageNav > .pageControl .pageSwitch{
    width: 36px;
    height: 36px;
    margin-top: 12px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    display: inline-block;
    cursor: pointer;
}
.pageNav > .pageControl .pagePrev {
    background: url(../images/toolIcons.png) no-repeat 0 -192px;
}
.pageNav > .pageControl .pageNext {
    background: url(../images/toolIcons.png) no-repeat 0 -288px;
}
.pageNav > .pageControl .pagePrev:hover {
    background: url(../images/toolIcons.png) no-repeat -96px -192px;
}
.pageNav > .pageControl .pageNext:hover {
    background: url(../images/toolIcons.png) no-repeat -96px -288px;
}
.pageNav > .pageControl .pageInfo {
    min-width: 150px;
    margin: 0 15px;
    color: #fff;
    line-height: 1.5;
}
.pageNav > .pageControl .pageInfo .pageName {
    margin-top: 6px;
    font-size: 14px;
    width: 100%;
    height: 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.pageNav > .pageControl .pageInfo .nameProcess {
    margin-top: 5px;
    font-size: 12px;
}
.pageNav > .pageControl .pageInfo .annotateProgress {
    margin-top: 3px;
    margin-bottom: 0;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    height: 3px;
    line-height: 3px;
}

.toolMuster > .assistTool {
    line-height: 60px;
    position: absolute;
    right: 50px;
    top: 0;
    margin: auto;
}

.toolMuster > .assistTool > .generalFeatures {
    width: 100%;
    text-align: right;
}
.toolMuster > .assistTool > .generalFeatures .featureList {
    display: inline-block;
    margin-right: 30px;
    height: 60px;
    color: #b3b3b3;
    text-align: center;
    line-height: 1;
    vertical-align: top;
    cursor: pointer;
}
.toolMuster > .assistTool > .generalFeatures .featureList i.bg {
    font-style: normal;
    width: 36px;
    height: 36px;
    display: block;
}
.toolMuster > .assistTool > .generalFeatures .featureList span {
    font-size: 12px;
    display: block;
}
.toolMuster > .assistTool > .generalFeatures .submitSave {
    padding-top: 12px;
    display: none;
}
.toolMuster > .assistTool > .generalFeatures .crossLine {
    padding-top: 12px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.toolMuster > .assistTool > .generalFeatures .crossLine input {
    margin: 0;
}
.toolMuster > .assistTool > .generalFeatures .crossLine span {
    margin-top: 8px;
}
.toolMuster > .assistTool > .generalFeatures .labelShower {
    padding-top: 12px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.toolMuster > .assistTool > .generalFeatures .labelShower input {
    margin: 0;
}
.toolMuster > .assistTool > .generalFeatures .labelShower span {
    margin-top: 8px;
}

.toolMuster > .assistTool > .generalFeatures .toolDrag {
    margin-right: 20px;
}
.toolMuster > .assistTool > .generalFeatures .toolDrag i.bg {
    background: url(../images/toolIcons.png) no-repeat 0 -768px;
}
.toolMuster > .assistTool > .generalFeatures .toolDrag:hover i.bg {
    background:  url(../images/toolIcons.png) no-repeat -96px -768px;
}
.toolMuster > .assistTool > .generalFeatures .toolDrag.xc_selected i.bg {
    border: 1px solid #757a84;
    margin: 2px;
}
.toolMuster > .assistTool > .generalFeatures .toolDrag:hover span {
    color: #fff;
}
.toolMuster > .assistTool > .generalFeatures .commonBtn {
    margin-right: 10px;
}
.toolMuster > .assistTool > .generalFeatures .commonBtn span {
    margin-top: 17px;
    line-height: 26px;
    font-size: 14px;
    border: solid 1px #fff;
    border-radius: 5px;
    padding-left: 6px;
    padding-right: 6px;
}
.toolMuster > .assistTool > .generalFeatures .commonBtn:hover span {
    border: solid 1px #aca3a3;
}

.toolMuster > .assistTool > .generalFeatures .toolRect {
    margin-right: 20px;
}
.toolMuster > .assistTool > .generalFeatures .toolRect i.bg {
    background: url(../images/toolIcons.png) no-repeat 0 -960px;
}
.toolMuster > .assistTool > .generalFeatures .toolRect:hover i.bg {
    background:  url(../images/toolIcons.png) no-repeat -96px -960px;
}
.toolMuster > .assistTool > .generalFeatures .toolRect.xc_selected i.bg {
    border: 1px solid #757a84;
    margin: 2px;
}
.toolMuster > .assistTool > .generalFeatures .toolRect:hover span {
    color: #fff;
}

.toolMuster > .assistTool > .generalFeatures .toolPolygon {
    margin-right: 20px;
}
.toolMuster > .assistTool > .generalFeatures .toolPolygon i.bg {
    background: url(../images/toolIcons.png) no-repeat 0 -1056px;
}
.toolMuster > .assistTool > .generalFeatures .toolPolygon:hover i.bg {
    background:  url(../images/toolIcons.png) no-repeat -96px -1056px;
}
.toolMuster > .assistTool > .generalFeatures .toolPolygon.xc_selected i.bg {
    border: 1px solid #757a84;
    margin: 2px;
}
.toolMuster > .assistTool > .generalFeatures .toolPolygon:hover span {
    color: #fff;
}

.toolMuster > .assistTool > .generalFeatures .screenShot {
    margin-right: 20px;
}
.toolMuster > .assistTool > .generalFeatures .screenShot i.bg {
    background: url(../images/toolIcons.png) no-repeat 0 -384px;
}
.toolMuster > .assistTool > .generalFeatures .screenShot:hover i.bg {
    background: url(../images/toolIcons.png) no-repeat -96px -384px;
}
.toolMuster > .assistTool > .generalFeatures .screenShot:hover span {
    color: #fff;
}
.toolMuster > .assistTool > .generalFeatures .screenFull {
    margin-right: 20px;
}
.toolMuster > .assistTool > .generalFeatures .screenFull i.bg {
    background: url(../images/toolIcons.png) no-repeat 0 -480px;
}
.toolMuster > .assistTool > .generalFeatures .screenFull:hover i.bg {
    background: url(../images/toolIcons.png) no-repeat -96px -480px;
}
.toolMuster > .assistTool > .generalFeatures .screenFull:hover span {
    color: #fff;
}
.toolMuster > .assistTool > .generalFeatures .dividingLine {
    width: 1px;
    height: 28px;
    background: #ff0000;
    margin-top: 16px;
    margin-right: 20px;
    display: inline-block;
}
.toolMuster > .assistTool > .generalFeatures .clientHelp {
    margin-right: 20px;
}
.toolMuster > .assistTool > .generalFeatures .clientHelp i.bg {
    background: url(../images/toolIcons.png) no-repeat 0 -576px;
}
.toolMuster > .assistTool > .generalFeatures .clientHelp:hover i.bg {
    background: url(../images/toolIcons.png) no-repeat -96px -576px;
}
.toolMuster > .assistTool > .generalFeatures .clientHelp:hover span {
    color: #fff;
}
.toolMuster > .assistTool > .generalFeatures .clientExit {
    margin-right: 20px;
}
.toolMuster > .assistTool > .generalFeatures .clientExit i.bg {
    background: url(../images/toolIcons.png) no-repeat 0 -672px;
}
.toolMuster > .assistTool > .generalFeatures .clientExit:hover i.bg {
    background: url(../images/toolIcons.png) no-repeat -96px -672px;
}
.toolMuster > .assistTool > .generalFeatures .clientExit:hover span {
    color: #fff;
}
/*
  canvasMain 标注画板主体内容
 */
.canvasMain {
    width: 100%;
    flex: auto;
    height: 0;
    display: flex;
    background: #434343;
    position: relative;
}

.canvasMain .canvasContent {
    flex: auto;
    width: 0;
    height: 100%;
    position: relative;
}
.canvasContent #canvas {
    cursor: crosshair;
    width: 100%;
    height: 100%;
}
.canvasContent .scaleBox {
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 6px 6px 0 6px;
    background: #293245;
    border: 1px solid #3c5167;
}
.canvasContent .scaleBox .scaleCanvas {
    position: relative;
    width: 150px;
    overflow: hidden;
    z-index: 999;
    cursor: pointer;
}
.canvasContent .scaleBox .scalePanel {
    width: 100%;
    font-size: 12px;
    text-align: center;
    color: #fff;
}
.canvasMain .commentResult {
    width: 300px;
    height: 100%;
    background: #091f35;
    border-top: 1px solid #3c5167;

    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    display: flex;
    flex-direction: column;
}
.commentResult .resultArea {
    position: relative;
    width: 100%;
    flex: 1;
    height: 0;
    display: flex;
    flex-direction: column;
}
.commentResult .resultArea .title {
    width: 100%;
    color: #fff;
    height: 36px;
    line-height: 36px;
    font-size: 12px;
    text-indent: 10px;
    -webkit-box-shadow: 0 1px 3px 0 #3c5167;
    -moz-box-shadow: 0 1px 3px 0 #3c5167;
    box-shadow: 0 1px 3px 0 #3c5167;
    background: #091f35;
}
.commentResult .resultArea .resultList_head {
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    padding-left: 10px;
    padding-right: 10px;
    position: relative;
    color: #fff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.resultList_head .headChildren {
    width: 100%;
    height: 30px;
    border-bottom: 1px solid #3c5167;
}
.resultList_head .headChildren .headName {
    display: inherit;
    position: absolute;
    left: 50px;
}
.resultList_head .headChildren .headDisplay {
    position: absolute;
    display: inherit;
    right: 10px;
}
.resultList_head .headChildren .headDelete {
    position: absolute;
    display: inherit;
    right: 50px;
}
.resultList_head .headChildren .headEdit {
    position: absolute;
    display: inherit;
    right: 90px;
}
.commentResult .resultArea .resultGroup {
    flex: auto;
    height: 0;
    overflow-y: auto;
}
.resultGroup .result_list {
    width: 100%;
    height: 28px;
    line-height: 28px;
    color: #999;
    position: relative;
}
.resultGroup .result_list .result_no {
    width: 28px;
    display: inline-block;
    vertical-align: top;
    text-align: center;
    font-size: 12px;
}
.resultGroup .result_list .result_color {
    width: 10px;
    height: 10px;
    background: #ff0000;
    display: inline-block;
    vertical-align: middle;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    margin-right: 10px;
    cursor: pointer;
}
.resultGroup .result_list .result_Name {
    width: 220px;
    font-size: 12px;
    line-height: 28px;
    color: #fff;
    display: inline-block;
    vertical-align: top;
    border: 0;
    background: transparent;
}
.resultGroup .result_list .result_Name[disabled=true] {
    cursor: default;
}
.resultGroup .result_list i {
    display: block;
    position: absolute;
    font-style: normal;
    font-size: 12px;
    height: 28px;
    line-height: 28px;
    color: #fff;
    top: 0;
    cursor: pointer;
}
.resultGroup .result_list .editLabelName {
    right: 96px;
}
.resultGroup .result_list .deleteLabel {
    right: 57px;
}
.resultGroup .result_list .isShowLabel {
    right: 18px;
}
.resultGroup .result_list.active {
    background: #212b3e;
}
.resultGroup .result_list:hover {
    background: rgba(33, 43, 62, 0.6);
}
.commentResult .resultArea .resultSelectLabel {
    position: absolute;
    top: 80px;
    right: 42px;
    width: 200px;
    height: 220px;
    padding: 24px 6px 8px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background: #fff;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.3);
    z-index: 999999;
    opacity: 0;
    visibility: hidden;
    font-size: 12px;
}
.commentResult .resultArea .resultSelectLabel .selectLabelTip {
    font-size: 14px;
    text-align: center;
}
.commentResult .resultArea .resultSelectLabel .selectLabel-ul {
    height: 100%;
    overflow-y: auto;
}
.commentResult .resultArea .resultSelectLabel .selectLabel-ul li {
    display: inline-block;
    position: relative;
    vertical-align: top;
    -webkit-border-radius: 50px;
    -moz-border-radius: 50px;
    border-radius: 50px;
    border: 1px solid #999;
    padding: 3px 6px;
    margin: 4px 4px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 12px;
    cursor: pointer;
    -webkit-transition: 0.3s;
    -moz-transition: 0.3s;
    -ms-transition: 0.3s;
    -o-transition: 0.3s;
    transition: 0.3s;
}
.commentResult .resultArea .resultSelectLabel .closeLabelManage {
    position: absolute;
    top: 0;
    right: 4px;
    font-size: 18px;
    cursor: pointer;
}
.commentResult .resultArea .resultSelectLabel::before {
    content: '';
    display: block;
    position: absolute;
    top: -10px;
    right: 50px;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    border-bottom: 10px solid #f1f1f1;
}
.commentResult .resultArea .resultSelectLabel::after {
    content: '';
    display: block;
    position: absolute;
    top: -9px;
    right: 50px;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    border-bottom: 10px solid #fff;
}
.commentResult .resultArea .resultSelectLabel.focus {
    -webkit-animation: showSelectLabel 0.4s ease-out forwards;
    -o-animation: showSelectLabel 0.4s ease-out forwards;
    animation: showSelectLabel 0.4s ease-out forwards;
    visibility: visible;
}
.commentResult .resultArea .resultSelectLabel.blur {
    -webkit-animation: hideSelectLabel 0.4s ease-out forwards;
    -o-animation: hideSelectLabel 0.4s ease-out forwards;
    animation: hideSelectLabel 0.4s ease-out forwards;
}
@keyframes showSelectLabel {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@-webkit-keyframes showSelectLabel {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@keyframes hideSelectLabel {
    from {
        opacity: 1;
    }
    to {
        visibility: hidden;
        opacity: 0;
    }
}
@-webkit-keyframes hideSelectLabel {
    from {
        opacity: 1;
    }
    to {
        visibility: hidden;
        opacity: 0;
    }
}
.commentResult .historyContent {
    width: 100%;
    height: 100px;
    /*flex: 1;*/
    display: flex;
    flex-direction: column;
}
.commentResult .historyContent .title {
    width: 100%;
    color: #fff;
    height: 36px;
    line-height: 36px;
    font-size: 12px;
    text-indent: 10px;
    -webkit-box-shadow: 0 1px 3px 0 #3c5167;
    -moz-box-shadow: 0 1px 3px 0 #3c5167;
    box-shadow: 0 1px 3px 0 #3c5167;
    background: #212d3f;
}
.commentResult .historyContent .historyGroup {
    flex: auto;
    overflow-y: auto;
    padding: 5px 0;
    box-sizing: border-box;
    height: 0;
}
.commentResult .historyContent .historyGroup p {
    color: #f1f1f1;
    line-height: 1.8;
    font-size: 12px;
    padding: 0 10px;
    box-sizing: border-box;
}
.commentResult .historyContent .historyGroup p:hover {
    background: rgba(33, 43, 62, 0.6);
}
.commentResult .historyContent .historyGroup p.active {
    background: #212b3e;
}
.commentResult .historyContent .historyGroup p.record {
    color: #999999;
}
.labelManage {
    position: absolute;
    top: 10px;
    right: 20px;
    width: 300px;
    height: 340px;
    padding: 24px 6px 8px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background: #fff;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.3);
    z-index: 999999;
    opacity: 0;
    visibility: hidden;
}
.labelManage .labelManage-Info {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
}
.labelManage .labelManage-Info .labelManage-menu {
    width: 100%;
    display: flex;
}
.labelManage-menu .labelManage-createLabel {
    flex: 3;
    /*text-align: center;*/
    line-height: 30px;
}
.labelManage-Info .labelManage-subList {
    font-size: 12px;
    margin-top: 10px;
}
.labelManage-Info .labelManage-group {
    width: 100%;
    margin-top: 10px;
    flex: auto;
}
.labelManage-Info .labelManage-group .labelTip {
    text-align: center;
    font-size: 14px;
}
.labelManage-Info .labelManage-group .labelManage-ul {
    height: 100%;
    overflow-y: auto;
}
.labelManage-Info .labelManage-group .labelManage-ul li {
    display: inline-block;
    position: relative;
    vertical-align: top;
    -webkit-border-radius: 50px;
    -moz-border-radius: 50px;
    border-radius: 50px;
    border: 1px solid #999;
    padding: 3px 6px;
    margin: 4px 4px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 12px;
    cursor: pointer;
    -webkit-transition: 0.3s;
    -moz-transition: 0.3s;
    -ms-transition: 0.3s;
    -o-transition: 0.3s;
    transition: 0.3s;
}
.labelManage .labelManage-create {
    width: 100%;
    height: 100%;
    position: relative;
}
.labelManage .labelManage-create .labelManage-Title {
    font-size: 1.875rem;
}
.labelManage .labelManage-create .labelCreate {
    margin-top: 1.25rem;
}
.labelManage .labelManage-create .labelCreate label {
    width: 80px;
    text-align: right;
    font-size: 14px;
    line-height: 30px;
    font-weight: normal;
}
.labelManage .labelManage-create .labelCreate .labelCreate-nameInput {
    width: 195px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid #e1e1e1;
    font-size: 14px;
    height: 30px;
    text-indent: 8px;
}
.labelManage .labelManage-create .labelCreate .colorPicker {
    display: inline-block;
    vertical-align: top;
    width: 20px;
    height: 20px;
    background: #ff0000;
    cursor: pointer;
    margin-top: 5px;
}
.labelManage .labelManage-create .labelCreate .colorHex {
    height: 26px;
    width: 60px;
    font-size: 12px;
    margin-left: 8px;
    outline: none;
}
.labelManage .labelManage-create .labelCreateButtons {
    margin-top: 50px;
    text-align: center;
}
.labelManage .labelManage-create .labelCreateButtons button {
    margin: 0 10px;
}
.labelManage .closeLabelManage {
    position: absolute;
    top: 0;
    right: 4px;
    font-size: 18px;
    cursor: pointer;
}
.labelManage.focus {
    -webkit-animation: showLabelManage 0.4s ease-out forwards;
    -o-animation: showLabelManage 0.4s ease-out forwards;
    animation: showLabelManage 0.4s ease-out forwards;
    visibility: visible;
}
.labelManage.blur {
    -webkit-animation: hideLabelManage 0.4s ease-out forwards;
    -o-animation: hideLabelManage 0.4s ease-out forwards;
    animation: hideLabelManage 0.4s ease-out forwards;
}
@keyframes showLabelManage {
    from {
        opacity: 0;
        top: 20px;
    }
    to {
        opacity: 1;
        top: 10px;
    }
}
@-webkit-keyframes showLabelManage {
    from {
        opacity: 0;
        top: 20px;
    }
    to {
        opacity: 1;
        top: 10px;
    }
}
@keyframes hideLabelManage {
    from {
        opacity: 1;
        top: 10px;
    }
    to {
        opacity: 0;
        top: 20px;
        visibility: hidden;
    }
}
@-webkit-keyframes hideLabelManage {
    from {
        opacity: 1;
        top: 10px;
    }
    to {
        opacity: 0;
        top: 20px;
        visibility: hidden;
    }
}

/* 右键标签选择菜单样式 */
.right-click-label-menu {
    position: fixed;
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 4px 0;
    z-index: 9999;
    display: none;
    min-width: 140px;
    max-height: 300px;
    overflow-y: auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
}

.right-click-menu-item {
    padding: 8px 16px;
    cursor: pointer;
    color: #374151;
    transition: all 0.15s ease;
    border-left: 3px solid transparent;
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.right-click-menu-item:hover {
    background-color: #f3f4f6;
    color: #111827;
}

.right-click-menu-item:active {
    background-color: #e5e7eb;
}

/* 滚动条样式 */
.right-click-label-menu::-webkit-scrollbar {
    width: 6px;
}

.right-click-label-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.right-click-label-menu::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.right-click-label-menu::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 菜单动画效果 */
.right-click-label-menu.show {
    animation: fadeInMenu 0.15s ease-out;
}

@keyframes fadeInMenu {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-5px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .right-click-label-menu {
        min-width: 120px;
        font-size: 16px;
    }

    .right-click-menu-item {
        padding: 12px 16px;
    }
}