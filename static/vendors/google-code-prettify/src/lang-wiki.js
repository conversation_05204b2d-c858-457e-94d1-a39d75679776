// Copyright (C) 2009 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


/**
 * @fileoverview
 * Registers a language handler for Wiki pages.
 *
 * Based on WikiSyntax at http://code.google.com/p/support/wiki/WikiSyntax
 *
 * <AUTHOR>
 */

PR['registerLangHandler'](
    PR['createSimpleLexer'](
        [
         // Whitespace
         [PR['PR_PLAIN'],       /^[\t \xA0a-gi-z0-9]+/, null,
          '\t \xA0abcdefgijklmnopqrstuvwxyz0123456789'],
         // Wiki formatting
         [PR['PR_PUNCTUATION'], /^[=*~\^\[\]]+/, null, '=*~^[]']
        ],
        [
         // Meta-info like #summary, #labels, etc.
         ['lang-wiki.meta',  /(?:^^|\r\n?|\n)(#[a-z]+)\b/],
         // A WikiWord
         [PR['PR_LITERAL'],     /^(?:[A-Z][a-z][a-z0-9]+[A-Z][a-z][a-zA-Z0-9]+)\b/
          ],
         // A preformatted block in an unknown language
         ['lang-',           /^\{\{\{([\s\S]+?)\}\}\}/],
         // A block of source code in an unknown language
         ['lang-',           /^`([^\r\n`]+)`/],
         // An inline URL.
         [PR['PR_STRING'],
          /^https?:\/\/[^\/?#\s]*(?:\/[^?#\s]*)?(?:\?[^#\s]*)?(?:#\S*)?/i],
         [PR['PR_PLAIN'],       /^(?:\r\n|[\s\S])[^#=*~^A-Zh\{`\[\r\n]*/]
        ]),
    ['wiki']);

PR['registerLangHandler'](
    PR['createSimpleLexer']([[PR['PR_KEYWORD'], /^#[a-z]+/i, null, '#']], []),
    ['wiki.meta']);
