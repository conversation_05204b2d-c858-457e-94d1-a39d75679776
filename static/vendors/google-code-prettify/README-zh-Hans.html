<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
 "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html>
  <head>
    <meta http-equiv="content-type" value="text/html; charset=UTF-8" />
    <title>Javascript code prettifier</title>

    <link href="src/prettify.css" type="text/css" rel="stylesheet" />

    <script src="src/prettify.js" type="text/javascript"></script>

    <style type="text/css">
      body { margin-left: .5in }
      h1, h2, h3, h4, .footer { margin-left: -.4in; }
    </style>
  </head>

  <body onload="prettyPrint()" bgcolor="white">
    <h1>Javascript code prettifier</h1>
	<h1>Javascript 代码美容师</h1>
    <h2>安装使用</h2>
    <ol>
      <li><a href="http://google-code-prettify.googlecode.com/files/prettify-small-5-Jul-2008.zip">下载</a> 文件
      <li>在你的文件中包含这儿的脚本和样式表(你要保证这儿的 css 和 js 文件按在你的服务器上, 并且调整在 <tt>script</tt> 和 <tt>link</tt>标签中的路径)
        <pre class="prettyprint">
&lt;link href="prettify.css" type="text/css" rel="stylesheet" />
&lt;script type="text/javascript" src="prettify.js">&lt;/script></pre>
      <li>添加<code class="prettyprint">onload="prettyPrint()"</code> 到你的文件的 body 标签中.
      <li>修改样式表,使用你自己喜欢的颜色.</li>
    </ol>

    <h2>使用方法<br></h2>
    <p>在
    <tt>&lt;pre class="prettyprint"&gt;...&lt;/pre&gt;</tt> 或 <tt>&lt;code class="prettyprint"&gt;...&lt;/code&gt;</tt>
    中间放上代码片段,它就会自动被美化了.

    <table summary="code examples">
      <tr>
        <th>The original
        <th>Prettier
      <tr>
        <td><pre style="border: 1px solid #888;padding: 2px"
             ><a name="voila1"></a>class Voila {
public:
  // Voila
  static const string VOILA = "Voila";

  // will not interfere with embedded <a href="#voila1">tags</a>.
}</pre>

        <td><pre class="prettyprint"><a name="voila2"></a>class Voila {
public:
  // Voila
  static const string VOILA = "Voila";

  // will not interfere with embedded <a href="#voila2">tags</a>.
}</pre>
    </table>

    
    <h2>常见问题</h2>
    <h3 id="langs">它是为什么语言工作的?</h3>
    <p><tt>prettify.js中的注释是权威的,但是它的语法分析程序可以在很多语言中使用</tt>,包括 C ,
    Java, Python, Bash, SQL, HTML, XML, CSS, Javascript, 和 Makefiles.
    它在 Ruby, PHP, VB, 和 Awk 中还算可以,而且也可以在 Perl 和 Ruby的合适子集中起作用,但是, 因为注释的约定,它对
    Smalltalk, 或 CAML类似的语言起作用.<br></p>

    
    <p>LISP系列的语言可以使用一个拓展<a href="http://code.google.com/p/google-code-prettify/source/browse/trunk/src/lang-lisp.js"><code>lang-lisp.js</code></a>.</p><p>对于 <a href="http://code.google.com/p/google-code-prettify/source/browse/trunk/src/lang-css"><code>CSS</code></a>,
    <a href="http://code.google.com/p/google-code-prettify/source/browse/trunk/src/lang-hs"><code>Haskell</code></a>,
    <a href="http://code.google.com/p/google-code-prettify/source/browse/trunk/src/lang-lua.js"><code>Lua</code></a>,
    <a href="http://code.google.com/p/google-code-prettify/source/browse/trunk/src/lang-ml.js"><code>OCAML, SML, F#</code></a>,
    <a href="http://code.google.com/p/google-code-prettify/source/browse/trunk/src/lang-vb.js"><code>Visual Basic</code></a>,
    <a href="http://code.google.com/p/google-code-prettify/source/browse/trunk/src/lang-sql.js"><code>SQL</code></a>,
    <a href="http://code.google.com/p/google-code-prettify/source/browse/trunk/src/lang-proto.js"><code>Protocol Buffers</code></a>, 和
    <a href="http://code.google.com/p/google-code-prettify/source/browse/trunk/src/lang-wiki.js"><code>WikiText</code></a>..也是类似的</p><p>如果你想给你喜欢的语言写个拓展版本, 请参阅<tt>src/lang-lisp.js</tt> ,并写一个包括你的语言的拓展的 <a href="http://code.google.com/p/google-code-prettify/issues/list"
     >发布</a> 和一个测试用例.</p>

    <h3>如何指定我的代码在哪种语言里?</h3>
    <p>你不需要指定语言环境,因为 <code>prettyprint()</code>
    会对此进行猜测.  你可以使用<code> prettyprint </code>这个类通过指定语言的拓展名来指定语言,就像这样:</p>
    <pre class="prettyprint lang-html"
>&lt;pre class=&quot;prettyprint <b>lang-html</b>&quot;&gt;
  The lang-* class specifies the language file extensions.
  File extensions supported by default include
    "bsh", "c", "cc", "cpp", "cs", "csh", "cyc", "cv", "htm", "html",
    "java", "js", "m", "mxml", "perl", "pl", "pm", "py", "rb", "sh",
    "xhtml", "xml", "xsl".
&lt;/pre&gt;</pre>

    <h3>它在混淆代码例子上不起作用吗?</h3>
    <p>是的.  美化混淆代码就像给小猪涂口红,也就是不在这个工具的范围内. <br></p>

    <h3>它可以在那些浏览器上工作?</h3>
    <p>这个工具已经在 IE 6, Firefox 1.5 &amp; 2, 和 Safari 2.0.4 上测试通过. 打开 <a href="tests/prettify_test.html">测试页面</a> ,看看它能不能在你的浏览器上起作用.</p>

    <h3>有什么改变?</h3>
    <p>查看 <a href="CHANGES.html">变化日志</a></p>

    <h3>&nbsp;为什么Prettyprinting 对WordPress中的字符串没用?</h3>
    <p>很显然,wordpress 在 &quot;smart quoting&quot; 时会改变关闭符号.
    这使得关闭符号跟开始符号不配套.
    <p>这和复制粘贴代码一样,破坏了美化作用. 去
    <a href="http://wordpress.org/support/topic/125038"
    >WordPress's help center</a> 查看更多关于如何关闭插入代码段时的&quot;smart quoting&quot;的信息.</p>

    <h3>如何在我的代码中加入行号?  (Out of date -- see <a href="README.html">English version</a>)</h3>
    <p>你可以使用 <code>nocode</code> 类来标记 span 标记不是代码.
<pre>&lt;pre class=prettyprint&gt;
&lt;span class="<b>nocode</b>"&gt;1:&lt;/span&gt; /* This is line 1 of my code
&lt;span class="<b>nocode</b>"&gt;2:&lt;/span&gt;  * and here's line 2 */
&lt;span class="<b>nocode</b>"&gt;3:&lt;/span&gt; print("I'm line number 3");
&lt;/pre&gt;</pre>得到
<pre class=prettyprint>
<span class="nocode">1:</span> /* This is line 1 of my code
<span class="nocode">2:</span>  * and here's line 2 */
<span class="nocode">3:</span> print("I'm line number 3");
</pre>

    <p>查看一个更完整的例子： issue22
    <a href="tests/prettify_test.html#issue22">testcase</a>.</p>

    <h3>我得到了这样一条错误信息 &quot;a is not a function&quot; 或 &quot;opt_whenDone is not a function&quot;</h3>
    <p>如果你通过事件句柄条用 <code>prettyPrint</code> , 把它隐藏到一个函数中. <br></p><p>不要这么写
    </p><blockquote>
      <code class="prettyprint lang-js"
       >addEventListener('load', prettyPrint, false);</code>
    </blockquote>而要这么写
    <blockquote>
      <code class="prettyprint lang-js"
       >addEventListener('load', function (event) { prettyPrint() }, false);</code>
    </blockquote>这样的话,浏览器就不会把时间对象传递给 <code>prettyPrint</code> ,事件对象会让它困惑的.

    <br><br><br>

    <div class="footer">
<!-- Created: Tue Oct  3 17:51:56 PDT 2006 -->
<!-- hhmts start -->
Last modified: Wed Jan  7 13:25:42 PST 2009<br><!-- hhmts end -->
    </div>
  </body>
</html>
