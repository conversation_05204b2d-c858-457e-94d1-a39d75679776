<!DOCTYPE html>
<html lang="en">
  	<head>
    	<meta charset="utf-8">
    	<meta http-equiv="X-UA-Compatible" content="IE=edge">
    	<meta name="viewport" content="width=device-width, initial-scale=1">
		<title>A tiny, opensource, Bootstrap WYSIWYG rich text editor</title>
		<link href="../bower_components/google-code-prettify/src/prettify.css" rel="stylesheet" />
		<link rel="stylesheet" href="http://netdna.bootstrapcdn.com/bootstrap/3.3.5/css/bootstrap.min.css" />
		<link href="http://netdna.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.css" rel="stylesheet" />
		<link href="../css/style.css" rel="stylesheet" />
	</head>

	<body>
		<div class="container">
			<h1>Multiple Editors with Toolbars</h1>
			<hr>
			<br />
			<h3>First Editor</h3>
			<div class="btn-toolbar" data-role="editor-toolbar" data-target="#first-editor">
					<div class="btn-group">
						<a class="btn btn-default dropdown-toggle" data-toggle="dropdown"
							title="Font"><i class="fa fa-font"></i><b class="caret"></b>
						</a>
						<ul class="dropdown-menu">
						</ul>
					</div>
					<div class="btn-group">
						<a class="btn btn-default dropdown-toggle" data-toggle="dropdown"
							title="Font Size"><i class="fa fa-text-height"></i>&nbsp;<b
							class="caret"></b>
						</a>
						<ul class="dropdown-menu">
							<li><a data-edit="fontSize 5" class="fs-Five">Huge</a></li>
							<li><a data-edit="fontSize 3" class="fs-Three">Normal</a></li>
							<li><a data-edit="fontSize 1" class="fs-One">Small</a></li>
						</ul>
					</div>
					<div class="btn-group">
						<a class="btn btn-default" data-edit="bold" title="Bold (Ctrl/Cmd+B)"><i class="fa fa-bold"></i></a>
						<a class="btn btn-default" data-edit="italic" title="Italic (Ctrl/Cmd+I)"><i class="fa fa-italic"></i></a>
						<a class="btn btn-default" data-edit="strikethrough" title="Strikethrough"><i class="fa fa-strikethrough"></i></a>
						<a class="btn btn-default" data-edit="underline" title="Underline (Ctrl/Cmd+U)"><i class="fa fa-underline"></i></a>
					</div>
					<div class="btn-group">
						<a class="btn btn-default" data-edit="insertunorderedlist" title="Bullet list"><i class="fa fa-list-ul"></i></a>
						<a class="btn btn-default" data-edit="insertorderedlist" title="Number list"><i class="fa fa-list-ol"></i></a>
						<a class="btn btn-default" data-edit="outdent" title="Reduce indent (Shift+Tab)"><i class="fa fa-outdent"></i></a>
						<a class="btn btn-default" data-edit="indent" title="Indent (Tab)"><i class="fa fa-indent"></i></a>
					</div>
					<div class="btn-group">
						<a class="btn btn-default" data-edit="justifyleft" title="Align Left (Ctrl/Cmd+L)"><i class="fa fa-align-left"></i></a>
						<a class="btn btn-default" data-edit="justifycenter" title="Center (Ctrl/Cmd+E)"><i class="fa fa-align-center"></i></a>
						<a class="btn btn-default" data-edit="justifyright" title="Align Right (Ctrl/Cmd+R)"><i class="fa fa-align-right"></i></a>
						<a class="btn btn-default" data-edit="justifyfull" title="Justify (Ctrl/Cmd+J)"><i class="fa fa-align-justify"></i></a>
					</div>
					<div class="btn-group">
						<a class="btn btn-default dropdown-toggle" data-toggle="dropdown" title="Hyperlink"><i class="fa fa-link"></i></a>
						<div class="dropdown-menu input-append">
							<input placeholder="URL" type="text" data-edit="createLink" />
							<button class="btn" type="button">Add</button>
						</div>
					</div>
					<div class="btn-group">
						<a class="btn btn-default" data-edit="unlink" title="Remove Hyperlink"><i class="fa fa-unlink"></i></a>
						<span class="btn btn-default" title="Insert picture (or just drag & drop)" id="picBtn"> <i class="fa fa-picture-o"></i>
							<input class="imgUpload" type="file" data-role="magic-overlay" data-target="#picBtn" data-edit="insertImage" />
						</span>
					</div>
					<div class="btn-group">
						<a class="btn btn-default" data-edit="undo" title="Undo (Ctrl/Cmd+Z)"><i class="fa fa-undo"></i></a>
						<a class="btn btn-default" data-edit="redo" title="Redo (Ctrl/Cmd+Y)"><i class="fa fa-repeat"></i></a>
					</div>
					<input class="pull-right voiceBtn" type="text" data-edit="inserttext" />
				</div>
			<div id="first-editor" class="lead" data-placeholder="This is a basic example with a simple toolbar."></div>
			<div class="editorPreview"></div>

			<br />
			<h3>Second Editor</h3>

			<div class="btn-toolbar" data-role="editor-toolbar" data-target="#second-editor">
					<div class="btn-group">
						<a class="btn btn-default dropdown-toggle" data-toggle="dropdown"
							title="Font"><i class="fa fa-font"></i><b class="caret"></b>
						</a>
						<ul class="dropdown-menu">
						</ul>
					</div>
					<div class="btn-group">
						<a class="btn btn-default dropdown-toggle" data-toggle="dropdown"
							title="Font Size"><i class="fa fa-text-height"></i>&nbsp;<b
							class="caret"></b>
						</a>
						<ul class="dropdown-menu">
							<li><a data-edit="fontSize 5" class="fs-Five">Huge</a></li>
							<li><a data-edit="fontSize 3" class="fs-Three">Normal</a></li>
							<li><a data-edit="fontSize 1" class="fs-One">Small</a></li>
						</ul>
					</div>
					<div class="btn-group">
						<a class="btn btn-default" data-edit="bold" title="Bold (Ctrl/Cmd+B)"><i class="fa fa-bold"></i></a>
						<a class="btn btn-default" data-edit="italic" title="Italic (Ctrl/Cmd+I)"><i class="fa fa-italic"></i></a>
						<a class="btn btn-default" data-edit="strikethrough" title="Strikethrough"><i class="fa fa-strikethrough"></i></a>
						<a class="btn btn-default" data-edit="underline" title="Underline (Ctrl/Cmd+U)"><i class="fa fa-underline"></i></a>
					</div>
					<div class="btn-group">
						<a class="btn btn-default" data-edit="insertunorderedlist" title="Bullet list"><i class="fa fa-list-ul"></i></a>
						<a class="btn btn-default" data-edit="insertorderedlist" title="Number list"><i class="fa fa-list-ol"></i></a>
						<a class="btn btn-default" data-edit="outdent" title="Reduce indent (Shift+Tab)"><i class="fa fa-outdent"></i></a>
						<a class="btn btn-default" data-edit="indent" title="Indent (Tab)"><i class="fa fa-indent"></i></a>
					</div>
					<div class="btn-group">
						<a class="btn btn-default" data-edit="justifyleft" title="Align Left (Ctrl/Cmd+L)"><i class="fa fa-align-left"></i></a>
						<a class="btn btn-default" data-edit="justifycenter" title="Center (Ctrl/Cmd+E)"><i class="fa fa-align-center"></i></a>
						<a class="btn btn-default" data-edit="justifyright" title="Align Right (Ctrl/Cmd+R)"><i class="fa fa-align-right"></i></a>
						<a class="btn btn-default" data-edit="justifyfull" title="Justify (Ctrl/Cmd+J)"><i class="fa fa-align-justify"></i></a>
					</div>
					<div class="btn-group">
						<a class="btn btn-default dropdown-toggle" data-toggle="dropdown" title="Hyperlink"><i class="fa fa-link"></i></a>
						<div class="dropdown-menu input-append">
							<input placeholder="URL" type="text" data-edit="createLink" />
							<button class="btn" type="button">Add</button>
						</div>
					</div>
					<div class="btn-group">
						<a class="btn btn-default" data-edit="unlink" title="Remove Hyperlink"><i class="fa fa-unlink"></i></a>
						<span class="btn btn-default" title="Insert picture (or just drag & drop)" id="pictureBtn"> <i class="fa fa-picture-o"></i>
							<input class="imgUpload" type="file" data-role="magic-overlay" data-target="#pictureBtn" data-edit="insertImage" />
						</span>
					</div>
					<div class="btn-group">
						<a class="btn btn-default" data-edit="undo" title="Undo (Ctrl/Cmd+Z)"><i class="fa fa-undo"></i></a>
						<a class="btn btn-default" data-edit="redo" title="Redo (Ctrl/Cmd+Y)"><i class="fa fa-repeat"></i></a>
					</div>
					<input class="pull-right voiceBtn" type="text" data-edit="inserttext" />
				</div>
			<div id="second-editor" class="lead" data-placeholder="This is a basic example with a simple toolbar."></div>
			<div class="editorPreview"></div>
		</div>

		<script src="http://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
		<script src="../bower_components/jquery.hotkeys/jquery.hotkeys.js"></script>
		<script src="http://netdna.bootstrapcdn.com/bootstrap/3.3.5/js/bootstrap.min.js"></script>
		<script src="../bower_components/google-code-prettify/src/prettify.js"></script>
		<script src="../src/bootstrap-wysiwyg.js"></script>
		<script type='text/javascript'>
			$('#first-editor').wysiwyg();
			$('#second-editor').wysiwyg();

			$(".dropdown-menu > input").click(function (e) {
        		e.stopPropagation();
    		});
		</script>
	</body>
</html>