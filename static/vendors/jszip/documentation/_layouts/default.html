<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Create .zip files using Javascript. Provides a simple API to place any content generated by Javascript into a .zip file for your users." />
    <title>{{page.title}}</title>

    <!-- Latest compiled and minified CSS -->
    <link rel="stylesheet" href="//netdna.bootstrapcdn.com/bootstrap/3.1.1/css/bootstrap.min.css">

    <!-- Optional theme -->
    <link rel="stylesheet" href="//netdna.bootstrapcdn.com/bootstrap/3.1.1/css/bootstrap-theme.min.css">

    <!-- Latest compiled and minified JavaScript -->
    <!-- <script src="//netdna.bootstrapcdn.com/bootstrap/3.1.1/js/bootstrap.min.js"></script> -->

    <link rel="stylesheet" href="{{site.baseurl}}/documentation/css/pygments.css">
    <link rel="stylesheet" href="{{site.baseurl}}/documentation/css/main.css">

    <script type="text/javascript" src="{{site.baseurl}}/dist/jszip.js"></script>

    <script type="text/javascript" src="//stuk.github.io/jszip-utils/dist/jszip-utils.js"></script>
    <!--
    Mandatory in IE 6, 7, 8 and 9.
    -->
    <!--[if IE]>
    <script type="text/javascript" src="//stuk.github.io/jszip-utils/dist/jszip-utils-ie.js"></script>
    <![endif]-->

    <!--
    Any version of jQuery will do (it's just to write some examples), this one
    happens to be available in our tests.
    -->
    <script type="text/javascript" src="{{site.baseurl}}/test/jquery-1.8.3.min.js"></script>

    <script type="text/javascript" src="{{site.baseurl}}/vendor/FileSaver.js"></script>
  </head>
  <body>
    <div class="container">
      <div class="navbar navbar-default" role="navigation">
        <div class="container-fluid">
          <div class="navbar-header">
            <a class="navbar-brand" href="{{site.baseurl}}/"><strong>JS</strong>Zip</a>
          </div>
          <ul class="nav navbar-nav">
            <li {% if page.section == "api" %}class="active"{% endif %}>
              <a href="{{site.baseurl}}/documentation/api_jszip.html">API</a>
            </li>
            <li {% if page.section == "example" %}class="active"{% endif %}>
              <a href="{{site.baseurl}}/documentation/examples.html">How to / examples</a>
            </li>
            <li {% if page.section == "limitations" %}class="active"{% endif %}>
              <a href="{{site.baseurl}}/documentation/limitations.html">Performances / limitations</a>
            </li>
          </ul>
          <ul class="nav navbar-nav navbar-right">
            <li>
              <a href="https://github.com/Stuk/jszip">JSZip on Github</a>
            </li>
          </ul>
        </div>
      </div>
      {% if page.section and page.fullpage != true %}
      <div class="row">
        <nav class="col-md-3">
        {% case page.section %}
        {% when "main" %}
        <h4>JSZip users :</h4>
        <ul class="nav">
              <li><a href="{{site.baseurl}}/">Installation</a></li>
              <li><a href="{{site.baseurl}}/documentation/faq.html">FAQ</a></li>
              <li><a href="{{site.baseurl}}/CHANGES.html">Changelog</a></li>
              <li><a href="{{site.baseurl}}/documentation/upgrade_guide.html">Upgrade guide</a></li>
              <li><a href="https://github.com/Stuk/jszip/issues">Bug tracker</a></li>
        </ul>
        <h4>JSZip developers :</h4>
        <ul class="nav">
              <li><a href="{{site.baseurl}}/documentation/contributing.html">How to contribute</a></li>
              <li><a href="https://github.com/Stuk/jszip/graphs/contributors">Contributors</a></li>
        </ul>
        {% when "api" %}
        <ul class="nav">
          <li><a href="{{site.baseurl}}/documentation/api_jszip.html">JSZip</a>
            <ul>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/constructor.html">new JSZip() or JSZip()</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/constructor_load.html">new JSZip(data [,options])</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/file_name.html">JSZip#file(name)</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/file_regex.html">JSZip#file(regex)</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/file_data.html">JSZip#file(name, data [,options])</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/folder_data.html">JSZip#folder(name)</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/folder_regex.html">JSZip#folder(regex)</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/filter.html">JSZip#filter(predicate)</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/remove.html">JSZip#remove(name)</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/generate.html">JSZip#generate(options)</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/load.html">JSZip#load(data [, options])</a></li>
              <li><a href="{{site.baseurl}}/documentation/api_jszip/support.html">JSZip.support</a></li>
            </ul>
          </li>
          <li><a href="{{site.baseurl}}/documentation/api_zipobject.html">ZipObject</a></li>
        </ul>
        {% when "example" %}
        <h4>How to ...</h4>
        <ul class="nav">
              <li><a href="{{site.baseurl}}/documentation/examples.html">Use JSZip</a></li>
              <li><a href="{{site.baseurl}}/documentation/howto/read_zip.html">Read a file</a></li>
              <li><a href="{{site.baseurl}}/documentation/howto/write_zip.html">Write a file</a></li>
        </ul>
        <h4>Examples</h4>
        <ul class="nav">
              <li><a href="{{site.baseurl}}/documentation/examples/read-local-file-api.html">Read local file</a></li>
              <li><a href="{{site.baseurl}}/documentation/examples/get-binary-files-ajax.html">Read remote file</a></li>
              <li><a href="{{site.baseurl}}/documentation/examples/download-zip-file.html">Give the user its zip file</a></li>
              <li><a href="{{site.baseurl}}/documentation/examples/downloader.html">Mini app : downloader</a></li>
        </ul>
        {% endcase %}
        <!-- <ul class="nav"> -->
          <!-- <li><a href="{{site.baseurl}}/documentation/faq.html">FAQ</a></li> -->
        <!-- </ul> -->
          <!-- <li><a href="{{site.baseurl}}/">installation</a></li> -->
          </nav>
        {% endif %}
        <div class="{% if page.section and page.fullpage != true%}col-md-9{% else %}col-md-12{% endif %}">
          <h1>{{page.title}}</h1>
          <!-- ===================== -->
          <!-- === C O N T E N T === -->
          <!-- ===================== -->

          {{content}}

          <!-- ===================== -->
          <!-- == / C O N T E N T == -->
          <!-- ===================== -->
        </div>
      </div>
    </div>
    <script>
      // FIXME find how to do that cleanly
      (function(){
        var tables = document.getElementsByTagName("table");
        for(var i = 0; i < tables.length; i++) {
          tables[i].className += " table table-condensed table-striped table-bordered ";
        }
      })();
    </script>
    <script>
      (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

      ga('create', 'UA-52085706-1', 'stuk.github.io');
      ga('send', 'pageview');

    </script>
  </body>
</html>
