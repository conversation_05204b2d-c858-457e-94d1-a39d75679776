{"name": "knob", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Nice, downward compatible, touchable, jQuery dial.", "keywords": ["dial", "button", "knob", "ui", "input"], "version": "1.2.13", "author": {"name": "<PERSON>", "url": "https://github.com/aterrien"}, "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://anthonyterrien.com/knob"}], "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/mit-license.php"}], "bugs": "https://github.com/aterrien/jQuery-Knob/issues", "homepage": "https://github.com/aterrien/jQ<PERSON><PERSON>-<PERSON>nob", "docs": "https://github.com/aterrien/jQ<PERSON><PERSON>-<PERSON>nob", "download": "https://github.com/aterrien/jQuery-Knob/tags", "dependencies": {"jquery": ">=1.7.0"}}