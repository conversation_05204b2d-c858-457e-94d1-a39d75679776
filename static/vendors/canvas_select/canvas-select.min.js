!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).CanvasSelect=e()}(this,(function(){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};function e(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function s(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(s.prototype=i.prototype,new s)}var i=function(){return i=Object.assign||function(t){for(var e,i=1,s=arguments.length;i<s;i++)for(var a in e=arguments[i])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i.apply(this,arguments)};function s(t,e){var i="function"==typeof Symbol&&t[Symbol.iterator];if(!i)return t;var s,a,h=i.call(t),n=[];try{for(;(void 0===e||e-- >0)&&!(s=h.next()).done;)n.push(s.value)}catch(t){a={error:t}}finally{try{s&&!s.done&&(i=h.return)&&i.call(h)}finally{if(a)throw a.error}}return n}function a(t,e,i){for(var s=!1,a=i.length,h=0,n=a-1;h<a;n=h++){var o=i[h][0],r=i[h][1],c=i[n][0],l=i[n][1];r>e!=l>e&&t<(c-o)*(e-r)/(l-r)+o&&(s=!s)}return s}var h=function(t,e){this.label="",this.coor=[],this.active=!1,this.creating=!1,this.dragging=!1,this.uuid=function(){for(var t=[],e="0123456789abcdef",i=0;i<36;i++){var s=Math.floor(16*Math.random());t[i]=e.slice(s,s+1)}t[14]="4";var a=3&t[19]|8;return t[19]=e.slice(a,a+1),t[8]=t[13]=t[18]=t[23]="-",t.join("")}(),this.index=e,Object.assign(this,t)},n=function(t){function i(e,i){var s=t.call(this,e,i)||this;return s.type=1,s}return e(i,t),Object.defineProperty(i.prototype,"ctrlsData",{get:function(){var t=s(this.coor,2),e=s(t[0],2),i=e[0],a=e[1],h=s(t[1],2),n=h[0],o=h[1];return[[i,a],[i+(n-i)/2,a],[n,a],[n,a+(o-a)/2],[n,o],[i+(n-i)/2,o],[i,o],[i,a+(o-a)/2]]},enumerable:!1,configurable:!0}),i}(h),o=function(t){function i(e,i){var s=t.call(this,e,i)||this;return s.type=2,s}return e(i,t),Object.defineProperty(i.prototype,"ctrlsData",{get:function(){return this.coor.length>2?this.coor:[]},enumerable:!1,configurable:!0}),i}(h),r=function(t){function i(e,i){var s=t.call(this,e,i)||this;return s.type=3,s}return e(i,t),i}(h),c=function(){function t(){this._eventTree={}}return t.prototype.on=function(t,e){var i=this._eventTree[t];Array.isArray(i)?i.push(e):this._eventTree[t]=[e]},t.prototype.emit=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var a=this._eventTree[t];Array.isArray(a)&&a.forEach((function(t){return t.apply(void 0,function(t,e){for(var i=0,s=e.length,a=t.length;i<s;i++,a++)t[a]=e[i];return t}([],s(e)))}))},t.prototype.off=function(t,e){var i=this._eventTree[t],s=i.find((function(t){return t===e}));Array.isArray(i)&&s&&i.splice(s,1)},t}(),l=function(t){function i(e,i){var s=t.call(this,e,i)||this;return s.type=4,s}return e(i,t),Object.defineProperty(i.prototype,"ctrlsData",{get:function(){return this.coor.length>1?this.coor:[]},enumerable:!1,configurable:!0}),i}(h),u=function(t){function i(e,i){var s=t.call(this,e,i)||this;return s.type=6,s}return e(i,t),Object.defineProperty(i.prototype,"ctrlsData",{get:function(){return this.coor.length>1?this.coor:[]},enumerable:!1,configurable:!0}),i}(h),d=function(t){function i(e,i){var s=t.call(this,e,i)||this;return s.type=5,s.radius=0,s.radius=e.radius||s.radius,s}return e(i,t),Object.defineProperty(i.prototype,"ctrlsData",{get:function(){var t=s(this.coor,2),e=t[0],i=t[1];return[[e,i-this.radius],[e+this.radius,i],[e,i+this.radius],[e-this.radius,i]]},enumerable:!1,configurable:!0}),i}(h),p="2.19.1",f=function(t){function h(e,i){var s=t.call(this)||this;s.version=p,s.lock=!1,s.readonly=!1,s.MIN_WIDTH=10,s.MIN_HEIGHT=10,s.MIN_RADIUS=5,s.strokeStyle="#0f0",s.fillStyle="rgba(0, 0, 255,0.1)",s.lineWidth=1,s.activeStrokeStyle="#f00",s.activeFillStyle="rgba(255, 0, 0,0.1)",s.ctrlStrokeStyle="#000",s.ctrlFillStyle="#fff",s.ctrlRadius=3,s.hideLabel=!1,s.labelFillStyle="#fff",s.labelFont="10px sans-serif",s.textFillStyle="#000",s.labelMaxLen=10,s.WIDTH=0,s.HEIGHT=0,s.dataset=[],s.remmberOrigin=[0,0],s.createType=0,s.ctrlIndex=-1,s.image=new Image,s.IMAGE_WIDTH=0,s.IMAGE_ORIGIN_HEIGHT=0,s.IMAGE_HEIGHT=0,s.originX=0,s.originY=0,s.scaleStep=0,s.scrollZoom=!0,s.dblTouch=300,s.dblTouchStore=0,s.alpha=!0,s.focusMode=!1,s.scaleTouchStore=0,s.isTouch2=!1,s.isMobile=navigator.userAgent.includes("Mobile"),s.labelUp=!1,s.handleLoad=s.handleLoad.bind(s),s.handleContextmenu=s.handleContextmenu.bind(s),s.handleMousewheel=s.handleMousewheel.bind(s),s.handleMouseDown=s.handleMouseDown.bind(s),s.handelMouseMove=s.handelMouseMove.bind(s),s.handelMouseUp=s.handelMouseUp.bind(s),s.handelDblclick=s.handelDblclick.bind(s),s.handelKeyup=s.handelKeyup.bind(s);var a="string"==typeof e?document.querySelector(e):e;return a instanceof HTMLCanvasElement?(s.canvas=a,s.offScreen=document.createElement("canvas"),s.initSetting(),s.initEvents(),i&&s.setImage(i)):console.warn("HTMLCanvasElement is required!"),s}return e(h,t),Object.defineProperty(h.prototype,"activeShape",{get:function(){return this.dataset.find((function(t){return t.active}))||{}},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"scale",{get:function(){return this.IMAGE_ORIGIN_WIDTH&&this.IMAGE_WIDTH?this.IMAGE_WIDTH/this.IMAGE_ORIGIN_WIDTH:1},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"imageMin",{get:function(){return Math.min(this.IMAGE_WIDTH,this.IMAGE_HEIGHT)},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"imageOriginMax",{get:function(){return Math.max(this.IMAGE_ORIGIN_WIDTH,this.IMAGE_ORIGIN_HEIGHT)},enumerable:!1,configurable:!0}),h.prototype.mergeEvent=function(t){var e=0,s=0,a=0,h=0;if(this.isMobile){var n=t.touches[0],o=n.clientX,r=n.clientY,c=t.target.getBoundingClientRect(),l=c.left,u=c.top;if(e=Math.round(o-l),s=Math.round(r-u),2===t.touches.length){var d=t.touches[1]||{},p=d.clientX,f=void 0===p?0:p,v=d.clientY,I=void 0===v?0:v;a=Math.round(Math.abs((f-o)/2+o)-l),h=Math.round(Math.abs((I-r)/2+r)-u)}}else e=t.offsetX,s=t.offsetY;return i(i({},t),{mouseX:e,mouseY:s,mouseCX:a,mouseCY:h})},h.prototype.handleLoad=function(){this.emit("load",this.image.src),this.IMAGE_ORIGIN_WIDTH=this.IMAGE_WIDTH=this.image.width,this.IMAGE_ORIGIN_HEIGHT=this.IMAGE_HEIGHT=this.image.height,this.fitZoom()},h.prototype.handleContextmenu=function(t){t.preventDefault(),this.evt=t,this.lock},h.prototype.handleMousewheel=function(t){},h.prototype.handleMouseDown=function(t){var e=this;if(t.stopPropagation(),this.evt=t,!this.lock){var i=this.mergeEvent(t),a=i.mouseX,h=i.mouseY,c=i.mouseCX,p=i.mouseCY,f=Math.round(a/this.scale),v=Math.round(h/this.scale);if(this.mouse=this.isMobile&&2===t.touches.length?[c,p]:[a,h],this.remmberOrigin=[a-this.originX,h-this.originY],!this.isMobile&&1===t.buttons||this.isMobile&&1===t.touches.length){var I=this.activeShape.ctrlsData||[];if(this.ctrlIndex=I.findIndex((function(t){return e.isPointInCircle(e.mouse,t,e.ctrlRadius)})),this.ctrlIndex>-1&&!this.readonly){var y=s(I[this.ctrlIndex],2),g=y[0],S=y[1];this.remmber=[[f-g,v-S]]}else if(this.isInBackground(t)){if(this.activeShape.creating&&!this.readonly){if([2,4,6].includes(this.activeShape.type)){var M=s(this.activeShape.coor[this.activeShape.coor.length-1],2),m=M[0],b=M[1];if(m!==f&&b!==v){var x=Math.round(f-this.originX/this.scale),H=Math.round(v-this.originY/this.scale);this.activeShape.coor.push([x,H])}}}else if(this.createType>0&&!this.readonly){var T=void 0,E=[x=Math.round(f-this.originX/this.scale),H=Math.round(v-this.originY/this.scale)];switch(this.createType){case 1:(T=new n({coor:[E,E]},this.dataset.length)).creating=!0;break;case 2:(T=new o({coor:[E]},this.dataset.length)).creating=!0;break;case 3:T=new r({coor:E},this.dataset.length),this.emit("add",T);break;case 4:(T=new l({coor:[E]},this.dataset.length)).creating=!0;break;case 6:(T=new u({coor:[E]},this.dataset.length)).creating=!0;break;case 5:(T=new d({coor:E},this.dataset.length)).creating=!0}this.dataset.forEach((function(t){t.active=!1})),T.active=!0,this.dataset.push(T)}else{var G=s(this.hitOnShape(this.mouse),2),_=G[0],w=G[1];if(_>-1){if(w.dragging=!0,this.dataset.forEach((function(t,e){return t.active=e===_})),this.dataset.splice(_,1),this.dataset.push(w),!this.readonly)if(this.remmber=[],[3,5].includes(w.type)){var D=s(w.coor,2);m=D[0],b=D[1];this.remmber=[[f-m,v-b]]}else w.coor.forEach((function(t){e.remmber.push([f-t[0],v-t[1]])}));this.emit("select",w)}else this.activeShape.active=!1,this.dataset.sort((function(t,e){return t.index-e.index})),this.emit("select",null)}this.update()}}}},h.prototype.handelMouseMove=function(t){if(t.stopPropagation(),this.evt=t,!this.lock){var e=this.mergeEvent(t),i=e.mouseX,a=e.mouseY,h=e.mouseCX,n=e.mouseCY,o=Math.round(i/this.scale),r=Math.round(a/this.scale);if(this.mouse=this.isMobile&&2===t.touches.length?[h,n]:[i,a],(!this.isMobile&&1===t.buttons||this.isMobile&&1===t.touches.length)&&this.activeShape.type){if(this.ctrlIndex>-1&&this.remmber.length&&(this.isInBackground(t)||5===this.activeShape.type)){var c=s(this.remmber,1),l=s(c[0],2),u=l[0],d=l[1];if(1===this.activeShape.type){var p=s(this.activeShape.coor,2),f=s(p[0],2),v=f[0],I=f[1],y=s(p[1],2),g=y[0],S=y[1],M=[];switch(this.ctrlIndex){case 0:M=[[o-u,r-d],[g,S]];break;case 1:M=[[v,r-d],[g,S]];break;case 2:M=[[v,r-d],[o-u,S]];break;case 3:M=[[v,I],[o-u,S]];break;case 4:M=[[v,I],[o-u,r-d]];break;case 5:M=[[v,I],[g,r-d]];break;case 6:M=[[o-u,I],[g,r-d]];break;case 7:M=[[o-u,I],[g,S]]}var m=s(M,2),b=s(m[0],2),x=b[0],H=b[1],T=s(m[1],2),E=T[0],G=T[1];(x<0||E<0||H<0||G<0||E>this.IMAGE_ORIGIN_WIDTH||G>this.IMAGE_ORIGIN_HEIGHT)&&(x<0&&(x=0),E<0&&(E=0),H<0&&(H=0),G<0&&(G=0),E>this.IMAGE_ORIGIN_WIDTH&&(E=this.IMAGE_ORIGIN_WIDTH),G>this.IMAGE_ORIGIN_HEIGHT&&(G=this.IMAGE_ORIGIN_HEIGHT)),E-x>=this.MIN_WIDTH&&G-H>=this.MIN_HEIGHT?this.activeShape.coor=[[x,H],[E,G]]:this.emit("warn","Width cannot be less than ".concat(this.MIN_WIDTH,",Height cannot be less than").concat(this.MIN_HEIGHT,"。"))}else if([2,4,6].includes(this.activeShape.type)){var _=[Math.round(o-this.originX/this.scale),Math.round(r-this.originY/this.scale)];this.activeShape.coor.splice(this.ctrlIndex,1,_)}else if(5===this.activeShape.type){var w=Math.round(o-this.originX/this.scale)-this.activeShape.coor[0];w>=this.MIN_RADIUS&&(this.activeShape.radius=w)}}else if(this.activeShape.dragging&&!this.readonly){M=[];var D=!0,k=this.IMAGE_ORIGIN_WIDTH||this.WIDTH,W=this.IMAGE_ORIGIN_HEIGHT||this.HEIGHT;if([3,5].includes(this.activeShape.type)){var A=s(this.remmber[0],2),L=A[0];d=r-A[1];((u=o-L)<0||u>k||d<0||d>W)&&(D=!1),M=[u,d]}else for(var O=0;O<this.activeShape.coor.length;O++){var P=this.remmber[O];u=o-P[0],d=r-P[1];(u<0||u>k||d<0||d>W)&&(D=!1),M.push([u,d])}D&&(this.activeShape.coor=M)}else if(this.activeShape.creating&&this.isInBackground(t)){u=Math.round(o-this.originX/this.scale),d=Math.round(r-this.originY/this.scale);if(1===this.activeShape.type)this.activeShape.coor.splice(1,1,[u,d]);else if(5===this.activeShape.type){var C=s(this.activeShape.coor,2),R=(v=C[0],I=C[1],Math.sqrt(Math.pow(v-u,2)+Math.pow(I-d,2)));this.activeShape.radius=R}}this.update()}else if([2,4,6].includes(this.activeShape.type)&&this.activeShape.creating)this.update();else if(!this.isMobile&&2===t.buttons&&3===t.which||this.isMobile&&1===t.touches.length&&!this.isTouch2)this.originX=Math.round(i-this.remmberOrigin[0]),this.originY=Math.round(a-this.remmberOrigin[1]),this.update();else if(this.isMobile&&2===t.touches.length){this.isTouch2=!0;var N=t.touches[0],X=t.touches[1],Y=this.scaleTouchStore;this.scaleTouchStore=Math.abs((X.clientX-N.clientX)*(X.clientY-N.clientY)),this.setScale(this.scaleTouchStore>Y,!0)}}},h.prototype.handelMouseUp=function(t){if(t.stopPropagation(),this.evt=t,!this.lock){if(this.isMobile){if(0===t.touches.length&&(this.isTouch2=!1),Date.now()-this.dblTouchStore<this.dblTouch)return void this.handelDblclick(t);this.dblTouchStore=Date.now()}if(this.remmber=[],this.activeShape.type&&(this.activeShape.dragging=!1,this.activeShape.creating)){if(1===this.activeShape.type){var e=s(this.activeShape.coor,2),i=s(e[0],2),a=i[0],h=i[1],n=s(e[1],2),o=n[0],r=n[1];Math.abs(a-o)<this.MIN_WIDTH||Math.abs(h-r)<this.MIN_HEIGHT?(this.dataset.pop(),this.emit("warn","Width cannot be less than ".concat(this.MIN_WIDTH,",Height cannot be less than ").concat(this.MIN_HEIGHT))):(this.activeShape.coor=[[Math.min(a,o),Math.min(h,r)],[Math.max(a,o),Math.max(h,r)]],this.activeShape.creating=!1,this.emit("add",this.activeShape))}else 5===this.activeShape.type&&(this.activeShape.radius<this.MIN_RADIUS?(this.dataset.pop(),this.emit("warn","Radius cannot be less than ".concat(this.MIN_WIDTH))):(this.activeShape.creating=!1,this.emit("add",this.activeShape)));this.update()}}},h.prototype.handelDblclick=function(t){t.stopPropagation(),this.evt=t,this.lock||[2,4,6].includes(this.activeShape.type)&&(2===this.activeShape.type&&this.activeShape.coor.length>2||4===this.activeShape.type&&this.activeShape.coor.length>1||6===this.activeShape.type&&this.activeShape.coor.length>1)&&(this.emit("add",this.activeShape),this.activeShape.creating=!1,this.update())},h.prototype.handelKeyup=function(t){t.stopPropagation(),this.evt=t,this.lock||document.activeElement!==document.body||this.readonly||this.activeShape.type&&([2,4,6].includes(this.activeShape.type)&&"Escape"===t.key?(this.activeShape.coor.length>1&&this.activeShape.creating?this.activeShape.coor.pop():this.deleteByIndex(this.activeShape.index),this.update()):"Backspace"===t.key&&this.deleteByIndex(this.activeShape.index))},h.prototype.initSetting=function(){var t=window.devicePixelRatio||1;this.canvas.style.userSelect="none",this.ctx=this.ctx||this.canvas.getContext("2d",{alpha:this.alpha}),this.WIDTH=this.canvas.clientWidth,this.HEIGHT=this.canvas.clientHeight,this.canvas.width=this.WIDTH*t,this.canvas.height=this.HEIGHT*t,this.canvas.style.width=this.WIDTH+"px",this.canvas.style.height=this.HEIGHT+"px",this.offScreen.width=this.WIDTH,this.offScreen.height=this.HEIGHT,this.offScreenCtx=this.offScreenCtx||this.offScreen.getContext("2d",{willReadFrequently:!0}),this.ctx.scale(t,t)},h.prototype.initEvents=function(){this.image.addEventListener("load",this.handleLoad),this.canvas.addEventListener("touchstart",this.handleMouseDown),this.canvas.addEventListener("touchmove",this.handelMouseMove),this.canvas.addEventListener("touchend",this.handelMouseUp),this.canvas.addEventListener("contextmenu",this.handleContextmenu),this.canvas.addEventListener("mousewheel",this.handleMousewheel),this.canvas.addEventListener("mousedown",this.handleMouseDown),this.canvas.addEventListener("mousemove",this.handelMouseMove),this.canvas.addEventListener("mouseup",this.handelMouseUp),this.canvas.addEventListener("dblclick",this.handelDblclick),document.body.addEventListener("keyup",this.handelKeyup,!0)},h.prototype.setImage=function(t){this.image.src=t},h.prototype.setData=function(t){var e=this;setTimeout((function(){var i=[];t.forEach((function(t,e){if(Object.prototype.toString.call(t).includes("Object")){var s=void 0;switch(t.type){case 1:s=new n(t,e);break;case 2:s=new o(t,e);break;case 3:s=new r(t,e);break;case 4:s=new l(t,e);break;case 6:s=new u(t,e);break;case 5:s=new d(t,e);break;default:console.warn("Invalid shape",t)}[1,2,3,4,5,6].includes(t.type)&&i.push(s)}else console.warn("Shape must be an enumerable Object.",t)})),e.dataset=i,e.update()}))},h.prototype.hitOnShape=function(t){for(var e,i=-1,s=this.dataset.length-1;s>-1;s--){var a=this.dataset[s];if(3===a.type&&this.isPointInCircle(t,a.coor,this.ctrlRadius)||5===a.type&&this.isPointInCircle(t,a.coor,a.radius*this.scale)||1===a.type&&this.isPointInRect(t,a.coor)||2===a.type&&this.isPointInPolygon(t,a.coor)||4===a.type&&this.isPointInLine(t,a.coor)||6===a.type&&this.isPointInLine(t,a.coor)){if(this.focusMode&&!a.active)continue;i=s,e=a;break}}return[i,e]},h.prototype.isInBackground=function(t){var e=this.mergeEvent(t),i=e.mouseX,s=e.mouseY;return i>=this.originX&&s>=this.originY&&i<=this.originX+this.IMAGE_ORIGIN_WIDTH*this.scale&&s<=this.originY+this.IMAGE_ORIGIN_HEIGHT*this.scale},h.prototype.isPointInRect=function(t,e){var i=this,a=s(t,2),h=a[0],n=a[1],o=s(e.map((function(t){return t.map((function(t){return t*i.scale}))})),2),r=s(o[0],2),c=r[0],l=r[1],u=s(o[1],2),d=u[0],p=u[1];return c+this.originX<=h&&h<=d+this.originX&&l+this.originY<=n&&n<=p+this.originY},h.prototype.isPointInPolygon=function(t,e){var i=this;this.offScreenCtx.save(),this.offScreenCtx.clearRect(0,0,this.WIDTH,this.HEIGHT),this.offScreenCtx.translate(this.originX,this.originY),this.offScreenCtx.beginPath(),e.forEach((function(t,e){var a=s(t.map((function(t){return Math.round(t*i.scale)})),2),h=a[0],n=a[1];0===e?i.offScreenCtx.moveTo(h,n):i.offScreenCtx.lineTo(h,n)})),this.offScreenCtx.closePath(),this.offScreenCtx.fill();var a=this.offScreenCtx.getImageData(0,0,this.WIDTH,this.HEIGHT),h=(t[1]-1)*this.WIDTH*4+4*t[0];return this.offScreenCtx.restore(),0!==a.data[h+3]},h.prototype.isPointInCircle=function(t,e,i){var a=this,h=s(t,2),n=h[0],o=h[1],r=s(e.map((function(t){return t*a.scale})),2),c=r[0],l=r[1];return Math.sqrt(Math.pow(c+this.originX-n,2)+Math.pow(l+this.originY-o,2))<=i},h.prototype.isPointInLine=function(t,e){var i=this;this.offScreenCtx.save(),this.offScreenCtx.clearRect(0,0,this.WIDTH,this.HEIGHT),this.offScreenCtx.translate(this.originX,this.originY),this.offScreenCtx.lineWidth=5,this.offScreenCtx.beginPath(),e.forEach((function(t,e){var a=s(t.map((function(t){return Math.round(t*i.scale)})),2),h=a[0],n=a[1];0===e?i.offScreenCtx.moveTo(h,n):i.offScreenCtx.lineTo(h,n)})),this.offScreenCtx.stroke();var a=this.offScreenCtx.getImageData(0,0,this.WIDTH,this.HEIGHT),h=(t[1]-1)*this.WIDTH*4+4*t[0];return this.offScreenCtx.restore(),0!==a.data[h+3]},h.prototype.isNested=function(t,e){return function(t,e){if(1===t.type&&1===e.type){var i=s(t.coor,2),h=s(i[0],2),n=h[0],o=h[1],r=s(i[1],2),c=r[0],l=r[1],u=s(e.coor,2),d=s(u[0],2),p=d[0],f=d[1],v=s(u[1],2),I=v[0],y=v[1];return n<=p&&o<=f&&c>=I&&l>=y}if(1===t.type&&2===e.type){for(var g=s(t.coor,2),S=s(g[0],2),M=(n=S[0],o=S[1],s(g[1],2)),m=(c=M[0],l=M[1],e.coor),b=0;b<m.length;b++){var x=s(m[b],2),H=x[0],T=x[1];if(H<n||H>c||T<o||T>l)return!1}return!0}if(2===t.type&&1===e.type){for(m=e.coor,b=0;b<m.length;b++){var E=s(m[b],2);if(!a(H=E[0],T=E[1],t.coor))return!1}return!0}if(2===t.type&&2===e.type){var G=t.coor,_=e.coor;for(b=0;b<_.length;b++){var w=s(_[b],2);if(!a(H=w[0],T=w[1],G))return!1}return!0}}(t,e)},h.prototype.drawRect=function(t){var e=this;if(2===t.coor.length){var i=t.strokeStyle,a=t.fillStyle,h=t.active,n=t.creating,o=t.coor,r=t.lineWidth,c=s(o.map((function(t){return t.map((function(t){return Math.round(t*e.scale)}))})),2),l=s(c[0],2),u=l[0],d=l[1],p=s(c[1],2),f=p[0],v=p[1];this.ctx.save(),this.ctx.lineWidth=r||this.lineWidth,this.ctx.fillStyle=a||this.fillStyle,this.ctx.strokeStyle=h||n?this.activeStrokeStyle:i||this.strokeStyle;var I=f-u,y=v-d;n||this.ctx.fillRect(u,d,I,y),this.ctx.strokeRect(u,d,I,y),this.ctx.restore(),this.drawLabel(o[0],t)}},h.prototype.drawPolygon=function(t){var e=this,i=t.strokeStyle,a=t.fillStyle,h=t.active,n=t.creating,o=t.coor,r=t.lineWidth;if(this.ctx.save(),this.ctx.lineJoin="round",this.ctx.lineWidth=r||this.lineWidth,this.ctx.fillStyle=a||this.fillStyle,this.ctx.strokeStyle=h||n?this.activeStrokeStyle:i||this.strokeStyle,this.ctx.beginPath(),o.forEach((function(t,i){var a=s(t.map((function(t){return Math.round(t*e.scale)})),2),h=a[0],n=a[1];0===i?e.ctx.moveTo(h,n):e.ctx.lineTo(h,n)})),n){var c=s(this.mouse||[],2),l=c[0],u=c[1];this.ctx.lineTo(l-this.originX,u-this.originY)}else o.length>2&&this.ctx.closePath();this.ctx.fill(),this.ctx.stroke(),this.ctx.restore(),this.drawLabel(o[0],t)},h.prototype.drawDot=function(t){var e=this,i=t.strokeStyle,a=t.fillStyle,h=t.active,n=t.coor,o=t.lineWidth,r=s(n.map((function(t){return t*e.scale})),2),c=r[0],l=r[1];this.ctx.save(),this.ctx.lineWidth=o||this.lineWidth,this.ctx.fillStyle=a||this.ctrlFillStyle,this.ctx.strokeStyle=h?this.activeStrokeStyle:i||this.strokeStyle,this.ctx.beginPath(),this.ctx.arc(c,l,this.ctrlRadius,0,2*Math.PI,!0),this.ctx.fill(),this.ctx.arc(c,l,this.ctrlRadius,0,2*Math.PI,!0),this.ctx.stroke(),this.ctx.restore(),this.drawLabel(n,t)},h.prototype.drawCirle=function(t){var e=this,i=t.strokeStyle,a=t.fillStyle,h=t.active,n=t.coor;t.label;var o=t.creating,r=t.radius,c=t.ctrlsData,l=t.lineWidth,u=s(n.map((function(t){return t*e.scale})),2),d=u[0],p=u[1];this.ctx.save(),this.ctx.lineWidth=l||this.lineWidth,this.ctx.fillStyle=a||this.fillStyle,this.ctx.strokeStyle=h||o?this.activeStrokeStyle:i||this.strokeStyle,this.ctx.beginPath(),this.ctx.arc(d,p,r*this.scale,0,2*Math.PI,!0),this.ctx.fill(),this.ctx.arc(d,p,r*this.scale,0,2*Math.PI,!0),this.ctx.stroke(),this.ctx.restore(),this.drawLabel(c[0],t)},h.prototype.drawLine=function(t){var e=this,i=t.strokeStyle,a=t.active,h=t.creating,n=t.coor,o=t.lineWidth;if(this.ctx.save(),this.ctx.lineJoin="round",this.ctx.lineWidth=o||this.lineWidth,this.ctx.strokeStyle=a||h?this.activeStrokeStyle:i||this.strokeStyle,this.ctx.beginPath(),n.forEach((function(t,i){var a=s(t.map((function(t){return Math.round(t*e.scale)})),2),h=a[0],n=a[1];0===i?e.ctx.moveTo(h,n):e.ctx.lineTo(h,n)})),h){var r=s(this.mouse||[],2),c=r[0],l=r[1];this.ctx.lineTo(c-this.originX,l-this.originY)}this.ctx.stroke(),this.ctx.restore(),this.drawLabel(n[0],t)},h.prototype.drawLineArr=function(t){var e,i,a=this,h=t.strokeStyle,n=t.active,o=t.creating,r=t.coor,c=t.lineWidth;if(this.ctx.save(),this.ctx.lineJoin="round",this.ctx.lineWidth=c||this.lineWidth,this.ctx.strokeStyle=n||o?this.activeStrokeStyle:h||this.strokeStyle,this.ctx.beginPath(),r.forEach((function(t,h){var n=s(t.map((function(t){return Math.round(t*a.scale)})),2),o=n[0],r=n[1];0===h?(a.ctx.moveTo(o,r),e=o,i=r):(a.ctx.lineTo(o,r),a.drawLineArrow(a.ctx,e,i,o,r,a.ctx.strokeStyle))})),o){var l=s(this.mouse||[],2),u=l[0],d=l[1];this.ctx.lineTo(u-this.originX,d-this.originY),this.drawLineArrow(this.ctx,e,i,u-this.originX,d-this.originY,this.ctx.strokeStyle)}this.ctx.stroke(),this.ctx.restore(),this.drawLabel(r[0],t)},h.prototype.drawLineArrow=function(t,e,i,s,a,h){var n,o,r=180*Math.atan2(i-a,e-s)/Math.PI,c=(r+45)*Math.PI/180,l=(r-45)*Math.PI/180,u=10*Math.cos(c),d=10*Math.sin(c),p=10*Math.cos(l),f=10*Math.sin(l);n=s+u,o=a+d,t.moveTo(n,o),t.lineTo(s,a),n=s+p,o=a+f,t.lineTo(n,o),t.strokeStyle=h},h.prototype.drawCtrl=function(t){var e=this,i=s(t.map((function(t){return t*e.scale})),2),a=i[0],h=i[1];this.ctx.save(),this.ctx.beginPath(),this.ctx.fillStyle=this.ctrlFillStyle,this.ctx.strokeStyle=this.ctrlStrokeStyle,this.ctx.arc(a,h,this.ctrlRadius,0,2*Math.PI,!0),this.ctx.fill(),this.ctx.arc(a,h,this.ctrlRadius,0,2*Math.PI,!0),this.ctx.stroke(),this.ctx.restore()},h.prototype.drawCtrlList=function(t){var e=this;t.ctrlsData.forEach((function(i,s){5===t.type?1===s&&e.drawCtrl(i):e.drawCtrl(i)}))},h.prototype.drawLabel=function(t,e){var i=this,a=e.label,h=void 0===a?"":a,n=e.labelFillStyle,o=void 0===n?"":n,r=e.labelFont,c=void 0===r?"":r,l=e.textFillStyle,u=void 0===l?"":l,d=e.hideLabel,p=e.labelUp,f=e.lineWidth,v="boolean"==typeof d?d:this.hideLabel,I="boolean"==typeof p?p:this.labelUp,y=f||this.lineWidth;if(h.length&&!v){this.ctx.font=c||this.labelFont;var g=h.length<this.labelMaxLen+1?h:"".concat(h.slice(0,this.labelMaxLen),"..."),S=this.ctx.measureText(g),M=parseInt(this.ctx.font)-4,m=S.width+8,b=M+8,x=s(t.map((function(t){return t*i.scale})),2),H=x[0],T=x[1],E=this.IMAGE_ORIGIN_WIDTH-t[0]<m/this.scale,G=this.IMAGE_ORIGIN_HEIGHT-t[1]<b/this.scale,_=t[1]>b/this.scale,w=I?_:G;this.ctx.save(),this.ctx.fillStyle=o||this.labelFillStyle,this.ctx.fillRect(E?H-S.width-4-y/2:H+y/2,w?T-b-y/2:T+y/2,m,b),this.ctx.fillStyle=u||this.textFillStyle,this.ctx.fillText(g,E?H-S.width:H+4+y/2,w?T-b+M+4:T+M+4+y/2,180),this.ctx.restore()}},h.prototype.update=function(){var t=this;window.cancelAnimationFrame(this.timer),this.timer=window.requestAnimationFrame((function(){t.ctx.save(),t.ctx.clearRect(0,0,t.WIDTH,t.HEIGHT),t.ctx.translate(t.originX,t.originY),t.IMAGE_WIDTH&&t.IMAGE_HEIGHT&&t.ctx.drawImage(t.image,0,0,t.IMAGE_WIDTH,t.IMAGE_HEIGHT);for(var e=t.focusMode?t.activeShape.type?[t.activeShape]:[]:t.dataset,i=0;i<e.length;i++){var s=e[i];if(!s.hide)switch(s.type){case 1:t.drawRect(s);break;case 2:t.drawPolygon(s);break;case 3:t.drawDot(s);break;case 4:t.drawLine(s);break;case 6:t.drawLineArr(s);break;case 5:t.drawCirle(s)}}[1,2,4,5,6].includes(t.activeShape.type)&&!t.activeShape.hide&&t.drawCtrlList(t.activeShape),t.ctx.restore(),t.emit("updated",t.dataset)}))},h.prototype.deleteByIndex=function(t){var e=this.dataset.findIndex((function(e){return e.index===t}));e>-1&&(this.emit("delete",this.dataset[e]),this.dataset.splice(e,1),this.dataset.forEach((function(t,e){t.index=e})),this.update())},h.prototype.calcStep=function(t){void 0===t&&(t=""),this.IMAGE_WIDTH<this.WIDTH&&this.IMAGE_HEIGHT<this.HEIGHT&&(""!==t&&"b"!==t||(this.setScale(!0,!1,!0),this.calcStep("b"))),(this.IMAGE_WIDTH>this.WIDTH||this.IMAGE_HEIGHT>this.HEIGHT)&&(""!==t&&"s"!==t||(this.setScale(!1,!1,!0),this.calcStep("s")))},h.prototype.setScale=function(t,e,i){if(void 0===e&&(e=!1),void 0===i&&(i=!1),!this.lock&&!(!t&&this.imageMin<20||t&&this.IMAGE_WIDTH>100*this.imageOriginMax)){t?this.scaleStep++:this.scaleStep--;var a=0,h=0,n=s(this.mouse||[],2),o=n[0],r=n[1];e&&(a=(o-this.originX)/this.scale,h=(r-this.originY)/this.scale);var c=Math.abs(this.scaleStep),l=this.IMAGE_WIDTH;if(this.IMAGE_WIDTH=Math.round(this.IMAGE_ORIGIN_WIDTH*Math.pow(this.scaleStep>=0?1.05:.95,c)),this.IMAGE_HEIGHT=Math.round(this.IMAGE_ORIGIN_HEIGHT*Math.pow(this.scaleStep>=0?1.05:.95,c)),e)this.originX=o-a*this.scale,this.originY=r-h*this.scale;else{var u=this.IMAGE_WIDTH/l;this.originX=this.WIDTH/2-(this.WIDTH/2-this.originX)*u,this.originY=this.HEIGHT/2-(this.HEIGHT/2-this.originY)*u}i||this.update()}},h.prototype.fitZoom=function(){this.calcStep(),this.IMAGE_HEIGHT/this.IMAGE_WIDTH>=this.HEIGHT/this.WIDTH?(this.IMAGE_WIDTH=this.IMAGE_ORIGIN_WIDTH/(this.IMAGE_ORIGIN_HEIGHT/this.HEIGHT),this.IMAGE_HEIGHT=this.HEIGHT):(this.IMAGE_WIDTH=this.WIDTH,this.IMAGE_HEIGHT=this.IMAGE_ORIGIN_HEIGHT/(this.IMAGE_ORIGIN_WIDTH/this.WIDTH)),this.originX=(this.WIDTH-this.IMAGE_WIDTH)/2,this.originY=(this.HEIGHT-this.IMAGE_HEIGHT)/2,this.update()},h.prototype.setFocusMode=function(t){this.focusMode=t,this.update()},h.prototype.destroy=function(){this.image.removeEventListener("load",this.handleLoad),this.canvas.removeEventListener("contextmenu",this.handleContextmenu),this.canvas.removeEventListener("mousewheel",this.handleMousewheel),this.canvas.removeEventListener("mousedown",this.handleMouseDown),this.canvas.removeEventListener("touchend",this.handleMouseDown),this.canvas.removeEventListener("mousemove",this.handelMouseMove),this.canvas.removeEventListener("touchmove",this.handelMouseMove),this.canvas.removeEventListener("mouseup",this.handelMouseUp),this.canvas.removeEventListener("touchend",this.handelMouseUp),this.canvas.removeEventListener("dblclick",this.handelDblclick),document.body.removeEventListener("keyup",this.handelKeyup,!0),this.canvas.width=this.WIDTH,this.canvas.height=this.HEIGHT,this.canvas.style.width=null,this.canvas.style.height=null,this.canvas.style.userSelect=null},h.prototype.resize=function(){this.canvas.width=null,this.canvas.height=null,this.canvas.style.width=null,this.canvas.style.height=null,this.initSetting(),this.update()},h}(c);return f}));
//# sourceMappingURL=canvas-select.min.js.map
