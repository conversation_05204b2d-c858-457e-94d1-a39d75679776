{"name": "icheck", "version": "1.0.2", "title": "iCheck", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fronteed.com/"}, "licenses": [{"type": "MIT", "url": "http://en.wikipedia.org/wiki/MIT_License"}], "dependencies": {"jquery": ">=1.7"}, "description": "Highly customizable checkboxes and radio buttons (jQuery and Zepto). Features: identical inputs across different browsers and devices (both desktop and mobile), touch devices support (iOS, Android, BlackBerry, Windows Phone, Amazon Kindle), keyboard accessible inputs (Tab, <PERSON><PERSON>, Arrow up/down and other shortcuts), screenreader accessible inputs — (ARIA attributes for VoiceOver and others), customization freedom (use any HTML and CSS to style inputs or try 6 Retina-ready skins), lightweight size (1 kb gzipped). Provides 32 options to customize checkboxes and radio buttons, 11 callbacks to handle changes, 9 methods to make changes programmatically. Saves changes to original inputs, works carefully with any selectors.", "keywords": ["checkbox", "radio", "input", "field", "form", "desktop", "mobile", "custom", "replacement", "accessibility", "skins", "ui", "checked", "disabled", "indeterminate", "css3", "html5", "tiny", "lightweight", "j<PERSON>y", "zepto"], "homepage": "http://fronteed.com/iCheck/", "docs": "https://github.com/fronteed/iCheck", "demo": "http://fronteed.com/iCheck/", "download": "http://fronteed.com/iCheck/", "bugs": "https://github.com/fronteed/iCheck/issues/"}