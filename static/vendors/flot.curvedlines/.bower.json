{"name": "flot.curvedlines", "main": "curvedLines.js", "authors": ["<PERSON>"], "license": "MIT", "ignore": ["**/*", "!curvedLines.js", "!bower.json"], "dependencies": {"flot": ">=0.8.0"}, "homepage": "https://github.com/MichaelZinsmaier/CurvedLines", "version": "1.1.1", "_release": "1.1.1", "_resolution": {"type": "version", "tag": "1.1.1", "commit": "2fbf588e8c1fc2cafad13fcc39b54aabb1f1e460"}, "_source": "https://github.com/MichaelZinsmaier/CurvedLines.git", "_target": "^1.1.1", "_originalSource": "flot.curvedlines", "_direct": true}