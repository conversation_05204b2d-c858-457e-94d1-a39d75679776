jQuery Sparkline Plugin ChangeLog
http://omnipotent.net/jquery.sparkline/

2.1.x ???

+ Fix for IE <= 9 for loading CSS when 31 stylesheets are already present


2.1.2 15/June/2013

+ Make compatible with jQuery 1.10

+ Fix intermittent "undefined error" with IE8 (issue 64)
Thanks <PERSON>

+ Performance/minification size improvement (PR 34)
Thanks <PERSON>

+ Prevent the plugin from being instatiated more than once (PR 37)
Thanks <PERSON>


2.1.1 26/January/2013

+ Fix a couple of issues rendering pie charts in IE when they slices that make
up a negligible percentage of the whole chart (issue 49)

+ Don't display line chart final marker if final value is null (issue 46)

+ Make compatible with jQuery 1.9.0 (issue 60)


2.1 15/October/2012

+ Added support for Internet Explorer 10 (issue 18)
Thanks <PERSON>

+ Enable the plugin to be loaded dynamically (issue 6)
Thanks joeenzminger

+ Pie charts with zero in the array of values will no longer
cause IE to display "undefined" in the page (issue 9)

+ Null values in bar charts are now correctly omitted by default (issue 23)

+ Null values in line charts would cause value highligting to be
incorrect (issue 4)

+ Raise default z-index for tooltips to ensure they always appear on top
of the chart (issue 5)

+ Allow target and performance values to be set to null for bullet charts,
omitting them from the chart (issue 3)

+ Fix clipping of highlight marker spots if min/max/final spots are disabled 
in line charts (issue 31)

+ Line charts with normalRangeMin=0 will now render the normal range 
correctly (issue 7)
Thanks Tim Mathewso

+ Add support for AMD based module loaders such as requirejs (PR 32)
Thanks Tim Tucker

+ Fix interquartile range calculation used by box plots (PR 10)
Thanks Claudio Riva


2.0 29/April/2012

+ jQuery.sparkline now requires jQuery 1.4.3 or later

+ All charts now support mouseover and click actions
Adds options: disableInteraction, disableTooltips, disableHighlight

+ Added sparklineClick and sparklineRegionChange events

+ Line charts have highlightSpotColor and highlightLineColor options
for controlling mouseover highligiting

+ All charts display tooltips and highlight values when moused-over by default
Adds options: tooltipContainer, tooltipOffsetX, tooltipOffsetY, tooltipFormatter,
tooltipChartTitle, tooltipFormat, tooltipPrefix, tooltipSuffix, 
tooltipSkipNull, tooltipValueLookups, highlightLighten, numberFormatter, 
numberDigitGroupSep, numberDecimalMark, numberDigitGroupCount

+ Fix error when all null values are fed to a line chart
Thanks Ton Voon

+ Fix off-by-one error that caused the bottom of filled line charts to
be unlevel - Thanks Sylvain Vieujot for the bug report

+ Pie charts with a single value now render correctly
Thanks Juan Manuel

+ Charts that have their dimensions specified exactly in pixels may see 
improved performance
Thanks Sean Carpenter

+ Coloured spots may be added to some or all values on a line chart now using
the new valueSpots option
Thanks Russ Beebe for the suggestion

+ Bar and tristates charts now support using range of values for the colorMap
option - Eg. allows all bars with values betweeen 10 and 20 to be green

+ Pie charts may now have a border using the borderWidth and borderColor
options - Thanks Michael Mior for the suggestion

+ Added a disableHiddenCheck option - If you know your charts will never be
in a hidden block (display:none) at time of rendering, disabling this check
may improve performance

+ Reduce number of DOM interactions when rendering charts in Internet Explorer
to increase render performance

+ Bar charts may now have stacked values by providing an array of arrays
or by passing a comma and colon delimited series of values (eg. "1:2:3,2:3:1")

+ Fix a bug where some spots would be drawn in the wrong position on line
charts if the x values were supplied as well as the y

+ Fix a bug causing hidden composite sparklines to fail to render 
once $.sparkline_display_visible() is called.

+ If there are multiple calls to sparkline() on a hidden element, the plugin
will now only render the most recent one when the element becomes visible,
saving CPU time for the same visual result.

+ If a hidden sparkline waiting to be rendered is removed from the DOM
via jQuery's .remove() or .html() etc methods then it will be
removed from the pending queue the next time $.sparkline_display_visible()
is called, preventing a memory leak.

+ Add the $.range_map() and $.spformat() methods


1.6 30/November/2010

+ Allow options to be set as tag attributes, as well as passed in to the sparkline
function as an argument.  See new enableTagOptions and tagOptionPrefix options.

+ Added alternate methods of passing values in via HTML.
Can now include inline values in a comment, or pass values in as a values
attribute on the tag:
<span class="sparkline"><!-- 1,2,3,4,5 --></span>
<span class="sparkline" values="1,2,3,4,5"></span>

+ bullet graphs now handle non-integer values correctly

+ Added drawNormalOnTop option to line charts to force the normal
range to be drawn over the top of the line chart's fill color

+ Detect if an element is not inserted into the DOM so that $.sparkline_display_visible()
will function correctly after it's later inserted.

+ Remove the use of the Array indexOf prototype that was added to IE to avoid
conflicts with other libraries.

+ Default settings are now exposed as $.fn.sparkline.defaults allowing
script-wide changes to be made instead of passing them to the sparkline function
on each call


1.5.1 01/March/2010

+ 1 character typo fix for IE - Thanks to Daniel Kenyon-Jones for the heads up


1.5 26/February/2010

+ Very small pie slices could fill the whole chart in IE
Thanks to Peter Doel for catching and fixing it

+ Added chartRangeClip option to force values outside of chartRangeMin/chartRangeMax
to be clipped

+ Added chartRangeMinX/chartRangeMaxX for line charts

+ Allow chart types other than line chart to be used as composites.

+ colorMap may now pass null as a colour to omit that bar from display entirely

+ colorMap may now be passed as an Array of values allowing the colour of each
bar to be specified individually, rather than mapping by value

+ Added nullColor option to bar charts to force null values to show up as a thin line

+ Performance improvements with IE


1.4.3 14/September/2009

+ Updated default pie chart colours - Thanks Liel Dulev

+ Null values can now be supplied to line and bar charts
(use 'null' as the value) - Thanks to Ed Blachman for 
testing/debugging help

+ Added colorMap option for bar charts

+ Added lineWidth option for line charts - Can be an integer or a float
(try 1.5 or 2)


1.4.2 25/April/2009

+ Fixed rendering of circular spots on line charts
for Internet Explorer 8


1.4.1 27/March/2009

+ Fixed minor off-by-1-pixel display glitch with IE

+ Improved compatibilty with jQuery 1.3 which could cause some sparklines
not to be rendered in certain situations


1.4 25/February/2009

+ Added the box plot chart type

+ Fixed a canvas rendering issue that would display some charts with
fuzzy lines

+ Fixed error in bar charts which would causes bars to be too short.

+ Couple of other minor bug fixes


1.3 25/January/2009

+ Sparklines can't be automatically displayed into hidden elements (ie. 
with display:none) - Added a $.sparkline_display_visible() function
to render any sparklines that were generated while a tag was
hidden

+ Fixed positioning issues where sparklines would be displayed a few
pixels offset from their containers in some browsers

+ Made a first attempt at IE8 support.  IE8 beta 2 seems to
have some vml related bugs though so having more than one sparkline
on a line doesn't work correctly, nor do the markers on line charts

+ Misc other bug fixes

+ Updated the web site with a new look


1.2.1 24/November/2008

+ Pie chart bug fixes:  Divide by zero error resolved
and IE rendering issue if a pie slice is equal to 0
Thanks to Hubert Mandeville for a patch fixing both issues


1.2 - 19/November/2008

+ Fixed positioning of min/max line markers for fixed range graphs
(thanks to Stéphane Busso)

+ Fixed rendering of bar charts with negative values

+ All values in a bar chart have a height now (min values used to 
have height = 0)

+ Added zeroColor option for bar charts which allows assignment of
a different color for values that are equal to zero

+ Line charts can now specify both x and y values if required in one of 
three ways:
a) Inline with HTML with values interleaved:
x1:y1,x2:y2,x3:y3
eg. <span class="linechart">1:10,5:8,7:10</span>
b) Programatically using an x,y array for each value:
$('#mychart').sparkline([ [1,10], [5,8], [7,10] ]);
c) Programatically using a separate array for the x values:
$('#mychart').sparkline( [10,8,10], { xvalues: [1,5,7] });

+ Added chartRangeMin and chartRangeMax to line, bar and discrete graphs
This allows you to artificialy fix the min/max values for the graph
so that multiple graphs can share the same scale.

+ Added defaultPixelsPerValue option to specify how many pixels wide
each value should be in a dynamically sized line graph

+ Added offset option to piecharts - Takes a positive or negative degree
offset to start drawing the chart from.


1.1 - 29/July/2008

+ Added minSpotColor and maxSpotColor to line charts - If set then spot
markers are placed at the cordinates of the minimum and maximum values.

+ Added normalRangeMin and normalRangeMax to line charts - If set then
a normalRangeColor band will be drawn on the graph

+ Added chart compositing - Applying another chart to a DOM element with 
an option of composite: true will cause the second (or more) chart to be 
overlayed on the first rather than replacing it.  

+ Added optional colorMap option to tristate chart - This allows specific 
colors to be supplied for specific values

+ Added discrete chart type - Uses fixed size discrete vertical lines to mark
each values in a series

+ Added bullet graph type - Implementation of Stephen Few's bullet graphs

+ Added pie chart type

+ Improved auto height option to match the line height of the containing
element

+ Fixed some off by one positioning errors

+ Reduced the default radius of spot markers from 2 to 1.5 pixels


1.0 - 17/July/2008

+ Initial Release
