{"name": "autosize", "description": "Autosize is a small, stand-alone script to automatically adjust textarea height to fit text.", "dependencies": {}, "keywords": ["textarea", "form", "ui"], "authors": [{"name": "<PERSON>", "url": "http://www.jacklmoore.com", "email": "<EMAIL>"}], "license": "MIT", "homepage": "http://www.jacklmoore.com/autosize", "ignore": [], "repository": {"type": "git", "url": "http://github.com/jackmoore/autosize.git"}, "main": "dist/autosize.js", "moduleType": ["amd", "node"], "version": "3.0.15", "_release": "3.0.15", "_resolution": {"type": "version", "tag": "3.0.15", "commit": "82ba655a20a5939e93d967c883a3c413973bc480"}, "_source": "https://github.com/jackmoore/autosize.git", "_target": "^3.0.15", "_originalSource": "autosize"}