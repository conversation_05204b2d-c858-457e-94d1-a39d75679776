# <pre>
# This file is in the public domain, so clarified as of
# 2009-05-17 by <PERSON>.

# This data is by no means authoritative; if you think you know better,
# go ahead and edit the file (and please send any changes to
# <EMAIL> for general use in the future).

# From <PERSON> (2006-03-22):
# A good source for time zone historical data outside the U.S. is
# <PERSON> and <PERSON><PERSON>, The International Atlas (6th edition),
# San Diego: ACS Publications, Inc. (2003).
#
# <PERSON><PERSON><PERSON> writes that a good source
# for recent time zone data is the International Air Transport
# Association's Standard Schedules Information Manual (IATA SSIM),
# published semiannually.  <PERSON> sent in several helpful summaries
# of the IATA's data after 1990.
#
# Except where otherwise noted, <PERSON>ks & Pottenger is the source for
# entries through 1991, and IATA SSIM is the source for entries afterwards.
#
# Other sources occasionally used include:
#
#	<PERSON>, World Time Differences,
#	Whitman Publishing Co, 2 Niagara Av, Ealing, London (undated),
#	which I found in the UCLA library.
#
#	<a href="http://www.pettswoodvillage.co.uk/Daylight_Savings_William_<PERSON>.pdf">
#	<PERSON>, The Waste of Daylight, 19th edition
#	</a> (1914-03)
#
#	Brazil's Depar<PERSON>ent<PERSON> (DSH),
#	<a href="http://pcdsh01.on.br/HISTHV.htm">
#	History of Summer Time
#	</a> (1998-09-21, in Portuguese)

#
# I invented the abbreviations marked `*' in the following table;
# the rest are from earlier versions of this file, or from other sources.
# Corrections are welcome!
#                   std dst  2dst
#                   LMT           Local Mean Time
#       -4:00       AST ADT       Atlantic
#       -3:00       WGT WGST      Western Greenland*
#       -1:00       EGT EGST      Eastern Greenland*
#        0:00       GMT BST  BDST Greenwich, British Summer
#        0:00       GMT IST       Greenwich, Irish Summer
#        0:00       WET WEST WEMT Western Europe
#        0:19:32.13 AMT NST       Amsterdam, Netherlands Summer (1835-1937)*
#        0:20       NET NEST      Netherlands (1937-1940)*
#        1:00       CET CEST CEMT Central Europe
#        1:00:14    SET           Swedish (1879-1899)*
#        2:00       EET EEST      Eastern Europe
#        3:00       MSK MSD       Moscow
#
# A reliable and entertaining source about time zones, especially in Britain,
# Derek Howse, Greenwich time and longitude, Philip Wilson Publishers (1997).

# From Peter Ilieve (1994-12-04),
# The original six [EU members]: Belgium, France, (West) Germany, Italy,
# Luxembourg, the Netherlands.
# Plus, from 1 Jan 73: Denmark, Ireland, United Kingdom.
# Plus, from 1 Jan 81: Greece.
# Plus, from 1 Jan 86: Spain, Portugal.
# Plus, from 1 Jan 95: Austria, Finland, Sweden. (Norway negotiated terms for
# entry but in a referendum on 28 Nov 94 the people voted No by 52.2% to 47.8%
# on a turnout of 88.6%. This was almost the same result as Norway's previous
# referendum in 1972, they are the only country to have said No twice.
# Referendums in the other three countries voted Yes.)
# ...
# Estonia ... uses EU dates but not at 01:00 GMT, they use midnight GMT.
# I don't think they know yet what they will do from 1996 onwards.
# ...
# There shouldn't be any [current members who are not using EU rules].
# A Directive has the force of law, member states are obliged to enact
# national law to implement it. The only contentious issue was the
# different end date for the UK and Ireland, and this was always allowed
# in the Directive.


###############################################################################

# Britain (United Kingdom) and Ireland (Eire)

# From Peter Ilieve (1994-07-06):
#
# On 17 Jan 1994 the Independent, a UK quality newspaper, had a piece about
# historical vistas along the Thames in west London. There was a photo
# and a sketch map showing some of the sightlines involved. One paragraph
# of the text said:
#
# `An old stone obelisk marking a forgotten terrestrial meridian stands
# beside the river at Kew. In the 18th century, before time and longitude
# was standardised by the Royal Observatory in Greenwich, scholars observed
# this stone and the movement of stars from Kew Observatory nearby. They
# made their calculations and set the time for the Horse Guards and Parliament,
# but now the stone is obscured by scrubwood and can only be seen by walking
# along the towpath within a few yards of it.'
#
# I have a one inch to one mile map of London and my estimate of the stone's
# position is 51 deg. 28' 30" N, 0 deg. 18' 45" W. The longitude should
# be within about +-2". The Ordnance Survey grid reference is TQ172761.
#
# [This yields GMTOFF = -0:01:15 for London LMT in the 18th century.]

# From Paul Eggert (1993-11-18):
#
# Howse writes that Britain was the first country to use standard time.
# The railways cared most about the inconsistencies of local mean time,
# and it was they who forced a uniform time on the country.
# The original idea was credited to Dr. William Hyde Wollaston (1766-1828)
# and was popularized by Abraham Follett Osler (1808-1903).
# The first railway to adopt London time was the Great Western Railway
# in November 1840; other railways followed suit, and by 1847 most
# (though not all) railways used London time.  On 1847-09-22 the
# Railway Clearing House, an industry standards body, recommended that GMT be
# adopted at all stations as soon as the General Post Office permitted it.
# The transition occurred on 12-01 for the L&NW, the Caledonian,
# and presumably other railways; the January 1848 Bradshaw's lists many
# railways as using GMT.  By 1855 the vast majority of public
# clocks in Britain were set to GMT (though some, like the great clock
# on Tom Tower at Christ Church, Oxford, were fitted with two minute hands,
# one for local time and one for GMT).  The last major holdout was the legal
# system, which stubbornly stuck to local time for many years, leading
# to oddities like polls opening at 08:13 and closing at 16:13.
# The legal system finally switched to GMT when the Statutes (Definition
# of Time) Act took effect; it received the Royal Assent on 1880-08-02.
#
# In the tables below, we condense this complicated story into a single
# transition date for London, namely 1847-12-01.  We don't know as much
# about Dublin, so we use 1880-08-02, the legal transition time.

# From Paul Eggert (2003-09-27):
# Summer Time was first seriously proposed by William Willett (1857-1915),
# a London builder and member of the Royal Astronomical Society
# who circulated a pamphlet ``The Waste of Daylight'' (1907)
# that proposed advancing clocks 20 minutes on each of four Sundays in April,
# and retarding them by the same amount on four Sundays in September.
# A bill was drafted in 1909 and introduced in Parliament several times,
# but it met with ridicule and opposition, especially from farming interests.
# Later editions of the pamphlet proposed one-hour summer time, and
# it was eventually adopted as a wartime measure in 1916.
# See: Summer Time Arrives Early, The Times (2000-05-18).
# A monument to Willett was unveiled on 1927-05-21, in an open space in
# a 45-acre wood near Chislehurst, Kent that was purchased by popular
# subscription and open to the public.  On the south face of the monolith,
# designed by G. W. Miller, is the...William Willett Memorial Sundial,
# which is permanently set to Summer Time.

# From Winston Churchill (1934-04-28):
# It is one of the paradoxes of history that we should owe the boon of
# summer time, which gives every year to the people of this country
# between 160 and 170 hours more daylight leisure, to a war which
# plunged Europe into darkness for four years, and shook the
# foundations of civilization throughout the world.
#	-- <a href="http://www.winstonchurchill.org/fh114willett.htm">
#	"A Silent Toast to William Willett", Pictorial Weekly
#	</a>

# From Paul Eggert (1996-09-03):
# The OED Supplement says that the English originally said ``Daylight Saving''
# when they were debating the adoption of DST in 1908; but by 1916 this
# term appears only in quotes taken from DST's opponents, whereas the
# proponents (who eventually won the argument) are quoted as using ``Summer''.

# From Arthur David Olson (1989-01-19):
#
# A source at the British Information Office in New York avers that it's
# known as "British" Summer Time in all parts of the United Kingdom.

# Date: 4 Jan 89 08:57:25 GMT (Wed)
# From: Jonathan Leffler
# [British Summer Time] is fixed annually by Act of Parliament.
# If you can predict what Parliament will do, you should be in
# politics making a fortune, not computing.

# From Chris Carrier (1996-06-14):
# I remember reading in various wartime issues of the London Times the
# acronym BDST for British Double Summer Time.  Look for the published
# time of sunrise and sunset in The Times, when BDST was in effect, and
# if you find a zone reference it will say, "All times B.D.S.T."

# From Joseph S. Myers (1999-09-02):
# ... some military cables (WO 219/4100 - this is a copy from the
# main SHAEF archives held in the US National Archives, SHAEF/5252/8/516)
# agree that the usage is BDST (this appears in a message dated 17 Feb 1945).

# From Joseph S. Myers (2000-10-03):
# On 18th April 1941, Sir Stephen Tallents of the BBC wrote to Sir
# Alexander Maxwell of the Home Office asking whether there was any
# official designation; the reply of the 21st was that there wasn't
# but he couldn't think of anything better than the "Double British
# Summer Time" that the BBC had been using informally.
# http://student.cusu.cam.ac.uk/~jsm28/british-time/bbc-19410418.png
# http://student.cusu.cam.ac.uk/~jsm28/british-time/ho-19410421.png

# From Sir Alexander Maxwell in the above-mentioned letter (1941-04-21):
# [N]o official designation has as far as I know been adopted for the time
# which is to be introduced in May....
# I cannot think of anything better than "Double British Summer Time"
# which could not be said to run counter to any official description.

# From Paul Eggert (2000-10-02):
# Howse writes (p 157) `DBST' too, but `BDST' seems to have been common
# and follows the more usual convention of putting the location name first,
# so we use `BDST'.

# Peter Ilieve (1998-04-19) described at length
# the history of summer time legislation in the United Kingdom.
# Since 1998 Joseph S. Myers has been updating
# and extending this list, which can be found in
# http://student.cusu.cam.ac.uk/~jsm28/british-time/
# <a href="http://www.polyomino.org.uk/british-time/">
# History of legal time in Britain
# </a>
# Rob Crowther (2012-01-04) reports that that URL no longer
# exists, and the article can now be found at:
# <a href="http://www.polyomino.org.uk/british-time/">
# http://www.polyomino.org.uk/british-time/
# </a>

# From Joseph S. Myers (1998-01-06):
#
# The legal time in the UK outside of summer time is definitely GMT, not UTC;
# see Lord Tanlaw's speech
# <a href="http://www.parliament.the-stationery-office.co.uk/pa/ld199697/ldhansrd/pdvn/lds97/text/70611-20.htm#70611-20_head0">
# (Lords Hansard 11 June 1997 columns 964 to 976)
# </a>.

# From Paul Eggert (2006-03-22):
#
# For lack of other data, follow Shanks & Pottenger for Eire in 1940-1948.
#
# Given Ilieve and Myers's data, the following claims by Shanks & Pottenger
# are incorrect:
#     * Wales did not switch from GMT to daylight saving time until
#	1921 Apr 3, when they began to conform with the rest of Great Britain.
# Actually, Wales was identical after 1880.
#     * Eire had two transitions on 1916 Oct 1.
# It actually just had one transition.
#     * Northern Ireland used single daylight saving time throughout WW II.
# Actually, it conformed to Britain.
#     * GB-Eire changed standard time to 1 hour ahead of GMT on 1968-02-18.
# Actually, that date saw the usual switch to summer time.
# Standard time was not changed until 1968-10-27 (the clocks didn't change).
#
# Here is another incorrect claim by Shanks & Pottenger:
#     * Jersey, Guernsey, and the Isle of Man did not switch from GMT
#	to daylight saving time until 1921 Apr 3, when they began to
#	conform with Great Britain.
# S.R.&O. 1916, No. 382 and HO 45/10811/312364 (quoted above) say otherwise.
#
# The following claim by Shanks & Pottenger is possible though doubtful;
# we'll ignore it for now.
#     * Dublin's 1971-10-31 switch was at 02:00, even though London's was 03:00.
#
#
# Whitman says Dublin Mean Time was -0:25:21, which is more precise than
# Shanks & Pottenger.
# Perhaps this was Dunsink Observatory Time, as Dunsink Observatory
# (8 km NW of Dublin's center) seemingly was to Dublin as Greenwich was
# to London.  For example:
#
#   "Timeball on the ballast office is down.  Dunsink time."
#   -- James Joyce, Ulysses

# From Joseph S. Myers (2005-01-26):
# Irish laws are available online at www.irishstatutebook.ie.  These include
# various relating to legal time, for example:
#
# ZZA13Y1923.html ZZA12Y1924.html ZZA8Y1925.html ZZSIV20PG1267.html
#
# ZZSI71Y1947.html ZZSI128Y1948.html ZZSI23Y1949.html ZZSI41Y1950.html
# ZZSI27Y1951.html ZZSI73Y1952.html
#
# ZZSI11Y1961.html ZZSI232Y1961.html ZZSI182Y1962.html
# ZZSI167Y1963.html ZZSI257Y1964.html ZZSI198Y1967.html
# ZZA23Y1968.html ZZA17Y1971.html
#
# ZZSI67Y1981.html ZZSI212Y1982.html ZZSI45Y1986.html
# ZZSI264Y1988.html ZZSI52Y1990.html ZZSI371Y1992.html
# ZZSI395Y1994.html ZZSI484Y1997.html ZZSI506Y2001.html
#
# [These are all relative to the root, e.g., the first is
# <http://www.irishstatutebook.ie/ZZA13Y1923.html>.]
#
# (These are those I found, but there could be more.  In any case these
# should allow various updates to the comments in the europe file to cover
# the laws applicable in Ireland.)
#
# (Note that the time in the Republic of Ireland since 1968 has been defined
# in terms of standard time being GMT+1 with a period of winter time when it
# is GMT, rather than standard time being GMT with a period of summer time
# being GMT+1.)

# From Paul Eggert (1999-03-28):
# Clive Feather (<news:<EMAIL>>, 1997-03-31)
# reports that Folkestone (Cheriton) Shuttle Terminal uses Concession Time
# (CT), equivalent to French civil time.
# Julian Hill (<news:<EMAIL>>, 1998-09-30) reports that
# trains between Dollands Moor (the freight facility next door)
# and Frethun run in CT.
# My admittedly uninformed guess is that the terminal has two authorities,
# the French concession operators and the British civil authorities,
# and that the time depends on who you're talking to.
# If, say, the British police were called to the station for some reason,
# I would expect the official police report to use GMT/BST and not CET/CEST.
# This is a borderline case, but for now let's stick to GMT/BST.

# From an anonymous contributor (1996-06-02):
# The law governing time in Ireland is under Statutory Instrument SI 395/94,
# which gives force to European Union 7th Council Directive # 94/21/EC.
# Under this directive, the Minister for Justice in Ireland makes appropriate
# regulations. I spoke this morning with the Secretary of the Department of
# Justice (tel +353 1 678 9711) who confirmed to me that the correct name is
# "Irish Summer Time", abbreviated to "IST".

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
# Summer Time Act, 1916
Rule	GB-Eire	1916	only	-	May	21	2:00s	1:00	BST
Rule	GB-Eire	1916	only	-	Oct	 1	2:00s	0	GMT
# S.R.&O. 1917, No. 358
Rule	GB-Eire	1917	only	-	Apr	 8	2:00s	1:00	BST
Rule	GB-Eire	1917	only	-	Sep	17	2:00s	0	GMT
# S.R.&O. 1918, No. 274
Rule	GB-Eire	1918	only	-	Mar	24	2:00s	1:00	BST
Rule	GB-Eire	1918	only	-	Sep	30	2:00s	0	GMT
# S.R.&O. 1919, No. 297
Rule	GB-Eire	1919	only	-	Mar	30	2:00s	1:00	BST
Rule	GB-Eire	1919	only	-	Sep	29	2:00s	0	GMT
# S.R.&O. 1920, No. 458
Rule	GB-Eire	1920	only	-	Mar	28	2:00s	1:00	BST
# S.R.&O. 1920, No. 1844
Rule	GB-Eire	1920	only	-	Oct	25	2:00s	0	GMT
# S.R.&O. 1921, No. 363
Rule	GB-Eire	1921	only	-	Apr	 3	2:00s	1:00	BST
Rule	GB-Eire	1921	only	-	Oct	 3	2:00s	0	GMT
# S.R.&O. 1922, No. 264
Rule	GB-Eire	1922	only	-	Mar	26	2:00s	1:00	BST
Rule	GB-Eire	1922	only	-	Oct	 8	2:00s	0	GMT
# The Summer Time Act, 1922
Rule	GB-Eire	1923	only	-	Apr	Sun>=16	2:00s	1:00	BST
Rule	GB-Eire	1923	1924	-	Sep	Sun>=16	2:00s	0	GMT
Rule	GB-Eire	1924	only	-	Apr	Sun>=9	2:00s	1:00	BST
Rule	GB-Eire	1925	1926	-	Apr	Sun>=16	2:00s	1:00	BST
# The Summer Time Act, 1925
Rule	GB-Eire	1925	1938	-	Oct	Sun>=2	2:00s	0	GMT
Rule	GB-Eire	1927	only	-	Apr	Sun>=9	2:00s	1:00	BST
Rule	GB-Eire	1928	1929	-	Apr	Sun>=16	2:00s	1:00	BST
Rule	GB-Eire	1930	only	-	Apr	Sun>=9	2:00s	1:00	BST
Rule	GB-Eire	1931	1932	-	Apr	Sun>=16	2:00s	1:00	BST
Rule	GB-Eire	1933	only	-	Apr	Sun>=9	2:00s	1:00	BST
Rule	GB-Eire	1934	only	-	Apr	Sun>=16	2:00s	1:00	BST
Rule	GB-Eire	1935	only	-	Apr	Sun>=9	2:00s	1:00	BST
Rule	GB-Eire	1936	1937	-	Apr	Sun>=16	2:00s	1:00	BST
Rule	GB-Eire	1938	only	-	Apr	Sun>=9	2:00s	1:00	BST
Rule	GB-Eire	1939	only	-	Apr	Sun>=16	2:00s	1:00	BST
# S.R.&O. 1939, No. 1379
Rule	GB-Eire	1939	only	-	Nov	Sun>=16	2:00s	0	GMT
# S.R.&O. 1940, No. 172 and No. 1883
Rule	GB-Eire	1940	only	-	Feb	Sun>=23	2:00s	1:00	BST
# S.R.&O. 1941, No. 476
Rule	GB-Eire	1941	only	-	May	Sun>=2	1:00s	2:00	BDST
Rule	GB-Eire	1941	1943	-	Aug	Sun>=9	1:00s	1:00	BST
# S.R.&O. 1942, No. 506
Rule	GB-Eire	1942	1944	-	Apr	Sun>=2	1:00s	2:00	BDST
# S.R.&O. 1944, No. 932
Rule	GB-Eire	1944	only	-	Sep	Sun>=16	1:00s	1:00	BST
# S.R.&O. 1945, No. 312
Rule	GB-Eire	1945	only	-	Apr	Mon>=2	1:00s	2:00	BDST
Rule	GB-Eire	1945	only	-	Jul	Sun>=9	1:00s	1:00	BST
# S.R.&O. 1945, No. 1208
Rule	GB-Eire	1945	1946	-	Oct	Sun>=2	2:00s	0	GMT
Rule	GB-Eire	1946	only	-	Apr	Sun>=9	2:00s	1:00	BST
# The Summer Time Act, 1947
Rule	GB-Eire	1947	only	-	Mar	16	2:00s	1:00	BST
Rule	GB-Eire	1947	only	-	Apr	13	1:00s	2:00	BDST
Rule	GB-Eire	1947	only	-	Aug	10	1:00s	1:00	BST
Rule	GB-Eire	1947	only	-	Nov	 2	2:00s	0	GMT
# Summer Time Order, 1948 (S.I. 1948/495)
Rule	GB-Eire	1948	only	-	Mar	14	2:00s	1:00	BST
Rule	GB-Eire	1948	only	-	Oct	31	2:00s	0	GMT
# Summer Time Order, 1949 (S.I. 1949/373)
Rule	GB-Eire	1949	only	-	Apr	 3	2:00s	1:00	BST
Rule	GB-Eire	1949	only	-	Oct	30	2:00s	0	GMT
# Summer Time Order, 1950 (S.I. 1950/518)
# Summer Time Order, 1951 (S.I. 1951/430)
# Summer Time Order, 1952 (S.I. 1952/451)
Rule	GB-Eire	1950	1952	-	Apr	Sun>=14	2:00s	1:00	BST
Rule	GB-Eire	1950	1952	-	Oct	Sun>=21	2:00s	0	GMT
# revert to the rules of the Summer Time Act, 1925
Rule	GB-Eire	1953	only	-	Apr	Sun>=16	2:00s	1:00	BST
Rule	GB-Eire	1953	1960	-	Oct	Sun>=2	2:00s	0	GMT
Rule	GB-Eire	1954	only	-	Apr	Sun>=9	2:00s	1:00	BST
Rule	GB-Eire	1955	1956	-	Apr	Sun>=16	2:00s	1:00	BST
Rule	GB-Eire	1957	only	-	Apr	Sun>=9	2:00s	1:00	BST
Rule	GB-Eire	1958	1959	-	Apr	Sun>=16	2:00s	1:00	BST
Rule	GB-Eire	1960	only	-	Apr	Sun>=9	2:00s	1:00	BST
# Summer Time Order, 1961 (S.I. 1961/71)
# Summer Time (1962) Order, 1961 (S.I. 1961/2465)
# Summer Time Order, 1963 (S.I. 1963/81)
Rule	GB-Eire	1961	1963	-	Mar	lastSun	2:00s	1:00	BST
Rule	GB-Eire	1961	1968	-	Oct	Sun>=23	2:00s	0	GMT
# Summer Time (1964) Order, 1963 (S.I. 1963/2101)
# Summer Time Order, 1964 (S.I. 1964/1201)
# Summer Time Order, 1967 (S.I. 1967/1148)
Rule	GB-Eire	1964	1967	-	Mar	Sun>=19	2:00s	1:00	BST
# Summer Time Order, 1968 (S.I. 1968/117)
Rule	GB-Eire	1968	only	-	Feb	18	2:00s	1:00	BST
# The British Standard Time Act, 1968
#	(no summer time)
# The Summer Time Act, 1972
Rule	GB-Eire	1972	1980	-	Mar	Sun>=16	2:00s	1:00	BST
Rule	GB-Eire	1972	1980	-	Oct	Sun>=23	2:00s	0	GMT
# Summer Time Order, 1980 (S.I. 1980/1089)
# Summer Time Order, 1982 (S.I. 1982/1673)
# Summer Time Order, 1986 (S.I. 1986/223)
# Summer Time Order, 1988 (S.I. 1988/931)
Rule	GB-Eire	1981	1995	-	Mar	lastSun	1:00u	1:00	BST
Rule	GB-Eire 1981	1989	-	Oct	Sun>=23	1:00u	0	GMT
# Summer Time Order, 1989 (S.I. 1989/985)
# Summer Time Order, 1992 (S.I. 1992/1729)
# Summer Time Order 1994 (S.I. 1994/2798)
Rule	GB-Eire 1990	1995	-	Oct	Sun>=22	1:00u	0	GMT
# Summer Time Order 1997 (S.I. 1997/2982)
# See EU for rules starting in 1996.

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/London	-0:01:15 -	LMT	1847 Dec  1 0:00s
			 0:00	GB-Eire	%s	1968 Oct 27
			 1:00	-	BST	1971 Oct 31 2:00u
			 0:00	GB-Eire	%s	1996
			 0:00	EU	GMT/BST
Link	Europe/London	Europe/Jersey
Link	Europe/London	Europe/Guernsey
Link	Europe/London	Europe/Isle_of_Man
Zone	Europe/Dublin	-0:25:00 -	LMT	1880 Aug  2
			-0:25:21 -	DMT	1916 May 21 2:00
			-0:25:21 1:00	IST	1916 Oct  1 2:00s
			 0:00	GB-Eire	%s	1921 Dec  6 # independence
			 0:00	GB-Eire	GMT/IST	1940 Feb 25 2:00
			 0:00	1:00	IST	1946 Oct  6 2:00
			 0:00	-	GMT	1947 Mar 16 2:00
			 0:00	1:00	IST	1947 Nov  2 2:00
			 0:00	-	GMT	1948 Apr 18 2:00
			 0:00	GB-Eire	GMT/IST	1968 Oct 27
			 1:00	-	IST	1971 Oct 31 2:00u
			 0:00	GB-Eire	GMT/IST	1996
			 0:00	EU	GMT/IST

###############################################################################

# Europe

# EU rules are for the European Union, previously known as the EC, EEC,
# Common Market, etc.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	EU	1977	1980	-	Apr	Sun>=1	 1:00u	1:00	S
Rule	EU	1977	only	-	Sep	lastSun	 1:00u	0	-
Rule	EU	1978	only	-	Oct	 1	 1:00u	0	-
Rule	EU	1979	1995	-	Sep	lastSun	 1:00u	0	-
Rule	EU	1981	max	-	Mar	lastSun	 1:00u	1:00	S
Rule	EU	1996	max	-	Oct	lastSun	 1:00u	0	-
# The most recent directive covers the years starting in 2002.  See:
# <a="http://eur-lex.europa.eu/LexUriServ/LexUriServ.do?uri=CELEX:32000L0084:EN:NOT">
# Directive 2000/84/EC of the European Parliament and of the Council
# of 19 January 2001 on summer-time arrangements.
# </a>

# W-Eur differs from EU only in that W-Eur uses standard time.
Rule	W-Eur	1977	1980	-	Apr	Sun>=1	 1:00s	1:00	S
Rule	W-Eur	1977	only	-	Sep	lastSun	 1:00s	0	-
Rule	W-Eur	1978	only	-	Oct	 1	 1:00s	0	-
Rule	W-Eur	1979	1995	-	Sep	lastSun	 1:00s	0	-
Rule	W-Eur	1981	max	-	Mar	lastSun	 1:00s	1:00	S
Rule	W-Eur	1996	max	-	Oct	lastSun	 1:00s	0	-

# Older C-Eur rules are for convenience in the tables.
# From 1977 on, C-Eur differs from EU only in that C-Eur uses standard time.
Rule	C-Eur	1916	only	-	Apr	30	23:00	1:00	S
Rule	C-Eur	1916	only	-	Oct	 1	 1:00	0	-
Rule	C-Eur	1917	1918	-	Apr	Mon>=15	 2:00s	1:00	S
Rule	C-Eur	1917	1918	-	Sep	Mon>=15	 2:00s	0	-
Rule	C-Eur	1940	only	-	Apr	 1	 2:00s	1:00	S
Rule	C-Eur	1942	only	-	Nov	 2	 2:00s	0	-
Rule	C-Eur	1943	only	-	Mar	29	 2:00s	1:00	S
Rule	C-Eur	1943	only	-	Oct	 4	 2:00s	0	-
Rule	C-Eur	1944	1945	-	Apr	Mon>=1	 2:00s	1:00	S
# Whitman gives 1944 Oct 7; go with Shanks & Pottenger.
Rule	C-Eur	1944	only	-	Oct	 2	 2:00s	0	-
# From Jesper Norgaard Welen (2008-07-13):
#
# I found what is probably a typo of 2:00 which should perhaps be 2:00s
# in the C-Eur rule from tz database version 2008d (this part was
# corrected in version 2008d). The circumstancial evidence is simply the
# tz database itself, as seen below:
#
# Zone Europe/Paris 0:09:21 - LMT 1891 Mar 15  0:01
#    0:00 France WE%sT 1945 Sep 16  3:00
#
# Zone Europe/Monaco 0:29:32 - LMT 1891 Mar 15
#    0:00 France WE%sT 1945 Sep 16 3:00
#
# Zone Europe/Belgrade 1:22:00 - LMT 1884
#    1:00 1:00 CEST 1945 Sep 16  2:00s
#
# Rule France 1945 only - Sep 16  3:00 0 -
# Rule Belgium 1945 only - Sep 16  2:00s 0 -
# Rule Neth 1945 only - Sep 16 2:00s 0 -
#
# The rule line to be changed is:
#
# Rule C-Eur 1945 only - Sep 16  2:00 0 -
#
# It seems that Paris, Monaco, Rule France, Rule Belgium all agree on
# 2:00 standard time, e.g. 3:00 local time.  However there are no
# countries that use C-Eur rules in September 1945, so the only items
# affected are apparently these ficticious zones that translates acronyms
# CET and MET:
#
# Zone CET  1:00 C-Eur CE%sT
# Zone MET  1:00 C-Eur ME%sT
#
# It this is right then the corrected version would look like:
#
# Rule C-Eur 1945 only - Sep 16  2:00s 0 -
#
# A small step for mankind though 8-)
Rule	C-Eur	1945	only	-	Sep	16	 2:00s	0	-
Rule	C-Eur	1977	1980	-	Apr	Sun>=1	 2:00s	1:00	S
Rule	C-Eur	1977	only	-	Sep	lastSun	 2:00s	0	-
Rule	C-Eur	1978	only	-	Oct	 1	 2:00s	0	-
Rule	C-Eur	1979	1995	-	Sep	lastSun	 2:00s	0	-
Rule	C-Eur	1981	max	-	Mar	lastSun	 2:00s	1:00	S
Rule	C-Eur	1996	max	-	Oct	lastSun	 2:00s	0	-

# E-Eur differs from EU only in that E-Eur switches at midnight local time.
Rule	E-Eur	1977	1980	-	Apr	Sun>=1	 0:00	1:00	S
Rule	E-Eur	1977	only	-	Sep	lastSun	 0:00	0	-
Rule	E-Eur	1978	only	-	Oct	 1	 0:00	0	-
Rule	E-Eur	1979	1995	-	Sep	lastSun	 0:00	0	-
Rule	E-Eur	1981	max	-	Mar	lastSun	 0:00	1:00	S
Rule	E-Eur	1996	max	-	Oct	lastSun	 0:00	0	-

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Russia	1917	only	-	Jul	 1	23:00	1:00	MST	# Moscow Summer Time
Rule	Russia	1917	only	-	Dec	28	 0:00	0	MMT	# Moscow Mean Time
Rule	Russia	1918	only	-	May	31	22:00	2:00	MDST	# Moscow Double Summer Time
Rule	Russia	1918	only	-	Sep	16	 1:00	1:00	MST
Rule	Russia	1919	only	-	May	31	23:00	2:00	MDST
Rule	Russia	1919	only	-	Jul	 1	 2:00	1:00	S
Rule	Russia	1919	only	-	Aug	16	 0:00	0	-
Rule	Russia	1921	only	-	Feb	14	23:00	1:00	S
Rule	Russia	1921	only	-	Mar	20	23:00	2:00	M # Midsummer
Rule	Russia	1921	only	-	Sep	 1	 0:00	1:00	S
Rule	Russia	1921	only	-	Oct	 1	 0:00	0	-
# Act No.925 of the Council of Ministers of the USSR (1980-10-24):
Rule	Russia	1981	1984	-	Apr	 1	 0:00	1:00	S
Rule	Russia	1981	1983	-	Oct	 1	 0:00	0	-
# Act No.967 of the Council of Ministers of the USSR (1984-09-13), repeated in
# Act No.227 of the Council of Ministers of the USSR (1989-03-14):
Rule	Russia	1984	1991	-	Sep	lastSun	 2:00s	0	-
Rule	Russia	1985	1991	-	Mar	lastSun	 2:00s	1:00	S
#
Rule	Russia	1992	only	-	Mar	lastSat	 23:00	1:00	S
Rule	Russia	1992	only	-	Sep	lastSat	 23:00	0	-
Rule	Russia	1993	2010	-	Mar	lastSun	 2:00s	1:00	S
Rule	Russia	1993	1995	-	Sep	lastSun	 2:00s	0	-
Rule	Russia	1996	2010	-	Oct	lastSun	 2:00s	0	-

# From Alexander Krivenyshev (2011-06-14):
# According to Kremlin press service, Russian President Dmitry Medvedev
# signed a federal law "On calculation of time" on June 9, 2011.
# According to the law Russia is abolishing daylight saving time.
#
# Medvedev signed a law "On the Calculation of Time" (in russian):
# <a href="http://bmockbe.ru/events/?ID=7583">
# http://bmockbe.ru/events/?ID=7583
# </a>
#
# Medvedev signed a law on the calculation of the time (in russian):
# <a href="http://www.regnum.ru/news/polit/1413906.html">
# http://www.regnum.ru/news/polit/1413906.html
# </a>

# From Arthur David Olson (2011-06-15):
# Take "abolishing daylight saving time" to mean that time is now considered
# to be standard.

# These are for backward compatibility with older versions.

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	WET		0:00	EU	WE%sT
Zone	CET		1:00	C-Eur	CE%sT
Zone	MET		1:00	C-Eur	ME%sT
Zone	EET		2:00	EU	EE%sT

# Previous editions of this database used abbreviations like MET DST
# for Central European Summer Time, but this didn't agree with common usage.

# From Markus Kuhn (1996-07-12):
# The official German names ... are
#
#	Mitteleuropaeische Zeit (MEZ)         = UTC+01:00
#	Mitteleuropaeische Sommerzeit (MESZ)  = UTC+02:00
#
# as defined in the German Time Act (Gesetz ueber die Zeitbestimmung (ZeitG),
# 1978-07-25, Bundesgesetzblatt, Jahrgang 1978, Teil I, S. 1110-1111)....
# I wrote ... to the German Federal Physical-Technical Institution
#
#	Physikalisch-Technische Bundesanstalt (PTB)
#	Laboratorium 4.41 "Zeiteinheit"
#	Postfach 3345
#	D-38023 Braunschweig
#	phone: +49 531 592-0
#
# ... I received today an answer letter from Dr. Peter Hetzel, head of the PTB
# department for time and frequency transmission.  He explained that the
# PTB translates MEZ and MESZ into English as
#
#	Central European Time (CET)         = UTC+01:00
#	Central European Summer Time (CEST) = UTC+02:00


# Albania
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Albania	1940	only	-	Jun	16	0:00	1:00	S
Rule	Albania	1942	only	-	Nov	 2	3:00	0	-
Rule	Albania	1943	only	-	Mar	29	2:00	1:00	S
Rule	Albania	1943	only	-	Apr	10	3:00	0	-
Rule	Albania	1974	only	-	May	 4	0:00	1:00	S
Rule	Albania	1974	only	-	Oct	 2	0:00	0	-
Rule	Albania	1975	only	-	May	 1	0:00	1:00	S
Rule	Albania	1975	only	-	Oct	 2	0:00	0	-
Rule	Albania	1976	only	-	May	 2	0:00	1:00	S
Rule	Albania	1976	only	-	Oct	 3	0:00	0	-
Rule	Albania	1977	only	-	May	 8	0:00	1:00	S
Rule	Albania	1977	only	-	Oct	 2	0:00	0	-
Rule	Albania	1978	only	-	May	 6	0:00	1:00	S
Rule	Albania	1978	only	-	Oct	 1	0:00	0	-
Rule	Albania	1979	only	-	May	 5	0:00	1:00	S
Rule	Albania	1979	only	-	Sep	30	0:00	0	-
Rule	Albania	1980	only	-	May	 3	0:00	1:00	S
Rule	Albania	1980	only	-	Oct	 4	0:00	0	-
Rule	Albania	1981	only	-	Apr	26	0:00	1:00	S
Rule	Albania	1981	only	-	Sep	27	0:00	0	-
Rule	Albania	1982	only	-	May	 2	0:00	1:00	S
Rule	Albania	1982	only	-	Oct	 3	0:00	0	-
Rule	Albania	1983	only	-	Apr	18	0:00	1:00	S
Rule	Albania	1983	only	-	Oct	 1	0:00	0	-
Rule	Albania	1984	only	-	Apr	 1	0:00	1:00	S
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Tirane	1:19:20 -	LMT	1914
			1:00	-	CET	1940 Jun 16
			1:00	Albania	CE%sT	1984 Jul
			1:00	EU	CE%sT

# Andorra
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Andorra	0:06:04 -	LMT	1901
			0:00	-	WET	1946 Sep 30
			1:00	-	CET	1985 Mar 31 2:00
			1:00	EU	CE%sT

# Austria

# From Paul Eggert (2006-03-22): Shanks & Pottenger give 1918-06-16 and
# 1945-11-18, but the Austrian Federal Office of Metrology and
# Surveying (BEV) gives 1918-09-16 and for Vienna gives the "alleged"
# date of 1945-04-12 with no time.  For the 1980-04-06 transition
# Shanks & Pottenger give 02:00, the BEV 00:00.  Go with the BEV,
# and guess 02:00 for 1945-04-12.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Austria	1920	only	-	Apr	 5	2:00s	1:00	S
Rule	Austria	1920	only	-	Sep	13	2:00s	0	-
Rule	Austria	1946	only	-	Apr	14	2:00s	1:00	S
Rule	Austria	1946	1948	-	Oct	Sun>=1	2:00s	0	-
Rule	Austria	1947	only	-	Apr	 6	2:00s	1:00	S
Rule	Austria	1948	only	-	Apr	18	2:00s	1:00	S
Rule	Austria	1980	only	-	Apr	 6	0:00	1:00	S
Rule	Austria	1980	only	-	Sep	28	0:00	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Vienna	1:05:20 -	LMT	1893 Apr
			1:00	C-Eur	CE%sT	1920
			1:00	Austria	CE%sT	1940 Apr  1 2:00s
			1:00	C-Eur	CE%sT	1945 Apr  2 2:00s
			1:00	1:00	CEST	1945 Apr 12 2:00s
			1:00	-	CET	1946
			1:00	Austria	CE%sT	1981
			1:00	EU	CE%sT

# Belarus
# From Yauhen Kharuzhy (2011-09-16):
# By latest Belarus government act Europe/Minsk timezone was changed to
# GMT+3 without DST (was GMT+2 with DST).
#
# Sources (Russian language):
# 1.
# <a href="http://www.belta.by/ru/all_news/society/V-Belarusi-otmenjaetsja-perexod-na-sezonnoe-vremja_i_572952.html">
# http://www.belta.by/ru/all_news/society/V-Belarusi-otmenjaetsja-perexod-na-sezonnoe-vremja_i_572952.html
# </a>
# 2.
# <a href="http://naviny.by/rubrics/society/2011/09/16/ic_articles_116_175144/">
# http://naviny.by/rubrics/society/2011/09/16/ic_articles_116_175144/
# </a>
# 3.
# <a href="http://news.tut.by/society/250578.html">
# http://news.tut.by/society/250578.html
# </a>
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Minsk	1:50:16 -	LMT	1880
			1:50	-	MMT	1924 May 2 # Minsk Mean Time
			2:00	-	EET	1930 Jun 21
			3:00	-	MSK	1941 Jun 28
			1:00	C-Eur	CE%sT	1944 Jul  3
			3:00	Russia	MSK/MSD	1990
			3:00	-	MSK	1991 Mar 31 2:00s
			2:00	1:00	EEST	1991 Sep 29 2:00s
			2:00	-	EET	1992 Mar 29 0:00s
			2:00	1:00	EEST	1992 Sep 27 0:00s
			2:00	Russia	EE%sT	2011 Mar 27 2:00s
			3:00	-	FET # Further-eastern European Time

# Belgium
#
# From Paul Eggert (1997-07-02):
# Entries from 1918 through 1991 are taken from:
#	Annuaire de L'Observatoire Royal de Belgique,
#	Avenue Circulaire, 3, B-1180 BRUXELLES, CLVIIe annee, 1991
#	(Imprimerie HAYEZ, s.p.r.l., Rue Fin, 4, 1080 BRUXELLES, MCMXC),
#	pp 8-9.
# LMT before 1892 was 0:17:30, according to the official journal of Belgium:
#	Moniteur Belge, Samedi 30 Avril 1892, N.121.
# Thanks to Pascal Delmoitie for these references.
# The 1918 rules are listed for completeness; they apply to unoccupied Belgium.
# Assume Brussels switched to WET in 1918 when the armistice took effect.
#
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Belgium	1918	only	-	Mar	 9	 0:00s	1:00	S
Rule	Belgium	1918	1919	-	Oct	Sat>=1	23:00s	0	-
Rule	Belgium	1919	only	-	Mar	 1	23:00s	1:00	S
Rule	Belgium	1920	only	-	Feb	14	23:00s	1:00	S
Rule	Belgium	1920	only	-	Oct	23	23:00s	0	-
Rule	Belgium	1921	only	-	Mar	14	23:00s	1:00	S
Rule	Belgium	1921	only	-	Oct	25	23:00s	0	-
Rule	Belgium	1922	only	-	Mar	25	23:00s	1:00	S
Rule	Belgium	1922	1927	-	Oct	Sat>=1	23:00s	0	-
Rule	Belgium	1923	only	-	Apr	21	23:00s	1:00	S
Rule	Belgium	1924	only	-	Mar	29	23:00s	1:00	S
Rule	Belgium	1925	only	-	Apr	 4	23:00s	1:00	S
# DSH writes that a royal decree of 1926-02-22 specified the Sun following 3rd
# Sat in Apr (except if it's Easter, in which case it's one Sunday earlier),
# to Sun following 1st Sat in Oct, and that a royal decree of 1928-09-15
# changed the transition times to 02:00 GMT.
Rule	Belgium	1926	only	-	Apr	17	23:00s	1:00	S
Rule	Belgium	1927	only	-	Apr	 9	23:00s	1:00	S
Rule	Belgium	1928	only	-	Apr	14	23:00s	1:00	S
Rule	Belgium	1928	1938	-	Oct	Sun>=2	 2:00s	0	-
Rule	Belgium	1929	only	-	Apr	21	 2:00s	1:00	S
Rule	Belgium	1930	only	-	Apr	13	 2:00s	1:00	S
Rule	Belgium	1931	only	-	Apr	19	 2:00s	1:00	S
Rule	Belgium	1932	only	-	Apr	 3	 2:00s	1:00	S
Rule	Belgium	1933	only	-	Mar	26	 2:00s	1:00	S
Rule	Belgium	1934	only	-	Apr	 8	 2:00s	1:00	S
Rule	Belgium	1935	only	-	Mar	31	 2:00s	1:00	S
Rule	Belgium	1936	only	-	Apr	19	 2:00s	1:00	S
Rule	Belgium	1937	only	-	Apr	 4	 2:00s	1:00	S
Rule	Belgium	1938	only	-	Mar	27	 2:00s	1:00	S
Rule	Belgium	1939	only	-	Apr	16	 2:00s	1:00	S
Rule	Belgium	1939	only	-	Nov	19	 2:00s	0	-
Rule	Belgium	1940	only	-	Feb	25	 2:00s	1:00	S
Rule	Belgium	1944	only	-	Sep	17	 2:00s	0	-
Rule	Belgium	1945	only	-	Apr	 2	 2:00s	1:00	S
Rule	Belgium	1945	only	-	Sep	16	 2:00s	0	-
Rule	Belgium	1946	only	-	May	19	 2:00s	1:00	S
Rule	Belgium	1946	only	-	Oct	 7	 2:00s	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Brussels	0:17:30 -	LMT	1880
			0:17:30	-	BMT	1892 May  1 12:00 # Brussels MT
			0:00	-	WET	1914 Nov  8
			1:00	-	CET	1916 May  1  0:00
			1:00	C-Eur	CE%sT	1918 Nov 11 11:00u
			0:00	Belgium	WE%sT	1940 May 20  2:00s
			1:00	C-Eur	CE%sT	1944 Sep  3
			1:00	Belgium	CE%sT	1977
			1:00	EU	CE%sT

# Bosnia and Herzegovina
# see Serbia

# Bulgaria
#
# From Plamen Simenov via Steffen Thorsen (1999-09-09):
# A document of Government of Bulgaria (No.94/1997) says:
# EET --> EETDST is in 03:00 Local time in last Sunday of March ...
# EETDST --> EET is in 04:00 Local time in last Sunday of October
#
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Bulg	1979	only	-	Mar	31	23:00	1:00	S
Rule	Bulg	1979	only	-	Oct	 1	 1:00	0	-
Rule	Bulg	1980	1982	-	Apr	Sat>=1	23:00	1:00	S
Rule	Bulg	1980	only	-	Sep	29	 1:00	0	-
Rule	Bulg	1981	only	-	Sep	27	 2:00	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Sofia	1:33:16 -	LMT	1880
			1:56:56	-	IMT	1894 Nov 30 # Istanbul MT?
			2:00	-	EET	1942 Nov  2  3:00
			1:00	C-Eur	CE%sT	1945
			1:00	-	CET	1945 Apr 2 3:00
			2:00	-	EET	1979 Mar 31 23:00
			2:00	Bulg	EE%sT	1982 Sep 26  2:00
			2:00	C-Eur	EE%sT	1991
			2:00	E-Eur	EE%sT	1997
			2:00	EU	EE%sT

# Croatia
# see Serbia

# Cyprus
# Please see the `asia' file for Asia/Nicosia.

# Czech Republic
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Czech	1945	only	-	Apr	 8	2:00s	1:00	S
Rule	Czech	1945	only	-	Nov	18	2:00s	0	-
Rule	Czech	1946	only	-	May	 6	2:00s	1:00	S
Rule	Czech	1946	1949	-	Oct	Sun>=1	2:00s	0	-
Rule	Czech	1947	only	-	Apr	20	2:00s	1:00	S
Rule	Czech	1948	only	-	Apr	18	2:00s	1:00	S
Rule	Czech	1949	only	-	Apr	 9	2:00s	1:00	S
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Prague	0:57:44 -	LMT	1850
			0:57:44	-	PMT	1891 Oct     # Prague Mean Time
			1:00	C-Eur	CE%sT	1944 Sep 17 2:00s
			1:00	Czech	CE%sT	1979
			1:00	EU	CE%sT

# Denmark, Faroe Islands, and Greenland

# From Jesper Norgaard Welen (2005-04-26):
# http://www.hum.aau.dk/~poe/tid/tine/DanskTid.htm says that the law
# [introducing standard time] was in effect from 1894-01-01....
# The page http://www.retsinfo.dk/_GETDOCI_/ACCN/A18930008330-REGL
# confirms this, and states that the law was put forth 1893-03-29.
#
# The EU treaty with effect from 1973:
# http://www.retsinfo.dk/_GETDOCI_/ACCN/A19722110030-REGL
#
# This provoked a new law from 1974 to make possible summer time changes
# in subsequenet decrees with the law
# http://www.retsinfo.dk/_GETDOCI_/ACCN/A19740022330-REGL
#
# It seems however that no decree was set forward until 1980.  I have
# not found any decree, but in another related law, the effecting DST
# changes are stated explicitly to be from 1980-04-06 at 02:00 to
# 1980-09-28 at 02:00.  If this is true, this differs slightly from
# the EU rule in that DST runs to 02:00, not 03:00.  We don't know
# when Denmark began using the EU rule correctly, but we have only
# confirmation of the 1980-time, so I presume it was correct in 1981:
# The law is about the management of the extra hour, concerning
# working hours reported and effect on obligatory-rest rules (which
# was suspended on that night):
# http://www.retsinfo.dk/_GETDOCI_/ACCN/C19801120554-REGL

# From Jesper Norgaard Welen (2005-06-11):
# The Herning Folkeblad (1980-09-26) reported that the night between
# Saturday and Sunday the clock is set back from three to two.

# From Paul Eggert (2005-06-11):
# Hence the "02:00" of the 1980 law refers to standard time, not
# wall-clock time, and so the EU rules were in effect in 1980.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Denmark	1916	only	-	May	14	23:00	1:00	S
Rule	Denmark	1916	only	-	Sep	30	23:00	0	-
Rule	Denmark	1940	only	-	May	15	 0:00	1:00	S
Rule	Denmark	1945	only	-	Apr	 2	 2:00s	1:00	S
Rule	Denmark	1945	only	-	Aug	15	 2:00s	0	-
Rule	Denmark	1946	only	-	May	 1	 2:00s	1:00	S
Rule	Denmark	1946	only	-	Sep	 1	 2:00s	0	-
Rule	Denmark	1947	only	-	May	 4	 2:00s	1:00	S
Rule	Denmark	1947	only	-	Aug	10	 2:00s	0	-
Rule	Denmark	1948	only	-	May	 9	 2:00s	1:00	S
Rule	Denmark	1948	only	-	Aug	 8	 2:00s	0	-
#
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone Europe/Copenhagen	 0:50:20 -	LMT	1890
			 0:50:20 -	CMT	1894 Jan  1 # Copenhagen MT
			 1:00	Denmark	CE%sT	1942 Nov  2 2:00s
			 1:00	C-Eur	CE%sT	1945 Apr  2 2:00
			 1:00	Denmark	CE%sT	1980
			 1:00	EU	CE%sT
Zone Atlantic/Faroe	-0:27:04 -	LMT	1908 Jan 11	# Torshavn
			 0:00	-	WET	1981
			 0:00	EU	WE%sT
#
# From Paul Eggert (2004-10-31):
# During World War II, Germany maintained secret manned weather stations in
# East Greenland and Franz Josef Land, but we don't know their time zones.
# My source for this is Wilhelm Dege's book mentioned under Svalbard.
#
# From Paul Eggert (2006-03-22):
# Greenland joined the EU as part of Denmark, obtained home rule on 1979-05-01,
# and left the EU on 1985-02-01.  It therefore should have been using EU
# rules at least through 1984.  Shanks & Pottenger say Scoresbysund and Godthab
# used C-Eur rules after 1980, but IATA SSIM (1991/1996) says they use EU
# rules since at least 1991.  Assume EU rules since 1980.

# From Gwillin Law (2001-06-06), citing
# <http://www.statkart.no/efs/efshefter/2001/efs5-2001.pdf> (2001-03-15),
# and with translations corrected by Steffen Thorsen:
#
# Greenland has four local times, and the relation to UTC
# is according to the following time line:
#
# The military zone near Thule	UTC-4
# Standard Greenland time	UTC-3
# Scoresbysund			UTC-1
# Danmarkshavn			UTC
#
# In the military area near Thule and in Danmarkshavn DST will not be
# introduced.

# From Rives McDow (2001-11-01):
#
# I correspond regularly with the Dansk Polarcenter, and wrote them at
# the time to clarify the situation in Thule.  Unfortunately, I have
# not heard back from them regarding my recent letter.  [But I have
# info from earlier correspondence.]
#
# According to the center, a very small local time zone around Thule
# Air Base keeps the time according to UTC-4, implementing daylight
# savings using North America rules, changing the time at 02:00 local time....
#
# The east coast of Greenland north of the community of Scoresbysund
# uses UTC in the same way as in Iceland, year round, with no dst.
# There are just a few stations on this coast, including the
# Danmarkshavn ICAO weather station mentioned in your September 29th
# email.  The other stations are two sledge patrol stations in
# Mestersvig and Daneborg, the air force base at Station Nord, and the
# DPC research station at Zackenberg.
#
# Scoresbysund and two small villages nearby keep time UTC-1 and use
# the same daylight savings time period as in West Greenland (Godthab).
#
# The rest of Greenland, including Godthab (this area, although it
# includes central Greenland, is known as west Greenland), keeps time
# UTC-3, with daylight savings methods according to European rules.
#
# It is common procedure to use UTC 0 in the wilderness of East and
# North Greenland, because it is mainly Icelandic aircraft operators
# maintaining traffic in these areas.  However, the official status of
# this area is that it sticks with Godthab time.  This area might be
# considered a dual time zone in some respects because of this.

# From Rives McDow (2001-11-19):
# I heard back from someone stationed at Thule; the time change took place
# there at 2:00 AM.

# From Paul Eggert (2006-03-22):
# From 1997 on the CIA map shows Danmarkshavn on GMT;
# the 1995 map as like Godthab.
# For lack of better info, assume they were like Godthab before 1996.
# startkart.no says Thule does not observe DST, but this is clearly an error,
# so go with Shanks & Pottenger for Thule transitions until this year.
# For 2007 on assume Thule will stay in sync with US DST rules.
#
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Thule	1991	1992	-	Mar	lastSun	2:00	1:00	D
Rule	Thule	1991	1992	-	Sep	lastSun	2:00	0	S
Rule	Thule	1993	2006	-	Apr	Sun>=1	2:00	1:00	D
Rule	Thule	1993	2006	-	Oct	lastSun	2:00	0	S
Rule	Thule	2007	max	-	Mar	Sun>=8	2:00	1:00	D
Rule	Thule	2007	max	-	Nov	Sun>=1	2:00	0	S
#
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone America/Danmarkshavn -1:14:40 -	LMT	1916 Jul 28
			-3:00	-	WGT	1980 Apr  6 2:00
			-3:00	EU	WG%sT	1996
			0:00	-	GMT
Zone America/Scoresbysund -1:27:52 -	LMT	1916 Jul 28 # Ittoqqortoormiit
			-2:00	-	CGT	1980 Apr  6 2:00
			-2:00	C-Eur	CG%sT	1981 Mar 29
			-1:00	EU	EG%sT
Zone America/Godthab	-3:26:56 -	LMT	1916 Jul 28 # Nuuk
			-3:00	-	WGT	1980 Apr  6 2:00
			-3:00	EU	WG%sT
Zone America/Thule	-4:35:08 -	LMT	1916 Jul 28 # Pituffik air base
			-4:00	Thule	A%sT

# Estonia
# From Peter Ilieve (1994-10-15):
# A relative in Tallinn confirms the accuracy of the data for 1989 onwards
# [through 1994] and gives the legal authority for it,
# a regulation of the Government of Estonia, No. 111 of 1989....
#
# From Peter Ilieve (1996-10-28):
# [IATA SSIM (1992/1996) claims that the Baltic republics switch at 01:00s,
# but a relative confirms that Estonia still switches at 02:00s, writing:]
# ``I do not [know] exactly but there are some little different
# (confusing) rules for International Air and Railway Transport Schedules
# conversion in Sunday connected with end of summer time in Estonia....
# A discussion is running about the summer time efficiency and effect on
# human physiology.  It seems that Estonia maybe will not change to
# summer time next spring.''

# From Peter Ilieve (1998-11-04), heavily edited:
# <a href="http://trip.rk.ee/cgi-bin/thw?${BASE}=akt&${OOHTML}=rtd&TA=1998&TO=1&AN=1390">
# The 1998-09-22 Estonian time law
# </a>
# refers to the Eighth Directive and cites the association agreement between
# the EU and Estonia, ratified by the Estonian law (RT II 1995, 22--27, 120).
#
# I also asked [my relative] whether they use any standard abbreviation
# for their standard and summer times. He says no, they use "suveaeg"
# (summer time) and "talveaeg" (winter time).

# From <a href="http://www.baltictimes.com/">The Baltic Times</a> (1999-09-09)
# via Steffen Thorsen:
# This year will mark the last time Estonia shifts to summer time,
# a council of the ruling coalition announced Sept. 6....
# But what this could mean for Estonia's chances of joining the European
# Union are still unclear.  In 1994, the EU declared summer time compulsory
# for all member states until 2001.  Brussels has yet to decide what to do
# after that.

# From Mart Oruaas (2000-01-29):
# Regulation no. 301 (1999-10-12) obsoletes previous regulation
# no. 206 (1998-09-22) and thus sticks Estonia to +02:00 GMT for all
# the year round.  The regulation is effective 1999-11-01.

# From Toomas Soome (2002-02-21):
# The Estonian government has changed once again timezone politics.
# Now we are using again EU rules.
#
# From Urmet Jaanes (2002-03-28):
# The legislative reference is Government decree No. 84 on 2002-02-21.

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Tallinn	1:39:00	-	LMT	1880
			1:39:00	-	TMT	1918 Feb # Tallinn Mean Time
			1:00	C-Eur	CE%sT	1919 Jul
			1:39:00	-	TMT	1921 May
			2:00	-	EET	1940 Aug  6
			3:00	-	MSK	1941 Sep 15
			1:00	C-Eur	CE%sT	1944 Sep 22
			3:00	Russia	MSK/MSD	1989 Mar 26 2:00s
			2:00	1:00	EEST	1989 Sep 24 2:00s
			2:00	C-Eur	EE%sT	1998 Sep 22
			2:00	EU	EE%sT	1999 Nov  1
			2:00	-	EET	2002 Feb 21
			2:00	EU	EE%sT

# Finland

# From Hannu Strang (1994-09-25 06:03:37 UTC):
# Well, here in Helsinki we're just changing from summer time to regular one,
# and it's supposed to change at 4am...

# From Janne Snabb (2010-0715):
#
# I noticed that the Finland data is not accurate for years 1981 and 1982.
# During these two first trial years the DST adjustment was made one hour
# earlier than in forthcoming years. Starting 1983 the adjustment was made
# according to the central European standards.
#
# This is documented in Heikki Oja: Aikakirja 2007, published by The Almanac
# Office of University of Helsinki, ISBN 952-10-3221-9, available online (in
# Finnish) at
#
# <a href="http://almanakka.helsinki.fi/aikakirja/Aikakirja2007kokonaan.pdf">
# http://almanakka.helsinki.fi/aikakirja/Aikakirja2007kokonaan.pdf
# </a>
#
# Page 105 (56 in PDF version) has a handy table of all past daylight savings
# transitions. It is easy enough to interpret without Finnish skills.
#
# This is also confirmed by Finnish Broadcasting Company's archive at:
#
# <a href="http://www.yle.fi/elavaarkisto/?s=s&g=1&ag=5&t=&a=3401">
# http://www.yle.fi/elavaarkisto/?s=s&g=1&ag=5&t=&a=3401
# </a>
#
# The news clip from 1981 says that "the time between 2 and 3 o'clock does not
# exist tonight."

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Finland	1942	only	-	Apr	3	0:00	1:00	S
Rule	Finland	1942	only	-	Oct	3	0:00	0	-
Rule	Finland	1981	1982	-	Mar	lastSun	2:00	1:00	S
Rule	Finland	1981	1982	-	Sep	lastSun	3:00	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Helsinki	1:39:52 -	LMT	1878 May 31
			1:39:52	-	HMT	1921 May    # Helsinki Mean Time
			2:00	Finland	EE%sT	1983
			2:00	EU	EE%sT

# Aaland Is
Link	Europe/Helsinki	Europe/Mariehamn


# France

# From Ciro Discepolo (2000-12-20):
#
# Henri Le Corre, Regimes Horaires pour le monde entier, Editions
# Traditionnelles - Paris 2 books, 1993
#
# Gabriel, Traite de l'heure dans le monde, Guy Tredaniel editeur,
# Paris, 1991
#
# Francoise Gauquelin, Problemes de l'heure resolus en astrologie,
# Guy tredaniel, Paris 1987


#
# Shank & Pottenger seem to use `24:00' ambiguously; resolve it with Whitman.
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	France	1916	only	-	Jun	14	23:00s	1:00	S
Rule	France	1916	1919	-	Oct	Sun>=1	23:00s	0	-
Rule	France	1917	only	-	Mar	24	23:00s	1:00	S
Rule	France	1918	only	-	Mar	 9	23:00s	1:00	S
Rule	France	1919	only	-	Mar	 1	23:00s	1:00	S
Rule	France	1920	only	-	Feb	14	23:00s	1:00	S
Rule	France	1920	only	-	Oct	23	23:00s	0	-
Rule	France	1921	only	-	Mar	14	23:00s	1:00	S
Rule	France	1921	only	-	Oct	25	23:00s	0	-
Rule	France	1922	only	-	Mar	25	23:00s	1:00	S
# DSH writes that a law of 1923-05-24 specified 3rd Sat in Apr at 23:00 to 1st
# Sat in Oct at 24:00; and that in 1930, because of Easter, the transitions
# were Apr 12 and Oct 5.  Go with Shanks & Pottenger.
Rule	France	1922	1938	-	Oct	Sat>=1	23:00s	0	-
Rule	France	1923	only	-	May	26	23:00s	1:00	S
Rule	France	1924	only	-	Mar	29	23:00s	1:00	S
Rule	France	1925	only	-	Apr	 4	23:00s	1:00	S
Rule	France	1926	only	-	Apr	17	23:00s	1:00	S
Rule	France	1927	only	-	Apr	 9	23:00s	1:00	S
Rule	France	1928	only	-	Apr	14	23:00s	1:00	S
Rule	France	1929	only	-	Apr	20	23:00s	1:00	S
Rule	France	1930	only	-	Apr	12	23:00s	1:00	S
Rule	France	1931	only	-	Apr	18	23:00s	1:00	S
Rule	France	1932	only	-	Apr	 2	23:00s	1:00	S
Rule	France	1933	only	-	Mar	25	23:00s	1:00	S
Rule	France	1934	only	-	Apr	 7	23:00s	1:00	S
Rule	France	1935	only	-	Mar	30	23:00s	1:00	S
Rule	France	1936	only	-	Apr	18	23:00s	1:00	S
Rule	France	1937	only	-	Apr	 3	23:00s	1:00	S
Rule	France	1938	only	-	Mar	26	23:00s	1:00	S
Rule	France	1939	only	-	Apr	15	23:00s	1:00	S
Rule	France	1939	only	-	Nov	18	23:00s	0	-
Rule	France	1940	only	-	Feb	25	 2:00	1:00	S
# The French rules for 1941-1944 were not used in Paris, but Shanks & Pottenger
# write that they were used in Monaco and in many French locations.
# Le Corre writes that the upper limit of the free zone was Arneguy, Orthez,
# Mont-de-Marsan, Bazas, Langon, Lamotte-Montravel, Marouil, La
# Rochefoucault, Champagne-Mouton, La Roche-Posay, La Haye-Descartes,
# Loches, Montrichard, Vierzon, Bourges, Moulins, Digoin,
# Paray-le-Monial, Montceau-les-Mines, Chalons-sur-Saone, Arbois,
# Dole, Morez, St-Claude, and Collonges (Haute-Savoie).
Rule	France	1941	only	-	May	 5	 0:00	2:00	M # Midsummer
# Shanks & Pottenger say this transition occurred at Oct 6 1:00,
# but go with Denis Excoffier (1997-12-12),
# who quotes the Ephemerides Astronomiques for 1998 from Bureau des Longitudes
# as saying 5/10/41 22hUT.
Rule	France	1941	only	-	Oct	 6	 0:00	1:00	S
Rule	France	1942	only	-	Mar	 9	 0:00	2:00	M
Rule	France	1942	only	-	Nov	 2	 3:00	1:00	S
Rule	France	1943	only	-	Mar	29	 2:00	2:00	M
Rule	France	1943	only	-	Oct	 4	 3:00	1:00	S
Rule	France	1944	only	-	Apr	 3	 2:00	2:00	M
Rule	France	1944	only	-	Oct	 8	 1:00	1:00	S
Rule	France	1945	only	-	Apr	 2	 2:00	2:00	M
Rule	France	1945	only	-	Sep	16	 3:00	0	-
# Shanks & Pottenger give Mar 28 2:00 and Sep 26 3:00;
# go with Excoffier's 28/3/76 0hUT and 25/9/76 23hUT.
Rule	France	1976	only	-	Mar	28	 1:00	1:00	S
Rule	France	1976	only	-	Sep	26	 1:00	0	-
# Shanks & Pottenger give 0:09:20 for Paris Mean Time, and Whitman 0:09:05,
# but Howse quotes the actual French legislation as saying 0:09:21.
# Go with Howse.  Howse writes that the time in France was officially based
# on PMT-0:09:21 until 1978-08-09, when the time base finally switched to UTC.
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Paris	0:09:21 -	LMT	1891 Mar 15  0:01
			0:09:21	-	PMT	1911 Mar 11  0:01  # Paris MT
# Shanks & Pottenger give 1940 Jun 14 0:00; go with Excoffier and Le Corre.
			0:00	France	WE%sT	1940 Jun 14 23:00
# Le Corre says Paris stuck with occupied-France time after the liberation;
# go with Shanks & Pottenger.
			1:00	C-Eur	CE%sT	1944 Aug 25
			0:00	France	WE%sT	1945 Sep 16  3:00
			1:00	France	CE%sT	1977
			1:00	EU	CE%sT

# Germany

# From Markus Kuhn (1998-09-29):
# The German time zone web site by the Physikalisch-Technische
# Bundesanstalt contains DST information back to 1916.
# [See tz-link.htm for the URL.]

# From Joerg Schilling (2002-10-23):
# In 1945, Berlin was switched to Moscow Summer time (GMT+4) by
# <a href="http://www.dhm.de/lemo/html/biografien/BersarinNikolai/">
# General [Nikolai] Bersarin</a>.

# From Paul Eggert (2003-03-08):
# <a href="http://www.parlament-berlin.de/pds-fraktion.nsf/727459127c8b66ee8525662300459099/defc77cb784f180ac1256c2b0030274b/$FILE/bersarint.pdf">
# http://www.parlament-berlin.de/pds-fraktion.nsf/727459127c8b66ee8525662300459099/defc77cb784f180ac1256c2b0030274b/$FILE/bersarint.pdf
# </a>
# says that Bersarin issued an order to use Moscow time on May 20.
# However, Moscow did not observe daylight saving in 1945, so
# this was equivalent to CEMT (GMT+3), not GMT+4.


# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Germany	1946	only	-	Apr	14	2:00s	1:00	S
Rule	Germany	1946	only	-	Oct	 7	2:00s	0	-
Rule	Germany	1947	1949	-	Oct	Sun>=1	2:00s	0	-
# http://www.ptb.de/de/org/4/44/441/salt.htm says the following transition
# occurred at 3:00 MEZ, not the 2:00 MEZ given in Shanks & Pottenger.
# Go with the PTB.
Rule	Germany	1947	only	-	Apr	 6	3:00s	1:00	S
Rule	Germany	1947	only	-	May	11	2:00s	2:00	M
Rule	Germany	1947	only	-	Jun	29	3:00	1:00	S
Rule	Germany	1948	only	-	Apr	18	2:00s	1:00	S
Rule	Germany	1949	only	-	Apr	10	2:00s	1:00	S

Rule SovietZone	1945	only	-	May	24	2:00	2:00	M # Midsummer
Rule SovietZone	1945	only	-	Sep	24	3:00	1:00	S
Rule SovietZone	1945	only	-	Nov	18	2:00s	0	-

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Berlin	0:53:28 -	LMT	1893 Apr
			1:00	C-Eur	CE%sT	1945 May 24 2:00
			1:00 SovietZone	CE%sT	1946
			1:00	Germany	CE%sT	1980
			1:00	EU	CE%sT

# Georgia
# Please see the "asia" file for Asia/Tbilisi.
# Herodotus (Histories, IV.45) says Georgia north of the Phasis (now Rioni)
# is in Europe.  Our reference location Tbilisi is in the Asian part.

# Gibraltar
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone Europe/Gibraltar	-0:21:24 -	LMT	1880 Aug  2 0:00s
			0:00	GB-Eire	%s	1957 Apr 14 2:00
			1:00	-	CET	1982
			1:00	EU	CE%sT

# Greece
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
# Whitman gives 1932 Jul 5 - Nov 1; go with Shanks & Pottenger.
Rule	Greece	1932	only	-	Jul	 7	0:00	1:00	S
Rule	Greece	1932	only	-	Sep	 1	0:00	0	-
# Whitman gives 1941 Apr 25 - ?; go with Shanks & Pottenger.
Rule	Greece	1941	only	-	Apr	 7	0:00	1:00	S
# Whitman gives 1942 Feb 2 - ?; go with Shanks & Pottenger.
Rule	Greece	1942	only	-	Nov	 2	3:00	0	-
Rule	Greece	1943	only	-	Mar	30	0:00	1:00	S
Rule	Greece	1943	only	-	Oct	 4	0:00	0	-
# Whitman gives 1944 Oct 3 - Oct 31; go with Shanks & Pottenger.
Rule	Greece	1952	only	-	Jul	 1	0:00	1:00	S
Rule	Greece	1952	only	-	Nov	 2	0:00	0	-
Rule	Greece	1975	only	-	Apr	12	0:00s	1:00	S
Rule	Greece	1975	only	-	Nov	26	0:00s	0	-
Rule	Greece	1976	only	-	Apr	11	2:00s	1:00	S
Rule	Greece	1976	only	-	Oct	10	2:00s	0	-
Rule	Greece	1977	1978	-	Apr	Sun>=1	2:00s	1:00	S
Rule	Greece	1977	only	-	Sep	26	2:00s	0	-
Rule	Greece	1978	only	-	Sep	24	4:00	0	-
Rule	Greece	1979	only	-	Apr	 1	9:00	1:00	S
Rule	Greece	1979	only	-	Sep	29	2:00	0	-
Rule	Greece	1980	only	-	Apr	 1	0:00	1:00	S
Rule	Greece	1980	only	-	Sep	28	0:00	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Athens	1:34:52 -	LMT	1895 Sep 14
			1:34:52	-	AMT	1916 Jul 28 0:01     # Athens MT
			2:00	Greece	EE%sT	1941 Apr 30
			1:00	Greece	CE%sT	1944 Apr  4
			2:00	Greece	EE%sT	1981
			# Shanks & Pottenger say it switched to C-Eur in 1981;
			# go with EU instead, since Greece joined it on Jan 1.
			2:00	EU	EE%sT

# Hungary
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Hungary	1918	only	-	Apr	 1	 3:00	1:00	S
Rule	Hungary	1918	only	-	Sep	29	 3:00	0	-
Rule	Hungary	1919	only	-	Apr	15	 3:00	1:00	S
Rule	Hungary	1919	only	-	Sep	15	 3:00	0	-
Rule	Hungary	1920	only	-	Apr	 5	 3:00	1:00	S
Rule	Hungary	1920	only	-	Sep	30	 3:00	0	-
Rule	Hungary	1945	only	-	May	 1	23:00	1:00	S
Rule	Hungary	1945	only	-	Nov	 3	 0:00	0	-
Rule	Hungary	1946	only	-	Mar	31	 2:00s	1:00	S
Rule	Hungary	1946	1949	-	Oct	Sun>=1	 2:00s	0	-
Rule	Hungary	1947	1949	-	Apr	Sun>=4	 2:00s	1:00	S
Rule	Hungary	1950	only	-	Apr	17	 2:00s	1:00	S
Rule	Hungary	1950	only	-	Oct	23	 2:00s	0	-
Rule	Hungary	1954	1955	-	May	23	 0:00	1:00	S
Rule	Hungary	1954	1955	-	Oct	 3	 0:00	0	-
Rule	Hungary	1956	only	-	Jun	Sun>=1	 0:00	1:00	S
Rule	Hungary	1956	only	-	Sep	lastSun	 0:00	0	-
Rule	Hungary	1957	only	-	Jun	Sun>=1	 1:00	1:00	S
Rule	Hungary	1957	only	-	Sep	lastSun	 3:00	0	-
Rule	Hungary	1980	only	-	Apr	 6	 1:00	1:00	S
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Budapest	1:16:20 -	LMT	1890 Oct
			1:00	C-Eur	CE%sT	1918
			1:00	Hungary	CE%sT	1941 Apr  6  2:00
			1:00	C-Eur	CE%sT	1945
			1:00	Hungary	CE%sT	1980 Sep 28  2:00s
			1:00	EU	CE%sT

# Iceland
#
# From Adam David (1993-11-06):
# The name of the timezone in Iceland for system / mail / news purposes is GMT.
#
# (1993-12-05):
# This material is paraphrased from the 1988 edition of the University of
# Iceland Almanak.
#
# From January 1st, 1908 the whole of Iceland was standardised at 1 hour
# behind GMT. Previously, local mean solar time was used in different parts
# of Iceland, the almanak had been based on Reykjavik mean solar time which
# was 1 hour and 28 minutes behind GMT.
#
# "first day of winter" referred to [below] means the first day of the 26 weeks
# of winter, according to the old icelandic calendar that dates back to the
# time the norsemen first settled Iceland.  The first day of winter is always
# Saturday, but is not dependent on the Julian or Gregorian calendars.
#
# (1993-12-10):
# I have a reference from the Oxford Icelandic-English dictionary for the
# beginning of winter, which ties it to the ecclesiastical calendar (and thus
# to the julian/gregorian calendar) over the period in question.
#	the winter begins on the Saturday next before St. Luke's day
#	(old style), or on St. Luke's day, if a Saturday.
# St. Luke's day ought to be traceable from ecclesiastical sources. "old style"
# might be a reference to the Julian calendar as opposed to Gregorian, or it
# might mean something else (???).
#
# From Paul Eggert (2006-03-22):
# The Iceland Almanak, Shanks & Pottenger, and Whitman disagree on many points.
# We go with the Almanak, except for one claim from Shanks & Pottenger, namely
# that Reykavik was 21W57 from 1837 to 1908, local mean time before that.
#
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Iceland	1917	1918	-	Feb	19	23:00	1:00	S
Rule	Iceland	1917	only	-	Oct	21	 1:00	0	-
Rule	Iceland	1918	only	-	Nov	16	 1:00	0	-
Rule	Iceland	1939	only	-	Apr	29	23:00	1:00	S
Rule	Iceland	1939	only	-	Nov	29	 2:00	0	-
Rule	Iceland	1940	only	-	Feb	25	 2:00	1:00	S
Rule	Iceland	1940	only	-	Nov	 3	 2:00	0	-
Rule	Iceland	1941	only	-	Mar	 2	 1:00s	1:00	S
Rule	Iceland	1941	only	-	Nov	 2	 1:00s	0	-
Rule	Iceland	1942	only	-	Mar	 8	 1:00s	1:00	S
Rule	Iceland	1942	only	-	Oct	25	 1:00s	0	-
# 1943-1946 - first Sunday in March until first Sunday in winter
Rule	Iceland	1943	1946	-	Mar	Sun>=1	 1:00s	1:00	S
Rule	Iceland	1943	1948	-	Oct	Sun>=22	 1:00s	0	-
# 1947-1967 - first Sunday in April until first Sunday in winter
Rule	Iceland	1947	1967	-	Apr	Sun>=1	 1:00s	1:00	S
# 1949 Oct transition delayed by 1 week
Rule	Iceland	1949	only	-	Oct	30	 1:00s	0	-
Rule	Iceland	1950	1966	-	Oct	Sun>=22	 1:00s	0	-
Rule	Iceland	1967	only	-	Oct	29	 1:00s	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone Atlantic/Reykjavik	-1:27:24 -	LMT	1837
			-1:27:48 -	RMT	1908 # Reykjavik Mean Time?
			-1:00	Iceland	IS%sT	1968 Apr 7 1:00s
			 0:00	-	GMT

# Italy
#
# From Paul Eggert (2001-03-06):
# Sicily and Sardinia each had their own time zones from 1866 to 1893,
# called Palermo Time (+00:53:28) and Cagliari Time (+00:36:32).
# During World War II, German-controlled Italy used German time.
# But these events all occurred before the 1970 cutoff,
# so record only the time in Rome.
#
# From Paul Eggert (2006-03-22):
# For Italian DST we have three sources: Shanks & Pottenger, Whitman, and
# F. Pollastri
# <a href="http://toi.iriti.cnr.it/uk/ienitlt.html">
# Day-light Saving Time in Italy (2006-02-03)
# </a>
# (`FP' below), taken from an Italian National Electrotechnical Institute
# publication. When the three sources disagree, guess who's right, as follows:
#
# year	FP	Shanks&P. (S)	Whitman (W)	Go with:
# 1916	06-03	06-03 24:00	06-03 00:00	FP & W
#	09-30	09-30 24:00	09-30 01:00	FP; guess 24:00s
# 1917	04-01	03-31 24:00	03-31 00:00	FP & S
#	09-30	09-29 24:00	09-30 01:00	FP & W
# 1918	03-09	03-09 24:00	03-09 00:00	FP & S
#	10-06	10-05 24:00	10-06 01:00	FP & W
# 1919	03-01	03-01 24:00	03-01 00:00	FP & S
#	10-04	10-04 24:00	10-04 01:00	FP; guess 24:00s
# 1920	03-20	03-20 24:00	03-20 00:00	FP & S
#	09-18	09-18 24:00	10-01 01:00	FP; guess 24:00s
# 1944	04-02	04-03 02:00			S (see C-Eur)
#	09-16	10-02 03:00			FP; guess 24:00s
# 1945	09-14	09-16 24:00			FP; guess 24:00s
# 1970	05-21	05-31 00:00			S
#	09-20	09-27 00:00			S
#
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Italy	1916	only	-	Jun	 3	0:00s	1:00	S
Rule	Italy	1916	only	-	Oct	 1	0:00s	0	-
Rule	Italy	1917	only	-	Apr	 1	0:00s	1:00	S
Rule	Italy	1917	only	-	Sep	30	0:00s	0	-
Rule	Italy	1918	only	-	Mar	10	0:00s	1:00	S
Rule	Italy	1918	1919	-	Oct	Sun>=1	0:00s	0	-
Rule	Italy	1919	only	-	Mar	 2	0:00s	1:00	S
Rule	Italy	1920	only	-	Mar	21	0:00s	1:00	S
Rule	Italy	1920	only	-	Sep	19	0:00s	0	-
Rule	Italy	1940	only	-	Jun	15	0:00s	1:00	S
Rule	Italy	1944	only	-	Sep	17	0:00s	0	-
Rule	Italy	1945	only	-	Apr	 2	2:00	1:00	S
Rule	Italy	1945	only	-	Sep	15	0:00s	0	-
Rule	Italy	1946	only	-	Mar	17	2:00s	1:00	S
Rule	Italy	1946	only	-	Oct	 6	2:00s	0	-
Rule	Italy	1947	only	-	Mar	16	0:00s	1:00	S
Rule	Italy	1947	only	-	Oct	 5	0:00s	0	-
Rule	Italy	1948	only	-	Feb	29	2:00s	1:00	S
Rule	Italy	1948	only	-	Oct	 3	2:00s	0	-
Rule	Italy	1966	1968	-	May	Sun>=22	0:00	1:00	S
Rule	Italy	1966	1969	-	Sep	Sun>=22	0:00	0	-
Rule	Italy	1969	only	-	Jun	 1	0:00	1:00	S
Rule	Italy	1970	only	-	May	31	0:00	1:00	S
Rule	Italy	1970	only	-	Sep	lastSun	0:00	0	-
Rule	Italy	1971	1972	-	May	Sun>=22	0:00	1:00	S
Rule	Italy	1971	only	-	Sep	lastSun	1:00	0	-
Rule	Italy	1972	only	-	Oct	 1	0:00	0	-
Rule	Italy	1973	only	-	Jun	 3	0:00	1:00	S
Rule	Italy	1973	1974	-	Sep	lastSun	0:00	0	-
Rule	Italy	1974	only	-	May	26	0:00	1:00	S
Rule	Italy	1975	only	-	Jun	 1	0:00s	1:00	S
Rule	Italy	1975	1977	-	Sep	lastSun	0:00s	0	-
Rule	Italy	1976	only	-	May	30	0:00s	1:00	S
Rule	Italy	1977	1979	-	May	Sun>=22	0:00s	1:00	S
Rule	Italy	1978	only	-	Oct	 1	0:00s	0	-
Rule	Italy	1979	only	-	Sep	30	0:00s	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Rome	0:49:56 -	LMT	1866 Sep 22
			0:49:56	-	RMT	1893 Nov  1 0:00s # Rome Mean
			1:00	Italy	CE%sT	1942 Nov  2 2:00s
			1:00	C-Eur	CE%sT	1944 Jul
			1:00	Italy	CE%sT	1980
			1:00	EU	CE%sT

Link	Europe/Rome	Europe/Vatican
Link	Europe/Rome	Europe/San_Marino

# Latvia

# From Liene Kanepe (1998-09-17):

# I asked about this matter Scientific Secretary of the Institute of Astronomy
# of The University of Latvia Dr. paed Mr. Ilgonis Vilks. I also searched the
# correct data in juridical acts and I found some juridical documents about
# changes in the counting of time in Latvia from 1981....
#
# Act No.35 of the Council of Ministers of Latvian SSR of 1981-01-22 ...
# according to the Act No.925 of the Council of Ministers of USSR of 1980-10-24
# ...: all year round the time of 2nd time zone + 1 hour, in addition turning
# the hands of the clock 1 hour forward on 1 April at 00:00 (GMT 31 March 21:00)
# and 1 hour backward on the 1 October at 00:00 (GMT 30 September 20:00).
#
# Act No.592 of the Council of Ministers of Latvian SSR of 1984-09-24 ...
# according to the Act No.967 of the Council of Ministers of USSR of 1984-09-13
# ...: all year round the time of 2nd time zone + 1 hour, in addition turning
# the hands of the clock 1 hour forward on the last Sunday of March at 02:00
# (GMT 23:00 on the previous day) and 1 hour backward on the last Sunday of
# September at 03:00 (GMT 23:00 on the previous day).
#
# Act No.81 of the Council of Ministers of Latvian SSR of 1989-03-22 ...
# according to the Act No.227 of the Council of Ministers of USSR of 1989-03-14
# ...: since the last Sunday of March 1989 in Lithuanian SSR, Latvian SSR,
# Estonian SSR and Kaliningrad region of Russian Federation all year round the
# time of 2nd time zone (Moscow time minus one hour). On the territory of Latvia
# transition to summer time is performed on the last Sunday of March at 02:00
# (GMT 00:00), turning the hands of the clock 1 hour forward.  The end of
# daylight saving time is performed on the last Sunday of September at 03:00
# (GMT 00:00), turning the hands of the clock 1 hour backward. Exception is
# 1989-03-26, when we must not turn the hands of the clock....
#
# The Regulations of the Cabinet of Ministers of the Republic of Latvia of
# 1997-01-21 on transition to Summer time ... established the same order of
# daylight savings time settings as in the States of the European Union.

# From Andrei Ivanov (2000-03-06):
# This year Latvia will not switch to Daylight Savings Time (as specified in
# <a href="http://www.lv-laiks.lv/wwwraksti/2000/071072/vd4.htm">
# The Regulations of the Cabinet of Ministers of the Rep. of Latvia of
# 29-Feb-2000 (#79)</a>, in Latvian for subscribers only).

# <a href="http://www.rferl.org/newsline/2001/01/3-CEE/cee-030101.html">
# From RFE/RL Newsline (2001-01-03), noted after a heads-up by Rives McDow:
# </a>
# The Latvian government on 2 January decided that the country will
# institute daylight-saving time this spring, LETA reported.
# Last February the three Baltic states decided not to turn back their
# clocks one hour in the spring....
# Minister of Economy Aigars Kalvitis noted that Latvia had too few
# daylight hours and thus decided to comply with a draft European
# Commission directive that provides for instituting daylight-saving
# time in EU countries between 2002 and 2006. The Latvian government
# urged Lithuania and Estonia to adopt a similar time policy, but it
# appears that they will not do so....

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Latvia	1989	1996	-	Mar	lastSun	 2:00s	1:00	S
Rule	Latvia	1989	1996	-	Sep	lastSun	 2:00s	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Riga	1:36:24	-	LMT	1880
			1:36:24	-	RMT	1918 Apr 15 2:00 #Riga Mean Time
			1:36:24	1:00	LST	1918 Sep 16 3:00 #Latvian Summer
			1:36:24	-	RMT	1919 Apr  1 2:00
			1:36:24	1:00	LST	1919 May 22 3:00
			1:36:24	-	RMT	1926 May 11
			2:00	-	EET	1940 Aug  5
			3:00	-	MSK	1941 Jul
			1:00	C-Eur	CE%sT	1944 Oct 13
			3:00	Russia	MSK/MSD	1989 Mar lastSun 2:00s
			2:00	1:00	EEST	1989 Sep lastSun 2:00s
			2:00	Latvia	EE%sT	1997 Jan 21
			2:00	EU	EE%sT	2000 Feb 29
			2:00	-	EET	2001 Jan  2
			2:00	EU	EE%sT

# Liechtenstein
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Vaduz	0:38:04 -	LMT	1894 Jun
			1:00	-	CET	1981
			1:00	EU	CE%sT

# Lithuania

# From Paul Eggert (1996-11-22):
# IATA SSIM (1992/1996) says Lithuania uses W-Eur rules, but since it is
# known to be wrong about Estonia and Latvia, assume it's wrong here too.

# From Marius Gedminas (1998-08-07):
# I would like to inform that in this year Lithuanian time zone
# (Europe/Vilnius) was changed.

# From <a href="http://www.elta.lt/">ELTA</a> No. 972 (2582) (1999-09-29),
# via Steffen Thorsen:
# Lithuania has shifted back to the second time zone (GMT plus two hours)
# to be valid here starting from October 31,
# as decided by the national government on Wednesday....
# The Lithuanian government also announced plans to consider a
# motion to give up shifting to summer time in spring, as it was
# already done by Estonia.

# From the <a href="http://www.tourism.lt/informa/ff.htm">
# Fact File, Lithuanian State Department of Tourism
# </a> (2000-03-27): Local time is GMT+2 hours ..., no daylight saving.

# From a user via Klaus Marten (2003-02-07):
# As a candidate for membership of the European Union, Lithuania will
# observe Summer Time in 2003, changing its clocks at the times laid
# down in EU Directive 2000/84 of 19.I.01 (i.e. at the same times as its
# neighbour Latvia). The text of the Lithuanian government Order of
# 7.XI.02 to this effect can be found at
# http://www.lrvk.lt/nut/11/n1749.htm


# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Vilnius	1:41:16	-	LMT	1880
			1:24:00	-	WMT	1917	    # Warsaw Mean Time
			1:35:36	-	KMT	1919 Oct 10 # Kaunas Mean Time
			1:00	-	CET	1920 Jul 12
			2:00	-	EET	1920 Oct  9
			1:00	-	CET	1940 Aug  3
			3:00	-	MSK	1941 Jun 24
			1:00	C-Eur	CE%sT	1944 Aug
			3:00	Russia	MSK/MSD	1991 Mar 31 2:00s
			2:00	1:00	EEST	1991 Sep 29 2:00s
			2:00	C-Eur	EE%sT	1998
			2:00	-	EET	1998 Mar 29 1:00u
			1:00	EU	CE%sT	1999 Oct 31 1:00u
			2:00	-	EET	2003 Jan  1
			2:00	EU	EE%sT

# Luxembourg
# Whitman disagrees with most of these dates in minor ways;
# go with Shanks & Pottenger.
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Lux	1916	only	-	May	14	23:00	1:00	S
Rule	Lux	1916	only	-	Oct	 1	 1:00	0	-
Rule	Lux	1917	only	-	Apr	28	23:00	1:00	S
Rule	Lux	1917	only	-	Sep	17	 1:00	0	-
Rule	Lux	1918	only	-	Apr	Mon>=15	 2:00s	1:00	S
Rule	Lux	1918	only	-	Sep	Mon>=15	 2:00s	0	-
Rule	Lux	1919	only	-	Mar	 1	23:00	1:00	S
Rule	Lux	1919	only	-	Oct	 5	 3:00	0	-
Rule	Lux	1920	only	-	Feb	14	23:00	1:00	S
Rule	Lux	1920	only	-	Oct	24	 2:00	0	-
Rule	Lux	1921	only	-	Mar	14	23:00	1:00	S
Rule	Lux	1921	only	-	Oct	26	 2:00	0	-
Rule	Lux	1922	only	-	Mar	25	23:00	1:00	S
Rule	Lux	1922	only	-	Oct	Sun>=2	 1:00	0	-
Rule	Lux	1923	only	-	Apr	21	23:00	1:00	S
Rule	Lux	1923	only	-	Oct	Sun>=2	 2:00	0	-
Rule	Lux	1924	only	-	Mar	29	23:00	1:00	S
Rule	Lux	1924	1928	-	Oct	Sun>=2	 1:00	0	-
Rule	Lux	1925	only	-	Apr	 5	23:00	1:00	S
Rule	Lux	1926	only	-	Apr	17	23:00	1:00	S
Rule	Lux	1927	only	-	Apr	 9	23:00	1:00	S
Rule	Lux	1928	only	-	Apr	14	23:00	1:00	S
Rule	Lux	1929	only	-	Apr	20	23:00	1:00	S
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone Europe/Luxembourg	0:24:36 -	LMT	1904 Jun
			1:00	Lux	CE%sT	1918 Nov 25
			0:00	Lux	WE%sT	1929 Oct  6 2:00s
			0:00	Belgium	WE%sT	1940 May 14 3:00
			1:00	C-Eur	WE%sT	1944 Sep 18 3:00
			1:00	Belgium	CE%sT	1977
			1:00	EU	CE%sT

# Macedonia
# see Serbia

# Malta
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Malta	1973	only	-	Mar	31	0:00s	1:00	S
Rule	Malta	1973	only	-	Sep	29	0:00s	0	-
Rule	Malta	1974	only	-	Apr	21	0:00s	1:00	S
Rule	Malta	1974	only	-	Sep	16	0:00s	0	-
Rule	Malta	1975	1979	-	Apr	Sun>=15	2:00	1:00	S
Rule	Malta	1975	1980	-	Sep	Sun>=15	2:00	0	-
Rule	Malta	1980	only	-	Mar	31	2:00	1:00	S
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Malta	0:58:04 -	LMT	1893 Nov  2 0:00s # Valletta
			1:00	Italy	CE%sT	1942 Nov  2 2:00s
			1:00	C-Eur	CE%sT	1945 Apr  2 2:00s
			1:00	Italy	CE%sT	1973 Mar 31
			1:00	Malta	CE%sT	1981
			1:00	EU	CE%sT

# Moldova

# From Paul Eggert (2006-03-22):
# A previous version of this database followed Shanks & Pottenger, who write
# that Tiraspol switched to Moscow time on 1992-01-19 at 02:00.
# However, this is most likely an error, as Moldova declared independence
# on 1991-08-27 (the 1992-01-19 date is that of a Russian decree).
# In early 1992 there was large-scale interethnic violence in the area
# and it's possible that some Russophones continued to observe Moscow time.
# But [two people] separately reported via
# Jesper Norgaard that as of 2001-01-24 Tiraspol was like Chisinau.
# The Tiraspol entry has therefore been removed for now.
#
# From Alexander Krivenyshev (2011-10-17):
# Pridnestrovian Moldavian Republic (PMR, also known as
# "Pridnestrovie") has abolished seasonal clock change (no transition
# to the Winter Time).
#
# News (in Russian):
# <a href="http://www.kyivpost.ua/russia/news/pridnestrove-otkazalos-ot-perehoda-na-zimnee-vremya-30954.html">
# http://www.kyivpost.ua/russia/news/pridnestrove-otkazalos-ot-perehoda-na-zimnee-vremya-30954.html
# </a>
#
# <a href="http://www.allmoldova.com/moldova-news/1249064116.html">
# http://www.allmoldova.com/moldova-news/1249064116.html
# </a>
#
# The substance of this change (reinstatement of the Tiraspol entry)
# is from a patch from Petr Machata (2011-10-17)
#
# From Tim Parenti (2011-10-19)
# In addition, being situated at +4651+2938 would give Tiraspol
# a pre-1880 LMT offset of 1:58:32.
#
# (which agrees with the earlier entry that had been removed)
#
# From Alexander Krivenyshev (2011-10-26)
# NO need to divide Moldova into two timezones at this point.
# As of today, Transnistria (Pridnestrovie)- Tiraspol reversed its own
# decision to abolish DST this winter.
# Following Moldova and neighboring Ukraine- Transnistria (Pridnestrovie)-
# Tiraspol will go back to winter time on October 30, 2011.
# News from Moldova (in russian):
# <a href="http://ru.publika.md/link_317061.html">
# http://ru.publika.md/link_317061.html
# </a>


# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Chisinau	1:55:20 -	LMT	1880
			1:55	-	CMT	1918 Feb 15 # Chisinau MT
			1:44:24	-	BMT	1931 Jul 24 # Bucharest MT
			2:00	Romania	EE%sT	1940 Aug 15
			2:00	1:00	EEST	1941 Jul 17
			1:00	C-Eur	CE%sT	1944 Aug 24
			3:00	Russia	MSK/MSD	1990
			3:00	-	MSK	1990 May 6
			2:00	-	EET	1991
			2:00	Russia	EE%sT	1992
			2:00	E-Eur	EE%sT	1997
# See Romania commentary for the guessed 1997 transition to EU rules.
			2:00	EU	EE%sT

# Monaco
# Shanks & Pottenger give 0:09:20 for Paris Mean Time; go with Howse's
# more precise 0:09:21.
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Monaco	0:29:32 -	LMT	1891 Mar 15
			0:09:21	-	PMT	1911 Mar 11    # Paris Mean Time
			0:00	France	WE%sT	1945 Sep 16 3:00
			1:00	France	CE%sT	1977
			1:00	EU	CE%sT

# Montenegro
# see Serbia

# Netherlands

# Howse writes that the Netherlands' railways used GMT between 1892 and 1940,
# but for other purposes the Netherlands used Amsterdam mean time.

# However, Robert H. van Gent writes (2001-04-01):
# Howse's statement is only correct up to 1909. From 1909-05-01 (00:00:00
# Amsterdam mean time) onwards, the whole of the Netherlands (including
# the Dutch railways) was required by law to observe Amsterdam mean time
# (19 minutes 32.13 seconds ahead of GMT). This had already been the
# common practice (except for the railways) for many decades but it was
# not until 1909 when the Dutch government finally defined this by law.
# On 1937-07-01 this was changed to 20 minutes (exactly) ahead of GMT and
# was generally known as Dutch Time ("Nederlandse Tijd").
#
# (2001-04-08):
# 1892-05-01 was the date when the Dutch railways were by law required to
# observe GMT while the remainder of the Netherlands adhered to the common
# practice of following Amsterdam mean time.
#
# (2001-04-09):
# In 1835 the authorities of the province of North Holland requested the
# municipal authorities of the towns and cities in the province to observe
# Amsterdam mean time but I do not know in how many cases this request was
# actually followed.
#
# From 1852 onwards the Dutch telegraph offices were by law required to
# observe Amsterdam mean time. As the time signals from the observatory of
# Leiden were also distributed by the telegraph system, I assume that most
# places linked up with the telegraph (and railway) system automatically
# adopted Amsterdam mean time.
#
# Although the early Dutch railway companies initially observed a variety
# of times, most of them had adopted Amsterdam mean time by 1858 but it
# was not until 1866 when they were all required by law to observe
# Amsterdam mean time.

# The data before 1945 are taken from
# <http://www.phys.uu.nl/~vgent/wettijd/wettijd.htm>.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Neth	1916	only	-	May	 1	0:00	1:00	NST	# Netherlands Summer Time
Rule	Neth	1916	only	-	Oct	 1	0:00	0	AMT	# Amsterdam Mean Time
Rule	Neth	1917	only	-	Apr	16	2:00s	1:00	NST
Rule	Neth	1917	only	-	Sep	17	2:00s	0	AMT
Rule	Neth	1918	1921	-	Apr	Mon>=1	2:00s	1:00	NST
Rule	Neth	1918	1921	-	Sep	lastMon	2:00s	0	AMT
Rule	Neth	1922	only	-	Mar	lastSun	2:00s	1:00	NST
Rule	Neth	1922	1936	-	Oct	Sun>=2	2:00s	0	AMT
Rule	Neth	1923	only	-	Jun	Fri>=1	2:00s	1:00	NST
Rule	Neth	1924	only	-	Mar	lastSun	2:00s	1:00	NST
Rule	Neth	1925	only	-	Jun	Fri>=1	2:00s	1:00	NST
# From 1926 through 1939 DST began 05-15, except that it was delayed by a week
# in years when 05-15 fell in the Pentecost weekend.
Rule	Neth	1926	1931	-	May	15	2:00s	1:00	NST
Rule	Neth	1932	only	-	May	22	2:00s	1:00	NST
Rule	Neth	1933	1936	-	May	15	2:00s	1:00	NST
Rule	Neth	1937	only	-	May	22	2:00s	1:00	NST
Rule	Neth	1937	only	-	Jul	 1	0:00	1:00	S
Rule	Neth	1937	1939	-	Oct	Sun>=2	2:00s	0	-
Rule	Neth	1938	1939	-	May	15	2:00s	1:00	S
Rule	Neth	1945	only	-	Apr	 2	2:00s	1:00	S
Rule	Neth	1945	only	-	Sep	16	2:00s	0	-
#
# Amsterdam Mean Time was +00:19:32.13 exactly, but the .13 is omitted
# below because the current format requires GMTOFF to be an integer.
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone Europe/Amsterdam	0:19:32 -	LMT	1835
			0:19:32	Neth	%s	1937 Jul  1
			0:20	Neth	NE%sT	1940 May 16 0:00 # Dutch Time
			1:00	C-Eur	CE%sT	1945 Apr  2 2:00
			1:00	Neth	CE%sT	1977
			1:00	EU	CE%sT

# Norway
# http://met.no/met/met_lex/q_u/sommertid.html (2004-01) agrees with Shanks &
# Pottenger.
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Norway	1916	only	-	May	22	1:00	1:00	S
Rule	Norway	1916	only	-	Sep	30	0:00	0	-
Rule	Norway	1945	only	-	Apr	 2	2:00s	1:00	S
Rule	Norway	1945	only	-	Oct	 1	2:00s	0	-
Rule	Norway	1959	1964	-	Mar	Sun>=15	2:00s	1:00	S
Rule	Norway	1959	1965	-	Sep	Sun>=15	2:00s	0	-
Rule	Norway	1965	only	-	Apr	25	2:00s	1:00	S
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Oslo	0:43:00 -	LMT	1895 Jan  1
			1:00	Norway	CE%sT	1940 Aug 10 23:00
			1:00	C-Eur	CE%sT	1945 Apr  2  2:00
			1:00	Norway	CE%sT	1980
			1:00	EU	CE%sT

# Svalbard & Jan Mayen

# From Steffen Thorsen (2001-05-01):
# Although I could not find it explicitly, it seems that Jan Mayen and
# Svalbard have been using the same time as Norway at least since the
# time they were declared as parts of Norway.  Svalbard was declared
# as a part of Norway by law of 1925-07-17 no 11, section 4 and Jan
# Mayen by law of 1930-02-27 no 2, section 2. (From
# http://www.lovdata.no/all/nl-19250717-011.html and
# http://www.lovdata.no/all/nl-19300227-002.html).  The law/regulation
# for normal/standard time in Norway is from 1894-06-29 no 1 (came
# into operation on 1895-01-01) and Svalbard/Jan Mayen seem to be a
# part of this law since 1925/1930. (From
# http://www.lovdata.no/all/nl-18940629-001.html ) I have not been
# able to find if Jan Mayen used a different time zone (e.g. -0100)
# before 1930. Jan Mayen has only been "inhabitated" since 1921 by
# Norwegian meteorologists and maybe used the same time as Norway ever
# since 1921.  Svalbard (Arctic/Longyearbyen) has been inhabited since
# before 1895, and therefore probably changed the local time somewhere
# between 1895 and 1925 (inclusive).

# From Paul Eggert (2001-05-01):
#
# Actually, Jan Mayen was never occupied by Germany during World War II,
# so it must have diverged from Oslo time during the war, as Oslo was
# keeping Berlin time.
#
# <http://home.no.net/janmayen/history.htm> says that the meteorologists
# burned down their station in 1940 and left the island, but returned in
# 1941 with a small Norwegian garrison and continued operations despite
# frequent air ttacks from Germans.  In 1943 the Americans established a
# radiolocating station on the island, called "Atlantic City".  Possibly
# the UTC offset changed during the war, but I think it unlikely that
# Jan Mayen used German daylight-saving rules.
#
# Svalbard is more complicated, as it was raided in August 1941 by an
# Allied party that evacuated the civilian population to England (says
# <http://www.bartleby.com/65/sv/Svalbard.html>).  The Svalbard FAQ
# <http://www.svalbard.com/SvalbardFAQ.html> says that the Germans were
# expelled on 1942-05-14.  However, small parties of Germans did return,
# and according to Wilhelm Dege's book "War North of 80" (1954)
# <http://www.ucalgary.ca/UofC/departments/UP/1-55238/1-55238-110-2.html>
# the German armed forces at the Svalbard weather station code-named
# Haudegen did not surrender to the Allies until September 1945.
#
# All these events predate our cutoff date of 1970.  Unless we can
# come up with more definitive info about the timekeeping during the
# war years it's probably best just do...the following for now:
Link	Europe/Oslo	Arctic/Longyearbyen

# Poland
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Poland	1918	1919	-	Sep	16	2:00s	0	-
Rule	Poland	1919	only	-	Apr	15	2:00s	1:00	S
Rule	Poland	1944	only	-	Apr	 3	2:00s	1:00	S
# Whitman gives 1944 Nov 30; go with Shanks & Pottenger.
Rule	Poland	1944	only	-	Oct	 4	2:00	0	-
# For 1944-1948 Whitman gives the previous day; go with Shanks & Pottenger.
Rule	Poland	1945	only	-	Apr	29	0:00	1:00	S
Rule	Poland	1945	only	-	Nov	 1	0:00	0	-
# For 1946 on the source is Kazimierz Borkowski,
# Torun Center for Astronomy, Dept. of Radio Astronomy, Nicolaus Copernicus U.,
# <http://www.astro.uni.torun.pl/~kb/Artykuly/U-PA/Czas2.htm#tth_tAb1>
# Thanks to Przemyslaw Augustyniak (2005-05-28) for this reference.
# He also gives these further references:
# Mon Pol nr 13, poz 162 (1995) <http://www.abc.com.pl/serwis/mp/1995/0162.htm>
# Druk nr 2180 (2003) <http://www.senat.gov.pl/k5/dok/sejm/053/2180.pdf>
Rule	Poland	1946	only	-	Apr	14	0:00s	1:00	S
Rule	Poland	1946	only	-	Oct	 7	2:00s	0	-
Rule	Poland	1947	only	-	May	 4	2:00s	1:00	S
Rule	Poland	1947	1949	-	Oct	Sun>=1	2:00s	0	-
Rule	Poland	1948	only	-	Apr	18	2:00s	1:00	S
Rule	Poland	1949	only	-	Apr	10	2:00s	1:00	S
Rule	Poland	1957	only	-	Jun	 2	1:00s	1:00	S
Rule	Poland	1957	1958	-	Sep	lastSun	1:00s	0	-
Rule	Poland	1958	only	-	Mar	30	1:00s	1:00	S
Rule	Poland	1959	only	-	May	31	1:00s	1:00	S
Rule	Poland	1959	1961	-	Oct	Sun>=1	1:00s	0	-
Rule	Poland	1960	only	-	Apr	 3	1:00s	1:00	S
Rule	Poland	1961	1964	-	May	lastSun	1:00s	1:00	S
Rule	Poland	1962	1964	-	Sep	lastSun	1:00s	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Warsaw	1:24:00 -	LMT	1880
			1:24:00	-	WMT	1915 Aug  5   # Warsaw Mean Time
			1:00	C-Eur	CE%sT	1918 Sep 16 3:00
			2:00	Poland	EE%sT	1922 Jun
			1:00	Poland	CE%sT	1940 Jun 23 2:00
			1:00	C-Eur	CE%sT	1944 Oct
			1:00	Poland	CE%sT	1977
			1:00	W-Eur	CE%sT	1988
			1:00	EU	CE%sT

# Portugal
#
# From Rui Pedro Salgueiro (1992-11-12):
# Portugal has recently (September, 27) changed timezone
# (from WET to MET or CET) to harmonize with EEC.
#
# Martin Bruckmann (1996-02-29) reports via Peter Ilieve
# that Portugal is reverting to 0:00 by not moving its clocks this spring.
# The new Prime Minister was fed up with getting up in the dark in the winter.
#
# From Paul Eggert (1996-11-12):
# IATA SSIM (1991-09) reports several 1991-09 and 1992-09 transitions
# at 02:00u, not 01:00u.  Assume that these are typos.
# IATA SSIM (1991/1992) reports that the Azores were at -1:00.
# IATA SSIM (1993-02) says +0:00; later issues (through 1996-09) say -1:00.
# Guess that the Azores changed to EU rules in 1992 (since that's when Portugal
# harmonized with the EU), and that they stayed +0:00 that winter.
#
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
# DSH writes that despite Decree 1,469 (1915), the change to the clocks was not
# done every year, depending on what Spain did, because of railroad schedules.
# Go with Shanks & Pottenger.
Rule	Port	1916	only	-	Jun	17	23:00	1:00	S
# Whitman gives 1916 Oct 31; go with Shanks & Pottenger.
Rule	Port	1916	only	-	Nov	 1	 1:00	0	-
Rule	Port	1917	only	-	Feb	28	23:00s	1:00	S
Rule	Port	1917	1921	-	Oct	14	23:00s	0	-
Rule	Port	1918	only	-	Mar	 1	23:00s	1:00	S
Rule	Port	1919	only	-	Feb	28	23:00s	1:00	S
Rule	Port	1920	only	-	Feb	29	23:00s	1:00	S
Rule	Port	1921	only	-	Feb	28	23:00s	1:00	S
Rule	Port	1924	only	-	Apr	16	23:00s	1:00	S
Rule	Port	1924	only	-	Oct	14	23:00s	0	-
Rule	Port	1926	only	-	Apr	17	23:00s	1:00	S
Rule	Port	1926	1929	-	Oct	Sat>=1	23:00s	0	-
Rule	Port	1927	only	-	Apr	 9	23:00s	1:00	S
Rule	Port	1928	only	-	Apr	14	23:00s	1:00	S
Rule	Port	1929	only	-	Apr	20	23:00s	1:00	S
Rule	Port	1931	only	-	Apr	18	23:00s	1:00	S
# Whitman gives 1931 Oct 8; go with Shanks & Pottenger.
Rule	Port	1931	1932	-	Oct	Sat>=1	23:00s	0	-
Rule	Port	1932	only	-	Apr	 2	23:00s	1:00	S
Rule	Port	1934	only	-	Apr	 7	23:00s	1:00	S
# Whitman gives 1934 Oct 5; go with Shanks & Pottenger.
Rule	Port	1934	1938	-	Oct	Sat>=1	23:00s	0	-
# Shanks & Pottenger give 1935 Apr 30; go with Whitman.
Rule	Port	1935	only	-	Mar	30	23:00s	1:00	S
Rule	Port	1936	only	-	Apr	18	23:00s	1:00	S
# Whitman gives 1937 Apr 2; go with Shanks & Pottenger.
Rule	Port	1937	only	-	Apr	 3	23:00s	1:00	S
Rule	Port	1938	only	-	Mar	26	23:00s	1:00	S
Rule	Port	1939	only	-	Apr	15	23:00s	1:00	S
# Whitman gives 1939 Oct 7; go with Shanks & Pottenger.
Rule	Port	1939	only	-	Nov	18	23:00s	0	-
Rule	Port	1940	only	-	Feb	24	23:00s	1:00	S
# Shanks & Pottenger give 1940 Oct 7; go with Whitman.
Rule	Port	1940	1941	-	Oct	 5	23:00s	0	-
Rule	Port	1941	only	-	Apr	 5	23:00s	1:00	S
Rule	Port	1942	1945	-	Mar	Sat>=8	23:00s	1:00	S
Rule	Port	1942	only	-	Apr	25	22:00s	2:00	M # Midsummer
Rule	Port	1942	only	-	Aug	15	22:00s	1:00	S
Rule	Port	1942	1945	-	Oct	Sat>=24	23:00s	0	-
Rule	Port	1943	only	-	Apr	17	22:00s	2:00	M
Rule	Port	1943	1945	-	Aug	Sat>=25	22:00s	1:00	S
Rule	Port	1944	1945	-	Apr	Sat>=21	22:00s	2:00	M
Rule	Port	1946	only	-	Apr	Sat>=1	23:00s	1:00	S
Rule	Port	1946	only	-	Oct	Sat>=1	23:00s	0	-
Rule	Port	1947	1949	-	Apr	Sun>=1	 2:00s	1:00	S
Rule	Port	1947	1949	-	Oct	Sun>=1	 2:00s	0	-
# Shanks & Pottenger say DST was observed in 1950; go with Whitman.
# Whitman gives Oct lastSun for 1952 on; go with Shanks & Pottenger.
Rule	Port	1951	1965	-	Apr	Sun>=1	 2:00s	1:00	S
Rule	Port	1951	1965	-	Oct	Sun>=1	 2:00s	0	-
Rule	Port	1977	only	-	Mar	27	 0:00s	1:00	S
Rule	Port	1977	only	-	Sep	25	 0:00s	0	-
Rule	Port	1978	1979	-	Apr	Sun>=1	 0:00s	1:00	S
Rule	Port	1978	only	-	Oct	 1	 0:00s	0	-
Rule	Port	1979	1982	-	Sep	lastSun	 1:00s	0	-
Rule	Port	1980	only	-	Mar	lastSun	 0:00s	1:00	S
Rule	Port	1981	1982	-	Mar	lastSun	 1:00s	1:00	S
Rule	Port	1983	only	-	Mar	lastSun	 2:00s	1:00	S
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
# Shanks & Pottenger say the transition from LMT to WET occurred 1911-05-24;
# Willett says 1912-01-01.  Go with Willett.
Zone	Europe/Lisbon	-0:36:32 -	LMT	1884
			-0:36:32 -	LMT	1912 Jan  1  # Lisbon Mean Time
			 0:00	Port	WE%sT	1966 Apr  3 2:00
			 1:00	-	CET	1976 Sep 26 1:00
			 0:00	Port	WE%sT	1983 Sep 25 1:00s
			 0:00	W-Eur	WE%sT	1992 Sep 27 1:00s
			 1:00	EU	CE%sT	1996 Mar 31 1:00u
			 0:00	EU	WE%sT
Zone Atlantic/Azores	-1:42:40 -	LMT	1884		# Ponta Delgada
			-1:54:32 -	HMT	1911 May 24  # Horta Mean Time
			-2:00	Port	AZO%sT	1966 Apr  3 2:00 # Azores Time
			-1:00	Port	AZO%sT	1983 Sep 25 1:00s
			-1:00	W-Eur	AZO%sT	1992 Sep 27 1:00s
			 0:00	EU	WE%sT	1993 Mar 28 1:00u
			-1:00	EU	AZO%sT
Zone Atlantic/Madeira	-1:07:36 -	LMT	1884		# Funchal
			-1:07:36 -	FMT	1911 May 24  # Funchal Mean Time
			-1:00	Port	MAD%sT	1966 Apr  3 2:00 # Madeira Time
			 0:00	Port	WE%sT	1983 Sep 25 1:00s
			 0:00	EU	WE%sT

# Romania
#
# From Paul Eggert (1999-10-07):
# <a href="http://www.nineoclock.ro/POL/1778pol.html">
# Nine O'clock</a> (1998-10-23) reports that the switch occurred at
# 04:00 local time in fall 1998.  For lack of better info,
# assume that Romania and Moldova switched to EU rules in 1997,
# the same year as Bulgaria.
#
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Romania	1932	only	-	May	21	 0:00s	1:00	S
Rule	Romania	1932	1939	-	Oct	Sun>=1	 0:00s	0	-
Rule	Romania	1933	1939	-	Apr	Sun>=2	 0:00s	1:00	S
Rule	Romania	1979	only	-	May	27	 0:00	1:00	S
Rule	Romania	1979	only	-	Sep	lastSun	 0:00	0	-
Rule	Romania	1980	only	-	Apr	 5	23:00	1:00	S
Rule	Romania	1980	only	-	Sep	lastSun	 1:00	0	-
Rule	Romania	1991	1993	-	Mar	lastSun	 0:00s	1:00	S
Rule	Romania	1991	1993	-	Sep	lastSun	 0:00s	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone Europe/Bucharest	1:44:24 -	LMT	1891 Oct
			1:44:24	-	BMT	1931 Jul 24	# Bucharest MT
			2:00	Romania	EE%sT	1981 Mar 29 2:00s
			2:00	C-Eur	EE%sT	1991
			2:00	Romania	EE%sT	1994
			2:00	E-Eur	EE%sT	1997
			2:00	EU	EE%sT

# Russia

# From Paul Eggert (2006-03-22):
# Except for Moscow after 1919-07-01, I invented the time zone abbreviations.
# Moscow time zone abbreviations after 1919-07-01, and Moscow rules after 1991,
# are from Andrey A. Chernov.  The rest is from Shanks & Pottenger,
# except we follow Chernov's report that 1992 DST transitions were Sat
# 23:00, not Sun 02:00s.
#
# From Stanislaw A. Kuzikowski (1994-06-29):
# But now it is some months since Novosibirsk is 3 hours ahead of Moscow!
# I do not know why they have decided to make this change;
# as far as I remember it was done exactly during winter->summer switching
# so we (Novosibirsk) simply did not switch.
#
# From Andrey A. Chernov (1996-10-04):
# `MSK' and `MSD' were born and used initially on Moscow computers with
# UNIX-like OSes by several developer groups (e.g. Demos group, Kiae group)....
# The next step was the UUCP network, the Relcom predecessor
# (used mainly for mail), and MSK/MSD was actively used there.
#
# From Chris Carrier (1996-10-30):
# According to a friend of mine who rode the Trans-Siberian Railroad from
# Moscow to Irkutsk in 1995, public air and rail transport in Russia ...
# still follows Moscow time, no matter where in Russia it is located.
#
# For Grozny, Chechnya, we have the following story from
# John Daniszewski, "Scavengers in the Rubble", Los Angeles Times (2001-02-07):
# News--often false--is spread by word of mouth.  A rumor that it was
# time to move the clocks back put this whole city out of sync with
# the rest of Russia for two weeks--even soldiers stationed here began
# enforcing curfew at the wrong time.
#
# From Gwillim Law (2001-06-05):
# There's considerable evidence that Sakhalin Island used to be in
# UTC+11, and has changed to UTC+10, in this decade.  I start with the
# SSIM, which listed Yuzhno-Sakhalinsk in zone RU10 along with Magadan
# until February 1997, and then in RU9 with Khabarovsk and Vladivostok
# since September 1997....  Although the Kuril Islands are
# administratively part of Sakhalin oblast', they appear to have
# remained on UTC+11 along with Magadan.
#
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
#
# Kaliningradskaya oblast'.
Zone Europe/Kaliningrad	 1:22:00 -	LMT	1893 Apr
			 1:00	C-Eur	CE%sT	1945
			 2:00	Poland	CE%sT	1946
			 3:00	Russia	MSK/MSD	1991 Mar 31 2:00s
			 2:00	Russia	EE%sT	2011 Mar 27 2:00s
			 3:00	-	FET # Further-eastern European Time
#
# From Oscar van Vlijmen (2001-08-25): [This region consists of]
# Respublika Adygeya, Arkhangel'skaya oblast',
# Belgorodskaya oblast', Bryanskaya oblast', Vladimirskaya oblast',
# Vologodskaya oblast', Voronezhskaya oblast',
# Respublika Dagestan, Ivanovskaya oblast', Respublika Ingushetiya,
# Kabarbino-Balkarskaya Respublika, Respublika Kalmykiya,
# Kalyzhskaya oblast', Respublika Karachaevo-Cherkessiya,
# Respublika Kareliya, Respublika Komi,
# Kostromskaya oblast', Krasnodarskij kraj, Kurskaya oblast',
# Leningradskaya oblast', Lipetskaya oblast', Respublika Marij El,
# Respublika Mordoviya, Moskva, Moskovskaya oblast',
# Murmanskaya oblast', Nenetskij avtonomnyj okrug,
# Nizhegorodskaya oblast', Novgorodskaya oblast', Orlovskaya oblast',
# Penzenskaya oblast', Pskovskaya oblast', Rostovskaya oblast',
# Ryazanskaya oblast', Sankt-Peterburg,
# Respublika Severnaya Osetiya, Smolenskaya oblast',
# Stavropol'skij kraj, Tambovskaya oblast', Respublika Tatarstan,
# Tverskaya oblast', Tyl'skaya oblast', Ul'yanovskaya oblast',
# Chechenskaya Respublika, Chuvashskaya oblast',
# Yaroslavskaya oblast'
Zone Europe/Moscow	 2:30:20 -	LMT	1880
			 2:30	-	MMT	1916 Jul  3 # Moscow Mean Time
			 2:30:48 Russia	%s	1919 Jul  1 2:00
			 3:00	Russia	MSK/MSD	1922 Oct
			 2:00	-	EET	1930 Jun 21
			 3:00	Russia	MSK/MSD	1991 Mar 31 2:00s
			 2:00	Russia	EE%sT	1992 Jan 19 2:00s
			 3:00	Russia	MSK/MSD	2011 Mar 27 2:00s
			 4:00	-	MSK
#
# Astrakhanskaya oblast', Kirovskaya oblast', Saratovskaya oblast',
# Volgogradskaya oblast'.  Shanks & Pottenger say Kirov is still at +0400
# but Wikipedia (2006-05-09) says +0300.  Perhaps it switched after the
# others?  But we have no data.
Zone Europe/Volgograd	 2:57:40 -	LMT	1920 Jan  3
			 3:00	-	TSAT	1925 Apr  6 # Tsaritsyn Time
			 3:00	-	STAT	1930 Jun 21 # Stalingrad Time
			 4:00	-	STAT	1961 Nov 11
			 4:00	Russia	VOL%sT	1989 Mar 26 2:00s # Volgograd T
			 3:00	Russia	VOL%sT	1991 Mar 31 2:00s
			 4:00	-	VOLT	1992 Mar 29 2:00s
			 3:00	Russia	VOL%sT	2011 Mar 27 2:00s
			 4:00	-	VOLT
#
# From Oscar van Vlijmen (2001-08-25): [This region consists of]
# Samarskaya oblast', Udmyrtskaya respublika
Zone Europe/Samara	 3:20:36 -	LMT	1919 Jul  1 2:00
			 3:00	-	SAMT	1930 Jun 21
			 4:00	-	SAMT	1935 Jan 27
			 4:00	Russia	KUY%sT	1989 Mar 26 2:00s # Kuybyshev
			 3:00	Russia	KUY%sT	1991 Mar 31 2:00s
			 2:00	Russia	KUY%sT	1991 Sep 29 2:00s
			 3:00	-	KUYT	1991 Oct 20 3:00
			 4:00	Russia	SAM%sT	2010 Mar 28 2:00s # Samara Time
			 3:00	Russia	SAM%sT	2011 Mar 27 2:00s
			 4:00	-	SAMT

#
# From Oscar van Vlijmen (2001-08-25): [This region consists of]
# Respublika Bashkortostan, Komi-Permyatskij avtonomnyj okrug,
# Kurganskaya oblast', Orenburgskaya oblast', Permskaya oblast',
# Sverdlovskaya oblast', Tyumenskaya oblast',
# Khanty-Manskijskij avtonomnyj okrug, Chelyabinskaya oblast',
# Yamalo-Nenetskij avtonomnyj okrug.
Zone Asia/Yekaterinburg	 4:02:24 -	LMT	1919 Jul 15 4:00
			 4:00	-	SVET	1930 Jun 21 # Sverdlovsk Time
			 5:00	Russia	SVE%sT	1991 Mar 31 2:00s
			 4:00	Russia	SVE%sT	1992 Jan 19 2:00s
			 5:00	Russia	YEK%sT	2011 Mar 27 2:00s
			 6:00	-	YEKT	# Yekaterinburg Time
#
# From Oscar van Vlijmen (2001-08-25): [This region consists of]
# Respublika Altaj, Altajskij kraj, Omskaya oblast'.
Zone Asia/Omsk		 4:53:36 -	LMT	1919 Nov 14
			 5:00	-	OMST	1930 Jun 21 # Omsk TIme
			 6:00	Russia	OMS%sT	1991 Mar 31 2:00s
			 5:00	Russia	OMS%sT	1992 Jan 19 2:00s
			 6:00	Russia	OMS%sT	2011 Mar 27 2:00s
			 7:00	-	OMST
#
# From Paul Eggert (2006-08-19): I'm guessing about Tomsk here; it's
# not clear when it switched from +7 to +6.
# Novosibirskaya oblast', Tomskaya oblast'.
Zone Asia/Novosibirsk	 5:31:40 -	LMT	1919 Dec 14 6:00
			 6:00	-	NOVT	1930 Jun 21 # Novosibirsk Time
			 7:00	Russia	NOV%sT	1991 Mar 31 2:00s
			 6:00	Russia	NOV%sT	1992 Jan 19 2:00s
			 7:00	Russia	NOV%sT	1993 May 23 # say Shanks & P.
			 6:00	Russia	NOV%sT	2011 Mar 27 2:00s
			 7:00	-	NOVT

# From Alexander Krivenyshev (2009-10-13):
# Kemerovo oblast' (Kemerovo region) in Russia will change current time zone on
# March 28, 2010:
# from current Russia Zone 6 - Krasnoyarsk Time Zone (KRA) UTC +0700
# to Russia Zone 5 - Novosibirsk Time Zone (NOV) UTC +0600
#
# This is according to Government of Russia decree # 740, on September
# 14, 2009 "Application in the territory of the Kemerovo region the Fifth
# time zone." ("Russia Zone 5" or old "USSR Zone 5" is GMT +0600)
#
# Russian Government web site (Russian language)
# <a href="http://www.government.ru/content/governmentactivity/rfgovernmentdecisions/archiv">
# http://www.government.ru/content/governmentactivity/rfgovernmentdecisions/archive/2009/09/14/991633.htm
# </a>
# or Russian-English translation by WorldTimeZone.com with reference
# map to local region and new Russia Time Zone map after March 28, 2010
# <a href="http://www.worldtimezone.com/dst_news/dst_news_russia03.html">
# http://www.worldtimezone.com/dst_news/dst_news_russia03.html
# </a>
#
# Thus, when Russia will switch to DST on the night of March 28, 2010
# Kemerovo region (Kemerovo oblast') will not change the clock.
#
# As a result, Kemerovo oblast' will be in the same time zone as
# Novosibirsk, Omsk, Tomsk, Barnaul and Altai Republic.

Zone Asia/Novokuznetsk	 5:48:48 -	NMT	1920 Jan  6
			 6:00	-	KRAT	1930 Jun 21 # Krasnoyarsk Time
			 7:00	Russia	KRA%sT	1991 Mar 31 2:00s
			 6:00	Russia	KRA%sT	1992 Jan 19 2:00s
			 7:00	Russia	KRA%sT	2010 Mar 28 2:00s
			 6:00	Russia	NOV%sT	2011 Mar 27 2:00s
			 7:00	-	NOVT # Novosibirsk/Novokuznetsk Time

#
# From Oscar van Vlijmen (2001-08-25): [This region consists of]
# Krasnoyarskij kraj,
# Tajmyrskij (Dolgano-Nenetskij) avtonomnyj okrug,
# Respublika Tuva, Respublika Khakasiya, Evenkijskij avtonomnyj okrug.
Zone Asia/Krasnoyarsk	 6:11:20 -	LMT	1920 Jan  6
			 6:00	-	KRAT	1930 Jun 21 # Krasnoyarsk Time
			 7:00	Russia	KRA%sT	1991 Mar 31 2:00s
			 6:00	Russia	KRA%sT	1992 Jan 19 2:00s
			 7:00	Russia	KRA%sT	2011 Mar 27 2:00s
			 8:00	-	KRAT
#
# From Oscar van Vlijmen (2001-08-25): [This region consists of]
# Respublika Buryatiya, Irkutskaya oblast',
# Ust'-Ordynskij Buryatskij avtonomnyj okrug.
Zone Asia/Irkutsk	 6:57:20 -	LMT	1880
			 6:57:20 -	IMT	1920 Jan 25 # Irkutsk Mean Time
			 7:00	-	IRKT	1930 Jun 21 # Irkutsk Time
			 8:00	Russia	IRK%sT	1991 Mar 31 2:00s
			 7:00	Russia	IRK%sT	1992 Jan 19 2:00s
			 8:00	Russia	IRK%sT	2011 Mar 27 2:00s
			 9:00	-	IRKT
#
# From Oscar van Vlijmen (2003-10-18): [This region consists of]
# Aginskij Buryatskij avtonomnyj okrug, Amurskaya oblast',
# [parts of] Respublika Sakha (Yakutiya), Chitinskaya oblast'.

# From Oscar van Vlijmen (2009-11-29):
# ...some regions of [Russia] were merged with others since 2005...
# Some names were changed, no big deal, except for one instance: a new name.
# YAK/YAKST: UTC+9 Zabajkal'skij kraj.

# From Oscar van Vlijmen (2009-11-29):
# The Sakha districts are: Aldanskij, Amginskij, Anabarskij,
# Verkhnevilyujskij, Vilyujskij, Gornyj,
# Zhiganskij, Kobyajskij, Lenskij, Megino-Kangalasskij, Mirninskij,
# Namskij, Nyurbinskij, Olenyokskij, Olyokminskij,
# Suntarskij, Tattinskij, Ust'-Aldanskij, Khangalasskij,
# Churapchinskij, Eveno-Bytantajskij Natsional'nij.

Zone Asia/Yakutsk	 8:38:40 -	LMT	1919 Dec 15
			 8:00	-	YAKT	1930 Jun 21 # Yakutsk Time
			 9:00	Russia	YAK%sT	1991 Mar 31 2:00s
			 8:00	Russia	YAK%sT	1992 Jan 19 2:00s
			 9:00	Russia	YAK%sT	2011 Mar 27 2:00s
			 10:00	-	YAKT
#
# From Oscar van Vlijmen (2003-10-18): [This region consists of]
# Evrejskaya avtonomnaya oblast', Khabarovskij kraj, Primorskij kraj,
# [parts of] Respublika Sakha (Yakutiya).

# From Oscar van Vlijmen (2009-11-29):
# The Sakha districts are: Bulunskij, Verkhoyanskij, Tomponskij, Ust'-Majskij,
# Ust'-Yanskij.
Zone Asia/Vladivostok	 8:47:44 -	LMT	1922 Nov 15
			 9:00	-	VLAT	1930 Jun 21 # Vladivostok Time
			10:00	Russia	VLA%sT	1991 Mar 31 2:00s
			 9:00	Russia	VLA%sST	1992 Jan 19 2:00s
			10:00	Russia	VLA%sT	2011 Mar 27 2:00s
			11:00	-	VLAT
#
# Sakhalinskaya oblast'.
# The Zone name should be Yuzhno-Sakhalinsk, but that's too long.
Zone Asia/Sakhalin	 9:30:48 -	LMT	1905 Aug 23
			 9:00	-	CJT	1938
			 9:00	-	JST	1945 Aug 25
			11:00	Russia	SAK%sT	1991 Mar 31 2:00s # Sakhalin T.
			10:00	Russia	SAK%sT	1992 Jan 19 2:00s
			11:00	Russia	SAK%sT	1997 Mar lastSun 2:00s
			10:00	Russia	SAK%sT	2011 Mar 27 2:00s
			11:00	-	SAKT
#
# From Oscar van Vlijmen (2003-10-18): [This region consists of]
# Magadanskaya oblast', Respublika Sakha (Yakutiya).
# Probably also: Kuril Islands.

# From Oscar van Vlijmen (2009-11-29):
# The Sakha districts are: Abyjskij, Allaikhovskij, Verkhhhnekolymskij, Momskij,
# Nizhnekolymskij, Ojmyakonskij, Srednekolymskij.
Zone Asia/Magadan	10:03:12 -	LMT	1924 May  2
			10:00	-	MAGT	1930 Jun 21 # Magadan Time
			11:00	Russia	MAG%sT	1991 Mar 31 2:00s
			10:00	Russia	MAG%sT	1992 Jan 19 2:00s
			11:00	Russia	MAG%sT	2011 Mar 27 2:00s
			12:00	-	MAGT
#
# From Oscar van Vlijmen (2001-08-25): [This region consists of]
# Kamchatskaya oblast', Koryakskij avtonomnyj okrug.
#
# The Zone name should be Asia/Petropavlovsk-Kamchatski, but that's too long.
Zone Asia/Kamchatka	10:34:36 -	LMT	1922 Nov 10
			11:00	-	PETT	1930 Jun 21 # P-K Time
			12:00	Russia	PET%sT	1991 Mar 31 2:00s
			11:00	Russia	PET%sT	1992 Jan 19 2:00s
			12:00	Russia	PET%sT	2010 Mar 28 2:00s
			11:00	Russia	PET%sT	2011 Mar 27 2:00s
			12:00	-	PETT
#
# Chukotskij avtonomnyj okrug
Zone Asia/Anadyr	11:49:56 -	LMT	1924 May  2
			12:00	-	ANAT	1930 Jun 21 # Anadyr Time
			13:00	Russia	ANA%sT	1982 Apr  1 0:00s
			12:00	Russia	ANA%sT	1991 Mar 31 2:00s
			11:00	Russia	ANA%sT	1992 Jan 19 2:00s
			12:00	Russia	ANA%sT	2010 Mar 28 2:00s
			11:00	Russia	ANA%sT	2011 Mar 27 2:00s
			12:00	-	ANAT

# Serbia
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Belgrade	1:22:00	-	LMT	1884
			1:00	-	CET	1941 Apr 18 23:00
			1:00	C-Eur	CE%sT	1945
			1:00	-	CET	1945 May 8 2:00s
			1:00	1:00	CEST	1945 Sep 16  2:00s
# Metod Kozelj reports that the legal date of
# transition to EU rules was 1982-11-27, for all of Yugoslavia at the time.
# Shanks & Pottenger don't give as much detail, so go with Kozelj.
			1:00	-	CET	1982 Nov 27
			1:00	EU	CE%sT
Link Europe/Belgrade Europe/Ljubljana	# Slovenia
Link Europe/Belgrade Europe/Podgorica	# Montenegro
Link Europe/Belgrade Europe/Sarajevo	# Bosnia and Herzegovina
Link Europe/Belgrade Europe/Skopje	# Macedonia
Link Europe/Belgrade Europe/Zagreb	# Croatia

# Slovakia
Link Europe/Prague Europe/Bratislava

# Slovenia
# see Serbia

# Spain
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
# For 1917-1919 Whitman gives Apr Sat>=1 - Oct Sat>=1;
# go with Shanks & Pottenger.
Rule	Spain	1917	only	-	May	 5	23:00s	1:00	S
Rule	Spain	1917	1919	-	Oct	 6	23:00s	0	-
Rule	Spain	1918	only	-	Apr	15	23:00s	1:00	S
Rule	Spain	1919	only	-	Apr	 5	23:00s	1:00	S
# Whitman gives 1921 Feb 28 - Oct 14; go with Shanks & Pottenger.
Rule	Spain	1924	only	-	Apr	16	23:00s	1:00	S
# Whitman gives 1924 Oct 14; go with Shanks & Pottenger.
Rule	Spain	1924	only	-	Oct	 4	23:00s	0	-
Rule	Spain	1926	only	-	Apr	17	23:00s	1:00	S
# Whitman says no DST in 1929; go with Shanks & Pottenger.
Rule	Spain	1926	1929	-	Oct	Sat>=1	23:00s	0	-
Rule	Spain	1927	only	-	Apr	 9	23:00s	1:00	S
Rule	Spain	1928	only	-	Apr	14	23:00s	1:00	S
Rule	Spain	1929	only	-	Apr	20	23:00s	1:00	S
# Whitman gives 1937 Jun 16, 1938 Apr 16, 1940 Apr 13;
# go with Shanks & Pottenger.
Rule	Spain	1937	only	-	May	22	23:00s	1:00	S
Rule	Spain	1937	1939	-	Oct	Sat>=1	23:00s	0	-
Rule	Spain	1938	only	-	Mar	22	23:00s	1:00	S
Rule	Spain	1939	only	-	Apr	15	23:00s	1:00	S
Rule	Spain	1940	only	-	Mar	16	23:00s	1:00	S
# Whitman says no DST 1942-1945; go with Shanks & Pottenger.
Rule	Spain	1942	only	-	May	 2	22:00s	2:00	M # Midsummer
Rule	Spain	1942	only	-	Sep	 1	22:00s	1:00	S
Rule	Spain	1943	1946	-	Apr	Sat>=13	22:00s	2:00	M
Rule	Spain	1943	only	-	Oct	 3	22:00s	1:00	S
Rule	Spain	1944	only	-	Oct	10	22:00s	1:00	S
Rule	Spain	1945	only	-	Sep	30	 1:00	1:00	S
Rule	Spain	1946	only	-	Sep	30	 0:00	0	-
Rule	Spain	1949	only	-	Apr	30	23:00	1:00	S
Rule	Spain	1949	only	-	Sep	30	 1:00	0	-
Rule	Spain	1974	1975	-	Apr	Sat>=13	23:00	1:00	S
Rule	Spain	1974	1975	-	Oct	Sun>=1	 1:00	0	-
Rule	Spain	1976	only	-	Mar	27	23:00	1:00	S
Rule	Spain	1976	1977	-	Sep	lastSun	 1:00	0	-
Rule	Spain	1977	1978	-	Apr	 2	23:00	1:00	S
Rule	Spain	1978	only	-	Oct	 1	 1:00	0	-
# The following rules are copied from Morocco from 1967 through 1978.
Rule SpainAfrica 1967	only	-	Jun	 3	12:00	1:00	S
Rule SpainAfrica 1967	only	-	Oct	 1	 0:00	0	-
Rule SpainAfrica 1974	only	-	Jun	24	 0:00	1:00	S
Rule SpainAfrica 1974	only	-	Sep	 1	 0:00	0	-
Rule SpainAfrica 1976	1977	-	May	 1	 0:00	1:00	S
Rule SpainAfrica 1976	only	-	Aug	 1	 0:00	0	-
Rule SpainAfrica 1977	only	-	Sep	28	 0:00	0	-
Rule SpainAfrica 1978	only	-	Jun	 1	 0:00	1:00	S
Rule SpainAfrica 1978	only	-	Aug	 4	 0:00	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Madrid	-0:14:44 -	LMT	1901 Jan  1  0:00s
			 0:00	Spain	WE%sT	1946 Sep 30
			 1:00	Spain	CE%sT	1979
			 1:00	EU	CE%sT
Zone	Africa/Ceuta	-0:21:16 -	LMT	1901
			 0:00	-	WET	1918 May  6 23:00
			 0:00	1:00	WEST	1918 Oct  7 23:00
			 0:00	-	WET	1924
			 0:00	Spain	WE%sT	1929
			 0:00 SpainAfrica WE%sT 1984 Mar 16
			 1:00	-	CET	1986
			 1:00	EU	CE%sT
Zone	Atlantic/Canary	-1:01:36 -	LMT	1922 Mar # Las Palmas de Gran C.
			-1:00	-	CANT	1946 Sep 30 1:00 # Canaries Time
			 0:00	-	WET	1980 Apr  6 0:00s
			 0:00	1:00	WEST	1980 Sep 28 0:00s
			 0:00	EU	WE%sT
# IATA SSIM (1996-09) says the Canaries switch at 2:00u, not 1:00u.
# Ignore this for now, as the Canaries are part of the EU.

# Sweden

# From Ivan Nilsson (2001-04-13), superseding Shanks & Pottenger:
#
# The law "Svensk forfattningssamling 1878, no 14" about standard time in 1879:
# From the beginning of 1879 (that is 01-01 00:00) the time for all
# places in the country is "the mean solar time for the meridian at
# three degrees, or twelve minutes of time, to the west of the
# meridian of the Observatory of Stockholm".  The law is dated 1878-05-31.
#
# The observatory at that time had the meridian 18 degrees 03' 30"
# eastern longitude = 01:12:14 in time.  Less 12 minutes gives the
# national standard time as 01:00:14 ahead of GMT....
#
# About the beginning of CET in Sweden. The lawtext ("Svensk
# forfattningssamling 1899, no 44") states, that "from the beginning
# of 1900... ... the same as the mean solar time for the meridian at
# the distance of one hour of time from the meridian of the English
# observatory at Greenwich, or at 12 minutes 14 seconds to the west
# from the meridian of the Observatory of Stockholm". The law is dated
# 1899-06-16.  In short: At 1900-01-01 00:00:00 the new standard time
# in Sweden is 01:00:00 ahead of GMT.
#
# 1916: The lawtext ("Svensk forfattningssamling 1916, no 124") states
# that "1916-05-15 is considered to begin one hour earlier". It is
# pretty obvious that at 05-14 23:00 the clocks are set to 05-15 00:00....
# Further the law says, that "1916-09-30 is considered to end one hour later".
#
# The laws regulating [DST] are available on the site of the Swedish
# Parliament beginning with 1985 - the laws regulating 1980/1984 are
# not available on the site (to my knowledge they are only available
# in Swedish): <http://www.riksdagen.se/english/work/sfst.asp> (type
# "sommartid" without the quotes in the field "Fritext" and then click
# the Sok-button).
#
# (2001-05-13):
#
# I have now found a newspaper stating that at 1916-10-01 01:00
# summertime the church-clocks etc were set back one hour to show
# 1916-10-01 00:00 standard time.  The article also reports that some
# people thought the switch to standard time would take place already
# at 1916-10-01 00:00 summer time, but they had to wait for another
# hour before the event took place.
#
# Source: The newspaper "Dagens Nyheter", 1916-10-01, page 7 upper left.

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone Europe/Stockholm	1:12:12 -	LMT	1879 Jan  1
			1:00:14	-	SET	1900 Jan  1	# Swedish Time
			1:00	-	CET	1916 May 14 23:00
			1:00	1:00	CEST	1916 Oct  1 01:00
			1:00	-	CET	1980
			1:00	EU	CE%sT

# Switzerland
# From Howse:
# By the end of the 18th century clocks and watches became commonplace
# and their performance improved enormously.  Communities began to keep
# mean time in preference to apparent time -- Geneva from 1780 ....
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
# From Whitman (who writes ``Midnight?''):
# Rule	Swiss	1940	only	-	Nov	 2	0:00	1:00	S
# Rule	Swiss	1940	only	-	Dec	31	0:00	0	-
# From Shanks & Pottenger:
# Rule	Swiss	1941	1942	-	May	Sun>=1	2:00	1:00	S
# Rule	Swiss	1941	1942	-	Oct	Sun>=1	0:00	0	-

# From Alois Treindl (2008-12-17):
# I have researched the DST usage in Switzerland during the 1940ies.
#
# As I wrote in an earlier message, I suspected the current tzdata values
# to be wrong. This is now verified.
#
# I have found copies of the original ruling by the Swiss Federal
# government, in 'Eidgen[o]ssische Gesetzessammlung 1941 and 1942' (Swiss
# federal law collection)...
#
# DST began on Monday 5 May 1941, 1:00 am by shifting the clocks to 2:00 am
# DST ended on Monday 6 Oct 1941, 2:00 am by shifting the clocks to 1:00 am.
#
# DST began on Monday, 4 May 1942 at 01:00 am
# DST ended on Monday, 5 Oct 1942 at 02:00 am
#
# There was no DST in 1940, I have checked the law collection carefully.
# It is also indicated by the fact that the 1942 entry in the law
# collection points back to 1941 as a reference, but no reference to any
# other years are made.
#
# Newspaper articles I have read in the archives on 6 May 1941 reported
# about the introduction of DST (Sommerzeit in German) during the previous
# night as an absolute novelty, because this was the first time that such
# a thing had happened in Switzerland.
#
# I have also checked 1916, because one book source (Gabriel, Traite de
# l'heure dans le monde) claims that Switzerland had DST in 1916. This is
# false, no official document could be found. Probably Gabriel got misled
# by references to Germany, which introduced DST in 1916 for the first time.
#
# The tzdata rules for Switzerland must be changed to:
# Rule  Swiss   1941    1942    -       May     Mon>=1  1:00    1:00    S
# Rule  Swiss   1941    1942    -       Oct     Mon>=1  2:00    0       -
#
# The 1940 rules must be deleted.
#
# One further detail for Switzerland, which is probably out of scope for
# most users of tzdata:
# The zone file
# Zone    Europe/Zurich   0:34:08 -       LMT     1848 Sep 12
#                          0:29:44 -       BMT     1894 Jun #Bern Mean Time
#                          1:00    Swiss   CE%sT   1981
#                          1:00    EU      CE%sT
# describes all of Switzerland correctly, with the exception of
# the Cantone Geneve (Geneva, Genf). Between 1848 and 1894 Geneve did not
# follow Bern Mean Time but kept its own local mean time.
# To represent this, an extra zone would be needed.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Swiss	1941	1942	-	May	Mon>=1	1:00	1:00	S
Rule	Swiss	1941	1942	-	Oct	Mon>=1	2:00	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Zurich	0:34:08 -	LMT	1848 Sep 12
			0:29:44	-	BMT	1894 Jun # Bern Mean Time
			1:00	Swiss	CE%sT	1981
			1:00	EU	CE%sT

# Turkey

# From Amar Devegowda (2007-01-03):
# The time zone rules for Istanbul, Turkey have not been changed for years now.
# ... The latest rules are available at -
# http://www.timeanddate.com/worldclock/timezone.html?n=107
# From Steffen Thorsen (2007-01-03):
# I have been able to find press records back to 1996 which all say that
# DST started 01:00 local time and end at 02:00 local time.  I am not sure
# what happened before that.  One example for each year from 1996 to 2001:
# http://newspot.byegm.gov.tr/arsiv/1996/21/N4.htm
# http://www.byegm.gov.tr/YAYINLARIMIZ/CHR/ING97/03/97X03X25.TXT
# http://www.byegm.gov.tr/YAYINLARIMIZ/CHR/ING98/03/98X03X02.HTM
# http://www.byegm.gov.tr/YAYINLARIMIZ/CHR/ING99/10/99X10X26.HTM#%2016
# http://www.byegm.gov.tr/YAYINLARIMIZ/CHR/ING2000/03/00X03X06.HTM#%2021
# http://www.byegm.gov.tr/YAYINLARIMIZ/CHR/ING2001/03/23x03x01.HTM#%2027
# From Paul Eggert (2007-01-03):
# Prefer the above source to Shanks & Pottenger for time stamps after 1990.

# From Steffen Thorsen (2007-03-09):
# Starting 2007 though, it seems that they are adopting EU's 1:00 UTC
# start/end time, according to the following page (2007-03-07):
# http://www.ntvmsnbc.com/news/402029.asp
# The official document is located here - it is in Turkish...:
# http://rega.basbakanlik.gov.tr/eskiler/2007/03/20070307-7.htm
# I was able to locate the following seemingly official document
# (on a non-government server though) describing dates between 2002 and 2006:
# http://www.alomaliye.com/bkk_2002_3769.htm

# From G&ouml;kdeniz Karada&#x011f; (2011-03-10):
#
# According to the articles linked below, Turkey will change into summer
# time zone (GMT+3) on March 28, 2011 at 3:00 a.m. instead of March 27.
# This change is due to a nationwide exam on 27th.
#
# <a href="http://www.worldbulletin.net/?aType=haber&ArticleID=70872">
# http://www.worldbulletin.net/?aType=haber&ArticleID=70872
# </a>
# Turkish:
# <a href="http://www.hurriyet.com.tr/ekonomi/17230464.asp?gid=373">
# http://www.hurriyet.com.tr/ekonomi/17230464.asp?gid=373
# </a>

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Turkey	1916	only	-	May	 1	0:00	1:00	S
Rule	Turkey	1916	only	-	Oct	 1	0:00	0	-
Rule	Turkey	1920	only	-	Mar	28	0:00	1:00	S
Rule	Turkey	1920	only	-	Oct	25	0:00	0	-
Rule	Turkey	1921	only	-	Apr	 3	0:00	1:00	S
Rule	Turkey	1921	only	-	Oct	 3	0:00	0	-
Rule	Turkey	1922	only	-	Mar	26	0:00	1:00	S
Rule	Turkey	1922	only	-	Oct	 8	0:00	0	-
# Whitman gives 1923 Apr 28 - Sep 16 and no DST in 1924-1925;
# go with Shanks & Pottenger.
Rule	Turkey	1924	only	-	May	13	0:00	1:00	S
Rule	Turkey	1924	1925	-	Oct	 1	0:00	0	-
Rule	Turkey	1925	only	-	May	 1	0:00	1:00	S
Rule	Turkey	1940	only	-	Jun	30	0:00	1:00	S
Rule	Turkey	1940	only	-	Oct	 5	0:00	0	-
Rule	Turkey	1940	only	-	Dec	 1	0:00	1:00	S
Rule	Turkey	1941	only	-	Sep	21	0:00	0	-
Rule	Turkey	1942	only	-	Apr	 1	0:00	1:00	S
# Whitman omits the next two transition and gives 1945 Oct 1;
# go with Shanks & Pottenger.
Rule	Turkey	1942	only	-	Nov	 1	0:00	0	-
Rule	Turkey	1945	only	-	Apr	 2	0:00	1:00	S
Rule	Turkey	1945	only	-	Oct	 8	0:00	0	-
Rule	Turkey	1946	only	-	Jun	 1	0:00	1:00	S
Rule	Turkey	1946	only	-	Oct	 1	0:00	0	-
Rule	Turkey	1947	1948	-	Apr	Sun>=16	0:00	1:00	S
Rule	Turkey	1947	1950	-	Oct	Sun>=2	0:00	0	-
Rule	Turkey	1949	only	-	Apr	10	0:00	1:00	S
Rule	Turkey	1950	only	-	Apr	19	0:00	1:00	S
Rule	Turkey	1951	only	-	Apr	22	0:00	1:00	S
Rule	Turkey	1951	only	-	Oct	 8	0:00	0	-
Rule	Turkey	1962	only	-	Jul	15	0:00	1:00	S
Rule	Turkey	1962	only	-	Oct	 8	0:00	0	-
Rule	Turkey	1964	only	-	May	15	0:00	1:00	S
Rule	Turkey	1964	only	-	Oct	 1	0:00	0	-
Rule	Turkey	1970	1972	-	May	Sun>=2	0:00	1:00	S
Rule	Turkey	1970	1972	-	Oct	Sun>=2	0:00	0	-
Rule	Turkey	1973	only	-	Jun	 3	1:00	1:00	S
Rule	Turkey	1973	only	-	Nov	 4	3:00	0	-
Rule	Turkey	1974	only	-	Mar	31	2:00	1:00	S
Rule	Turkey	1974	only	-	Nov	 3	5:00	0	-
Rule	Turkey	1975	only	-	Mar	30	0:00	1:00	S
Rule	Turkey	1975	1976	-	Oct	lastSun	0:00	0	-
Rule	Turkey	1976	only	-	Jun	 1	0:00	1:00	S
Rule	Turkey	1977	1978	-	Apr	Sun>=1	0:00	1:00	S
Rule	Turkey	1977	only	-	Oct	16	0:00	0	-
Rule	Turkey	1979	1980	-	Apr	Sun>=1	3:00	1:00	S
Rule	Turkey	1979	1982	-	Oct	Mon>=11	0:00	0	-
Rule	Turkey	1981	1982	-	Mar	lastSun	3:00	1:00	S
Rule	Turkey	1983	only	-	Jul	31	0:00	1:00	S
Rule	Turkey	1983	only	-	Oct	 2	0:00	0	-
Rule	Turkey	1985	only	-	Apr	20	0:00	1:00	S
Rule	Turkey	1985	only	-	Sep	28	0:00	0	-
Rule	Turkey	1986	1990	-	Mar	lastSun	2:00s	1:00	S
Rule	Turkey	1986	1990	-	Sep	lastSun	2:00s	0	-
Rule	Turkey	1991	2006	-	Mar	lastSun	1:00s	1:00	S
Rule	Turkey	1991	1995	-	Sep	lastSun	1:00s	0	-
Rule	Turkey	1996	2006	-	Oct	lastSun	1:00s	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Istanbul	1:55:52 -	LMT	1880
			1:56:56	-	IMT	1910 Oct # Istanbul Mean Time?
			2:00	Turkey	EE%sT	1978 Oct 15
			3:00	Turkey	TR%sT	1985 Apr 20 # Turkey Time
			2:00	Turkey	EE%sT	2007
			2:00	EU	EE%sT	2011 Mar 27 1:00u
			2:00	-	EET	2011 Mar 28 1:00u
			2:00	EU	EE%sT
Link	Europe/Istanbul	Asia/Istanbul	# Istanbul is in both continents.

# Ukraine
#
# From Igor Karpov, who works for the Ukranian Ministry of Justice,
# via Garrett Wollman (2003-01-27):
# BTW, I've found the official document on this matter. It's goverment
# regulations number 509, May 13, 1996. In my poor translation it says:
# "Time in Ukraine is set to second timezone (Kiev time). Each last Sunday
# of March at 3am the time is changing to 4am and each last Sunday of
# October the time at 4am is changing to 3am"

# From Alexander Krivenyshev (2011-09-20):
# On September 20, 2011 the deputies of the Verkhovna Rada agreed to
# abolish the transfer clock to winter time.
#
# Bill number 8330 of MP from the Party of Regions Oleg Nadoshi got
# approval from 266 deputies.
#
# Ukraine abolishes transter back to the winter time (in Russian)
# <a href="http://news.mail.ru/politics/6861560/">
# http://news.mail.ru/politics/6861560/
# </a>
#
# The Ukrainians will no longer change the clock (in Russian)
# <a href="http://www.segodnya.ua/news/14290482.html">
# http://www.segodnya.ua/news/14290482.html
# </a>
#
# Deputies cancelled the winter time (in Russian)
# <a href="http://www.pravda.com.ua/rus/news/2011/09/20/6600616/">
# http://www.pravda.com.ua/rus/news/2011/09/20/6600616/
# </a>
#
# From Philip Pizzey (2011-10-18):
# Today my Ukrainian colleagues have informed me that the
# Ukrainian parliament have decided that they will go to winter
# time this year after all.
#
# From Udo Schwedt (2011-10-18):
# As far as I understand, the recent change to the Ukranian time zone
# (Europe/Kiev) to introduce permanent daylight saving time (similar
# to Russia) was reverted today:
#
# <a href="http://portal.rada.gov.ua/rada/control/en/publish/article/info_left?art_id=287324&cat_id=105995">
# http://portal.rada.gov.ua/rada/control/en/publish/article/info_left?art_id=287324&cat_id=105995
# </a>
#
# Also reported by Alexander Bokovoy (2011-10-18) who also noted:
# The law documents themselves are at
#
# <a href="http://w1.c1.rada.gov.ua/pls/zweb_n/webproc4_1?id=&pf3511=41484">
# http://w1.c1.rada.gov.ua/pls/zweb_n/webproc4_1?id=&pf3511=41484
# </a>


# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
# Most of Ukraine since 1970 has been like Kiev.
# "Kyiv" is the transliteration of the Ukrainian name, but
# "Kiev" is more common in English.
Zone Europe/Kiev	2:02:04 -	LMT	1880
			2:02:04	-	KMT	1924 May  2 # Kiev Mean Time
			2:00	-	EET	1930 Jun 21
			3:00	-	MSK	1941 Sep 20
			1:00	C-Eur	CE%sT	1943 Nov  6
			3:00	Russia	MSK/MSD	1990
			3:00	-	MSK	1990 Jul  1 2:00
			2:00	-	EET	1992
			2:00	E-Eur	EE%sT	1995
			2:00	EU	EE%sT
# Ruthenia used CET 1990/1991.
# "Uzhhorod" is the transliteration of the Ukrainian name, but
# "Uzhgorod" is more common in English.
Zone Europe/Uzhgorod	1:29:12 -	LMT	1890 Oct
			1:00	-	CET	1940
			1:00	C-Eur	CE%sT	1944 Oct
			1:00	1:00	CEST	1944 Oct 26
			1:00	-	CET	1945 Jun 29
			3:00	Russia	MSK/MSD	1990
			3:00	-	MSK	1990 Jul  1 2:00
			1:00	-	CET	1991 Mar 31 3:00
			2:00	-	EET	1992
			2:00	E-Eur	EE%sT	1995
			2:00	EU	EE%sT
# Zaporozh'ye and eastern Lugansk oblasts observed DST 1990/1991.
# "Zaporizhia" is the transliteration of the Ukrainian name, but
# "Zaporozh'ye" is more common in English.  Use the common English
# spelling, except omit the apostrophe as it is not allowed in
# portable Posix file names.
Zone Europe/Zaporozhye	2:20:40 -	LMT	1880
			2:20	-	CUT	1924 May  2 # Central Ukraine T
			2:00	-	EET	1930 Jun 21
			3:00	-	MSK	1941 Aug 25
			1:00	C-Eur	CE%sT	1943 Oct 25
			3:00	Russia	MSK/MSD	1991 Mar 31 2:00
			2:00	E-Eur	EE%sT	1995
			2:00	EU	EE%sT
# Central Crimea used Moscow time 1994/1997.
Zone Europe/Simferopol	2:16:24 -	LMT	1880
			2:16	-	SMT	1924 May  2 # Simferopol Mean T
			2:00	-	EET	1930 Jun 21
			3:00	-	MSK	1941 Nov
			1:00	C-Eur	CE%sT	1944 Apr 13
			3:00	Russia	MSK/MSD	1990
			3:00	-	MSK	1990 Jul  1 2:00
			2:00	-	EET	1992
# From Paul Eggert (2006-03-22):
# The _Economist_ (1994-05-28, p 45) reports that central Crimea switched
# from Kiev to Moscow time sometime after the January 1994 elections.
# Shanks (1999) says ``date of change uncertain'', but implies that it happened
# sometime between the 1994 DST switches.  Shanks & Pottenger simply say
# 1994-09-25 03:00, but that can't be right.  For now, guess it
# changed in May.
			2:00	E-Eur	EE%sT	1994 May
# From IATA SSIM (1994/1997), which also says that Kerch is still like Kiev.
			3:00	E-Eur	MSK/MSD	1996 Mar 31 3:00s
			3:00	1:00	MSD	1996 Oct 27 3:00s
# IATA SSIM (1997-09) says Crimea switched to EET/EEST.
# Assume it happened in March by not changing the clocks.
			3:00	Russia	MSK/MSD	1997
			3:00	-	MSK	1997 Mar lastSun 1:00u
			2:00	EU	EE%sT

###############################################################################

# One source shows that Bulgaria, Cyprus, Finland, and Greece observe DST from
# the last Sunday in March to the last Sunday in September in 1986.
# The source shows Romania changing a day later than everybody else.
#
# According to Bernard Sieloff's source, Poland is in the MET time zone but
# uses the WE DST rules.  The Western USSR uses EET+1 and ME DST rules.
# Bernard Sieloff's source claims Romania switches on the same day, but at
# 00:00 standard time (i.e., 01:00 DST).  It also claims that Turkey
# switches on the same day, but switches on at 01:00 standard time
# and off at 00:00 standard time (i.e., 01:00 DST)

# ...
# Date: Wed, 28 Jan 87 16:56:27 -0100
# From: Tom Hofmann
# ...
#
# ...the European time rules are...standardized since 1981, when
# most European coun[tr]ies started DST.  Before that year, only
# a few countries (UK, France, Italy) had DST, each according
# to own national rules.  In 1981, however, DST started on
# 'Apr firstSun', and not on 'Mar lastSun' as in the following
# years...
# But also since 1981 there are some more national exceptions
# than listed in 'europe': Switzerland, for example, joined DST
# one year later, Denmark ended DST on 'Oct 1' instead of 'Sep
# lastSun' in 1981---I don't know how they handle now.
#
# Finally, DST ist always from 'Apr 1' to 'Oct 1' in the
# Soviet Union (as far as I know).
#
# Tom Hofmann, Scientific Computer Center, CIBA-GEIGY AG,
# 4002 Basle, Switzerland
# ...

# ...
# Date: Wed, 4 Feb 87 22:35:22 +0100
# From: Dik T. Winter
# ...
#
# The information from Tom Hofmann is (as far as I know) not entirely correct.
# After a request from chongo at amdahl I tried to retrieve all information
# about DST in Europe.  I was able to find all from about 1969.
#
# ...standardization on DST in Europe started in about 1977 with switches on
# first Sunday in April and last Sunday in September...
# In 1981 UK joined Europe insofar that
# the starting day for both shifted to last Sunday in March.  And from 1982
# the whole of Europe used DST, with switch dates April 1 and October 1 in
# the Sov[i]et Union.  In 1985 the SU reverted to standard Europe[a]n switch
# dates...
#
# It should also be remembered that time-zones are not constants; e.g.
# Portugal switched in 1976 from MET (or CET) to WET with DST...
# Note also that though there were rules for switch dates not
# all countries abided to these dates, and many individual deviations
# occurred, though not since 1982 I believe.  Another note: it is always
# assumed that DST is 1 hour ahead of normal time, this need not be the
# case; at least in the Netherlands there have been times when DST was 2 hours
# in advance of normal time.
#
# ...
# dik t. winter, cwi, amsterdam, nederland
# ...

# From Bob Devine (1988-01-28):
# ...
# Greece: Last Sunday in April to last Sunday in September (iffy on dates).
# Since 1978.  Change at midnight.
# ...
# Monaco: has same DST as France.
# ...
