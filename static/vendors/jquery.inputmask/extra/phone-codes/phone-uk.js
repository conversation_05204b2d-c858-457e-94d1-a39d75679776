[
    { "mask": "+44(113)-###-####", "cc": "UK", "cd": "United Kingdom", "city": "Leeds" },
    { "mask": "+44(114)-###-####", "cc": "UK", "cd": "United Kingdom", "city": "Sheffield" },
    { "mask": "+44(115)-###-####", "cc": "UK", "cd": "United Kingdom", "city": "Nottingham" },
    { "mask": "+44(116)-###-####", "cc": "UK", "cd": "United Kingdom", "city": "Leicester" },
    { "mask": "+44(117)-###-####", "cc": "UK", "cd": "United Kingdom", "city": "Bristol" },
    { "mask": "+44(118)-###-####", "cc": "UK", "cd": "United Kingdom", "city": "Reading" },
    { "mask": "+44(1200)-######", "cc": "UK", "cd": "United Kingdom", "city": "Clitheroe" },
    { "mask": "+44(1202)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bournemouth" },
    { "mask": "+44(1204)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bolton" },
    { "mask": "+44(1205)-######", "cc": "UK", "cd": "United Kingdom", "city": "Boston" },
    { "mask": "+44(1206)-######", "cc": "UK", "cd": "United Kingdom", "city": "Colchester" },
    { "mask": "+44(1207)-######", "cc": "UK", "cd": "United Kingdom", "city": "Consett" },
    { "mask": "+44(1208)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bodmin" },
    { "mask": "+44(1209)-######", "cc": "UK", "cd": "United Kingdom", "city": "Redruth" },
    { "mask": "+44(121)-###-####", "cc": "UK", "cd": "United Kingdom", "city": "Birmingham" },
    { "mask": "+44(1223)-######", "cc": "UK", "cd": "United Kingdom", "city": "Cambridge" },
    { "mask": "+44(1224)-######", "cc": "UK", "cd": "United Kingdom", "city": "Aberdeen" },
    { "mask": "+44(1225)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bath" },
    { "mask": "+44(1226)-######", "cc": "UK", "cd": "United Kingdom", "city": "Barnsley" },
    { "mask": "+44(1227)-######", "cc": "UK", "cd": "United Kingdom", "city": "Canterbury" },
    { "mask": "+44(1228)-######", "cc": "UK", "cd": "United Kingdom", "city": "Carlisle" },
    { "mask": "+44(1229)-######", "cc": "UK", "cd": "United Kingdom", "city": "Barrow-in-Furness(2,4,5,6,8), Millom(3,7,9)" },
    { "mask": "+44(1233)-######", "cc": "UK", "cd": "United Kingdom", "city": "Ashford (Kent)" },
    { "mask": "+44(1234)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bedford" },
    { "mask": "+44(1235)-######", "cc": "UK", "cd": "United Kingdom", "city": "Abingdon" },
    { "mask": "+44(1236)-######", "cc": "UK", "cd": "United Kingdom", "city": "Coatbridge" },
    { "mask": "+44(1237)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bideford" },
    { "mask": "+44(1239)-######", "cc": "UK", "cd": "United Kingdom", "city": "Cardigan" },
    { "mask": "+44(1241)-######", "cc": "UK", "cd": "United Kingdom", "city": "Arbroath" },
    { "mask": "+44(1242)-######", "cc": "UK", "cd": "United Kingdom", "city": "Cheltenham" },
    { "mask": "+44(1243)-######", "cc": "UK", "cd": "United Kingdom", "city": "Chichester" },
    { "mask": "+44(1244)-######", "cc": "UK", "cd": "United Kingdom", "city": "Chester" },
    { "mask": "+44(1245)-######", "cc": "UK", "cd": "United Kingdom", "city": "Chelmsford" },
    { "mask": "+44(1246)-######", "cc": "UK", "cd": "United Kingdom", "city": "Chesterfield" },
    { "mask": "+44(1248)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bangor (Gwynedd)" },
    { "mask": "+44(1249)-######", "cc": "UK", "cd": "United Kingdom", "city": "Chippenham" },
    { "mask": "+44(1250)-######", "cc": "UK", "cd": "United Kingdom", "city": "Blairgowrie" },
    { "mask": "+44(1252)-######", "cc": "UK", "cd": "United Kingdom", "city": "Aldershot" },
    { "mask": "+44(1253)-######", "cc": "UK", "cd": "United Kingdom", "city": "Blackpool" },
    { "mask": "+44(1254)-######", "cc": "UK", "cd": "United Kingdom", "city": "Blackburn" },
    { "mask": "+44(1255)-######", "cc": "UK", "cd": "United Kingdom", "city": "Clacton-on-Sea" },
    { "mask": "+44(1256)-######", "cc": "UK", "cd": "United Kingdom", "city": "Basingstoke" },
    { "mask": "+44(1257)-######", "cc": "UK", "cd": "United Kingdom", "city": "Coppull" },
    { "mask": "+44(1258)-######", "cc": "UK", "cd": "United Kingdom", "city": "Blandford" },
    { "mask": "+44(1259)-######", "cc": "UK", "cd": "United Kingdom", "city": "Alloa" },
    { "mask": "+44(1260)-######", "cc": "UK", "cd": "United Kingdom", "city": "Congleton" },
    { "mask": "+44(1261)-######", "cc": "UK", "cd": "United Kingdom", "city": "Banff" },
    { "mask": "+44(1262)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bridlington" },
    { "mask": "+44(1263)-######", "cc": "UK", "cd": "United Kingdom", "city": "Cromer" },
    { "mask": "+44(1264)-######", "cc": "UK", "cd": "United Kingdom", "city": "Andover" },
    { "mask": "+44(1267)-######", "cc": "UK", "cd": "United Kingdom", "city": "Carmarthen" },
    { "mask": "+44(1268)-######", "cc": "UK", "cd": "United Kingdom", "city": "Basildon" },
    { "mask": "+44(1269)-######", "cc": "UK", "cd": "United Kingdom", "city": "Ammanford" },
    { "mask": "+44(1270)-######", "cc": "UK", "cd": "United Kingdom", "city": "Crewe" },
    { "mask": "+44(1271)-######", "cc": "UK", "cd": "United Kingdom", "city": "Barnstaple" },
    { "mask": "+44(1273)-######", "cc": "UK", "cd": "United Kingdom", "city": "Brighton" },
    { "mask": "+44(1274)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bradford" },
    { "mask": "+44(1275)-######", "cc": "UK", "cd": "United Kingdom", "city": "Clevedon" },
    { "mask": "+44(1276)-######", "cc": "UK", "cd": "United Kingdom", "city": "Camberley" },
    { "mask": "+44(1277)-######", "cc": "UK", "cd": "United Kingdom", "city": "Brentwood" },
    { "mask": "+44(1278)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bridgwater" },
    { "mask": "+44(1279)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bishops Stortford" },
    { "mask": "+44(1280)-######", "cc": "UK", "cd": "United Kingdom", "city": "Buckingham" },
    { "mask": "+44(1282)-######", "cc": "UK", "cd": "United Kingdom", "city": "Burnley" },
    { "mask": "+44(1283)-######", "cc": "UK", "cd": "United Kingdom", "city": "Burton-on-Trent" },
    { "mask": "+44(1284)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bury St Edmunds" },
    { "mask": "+44(1285)-######", "cc": "UK", "cd": "United Kingdom", "city": "Cirencester" },
    { "mask": "+44(1286)-######", "cc": "UK", "cd": "United Kingdom", "city": "Caernarfon" },
    { "mask": "+44(1287)-######", "cc": "UK", "cd": "United Kingdom", "city": "Guisborough" },
    { "mask": "+44(1288)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bude" },
    { "mask": "+44(1289)-######", "cc": "UK", "cd": "United Kingdom", "city": "Berwick-upon-Tweed" },
    { "mask": "+44(1290)-######", "cc": "UK", "cd": "United Kingdom", "city": "Cumnock" },
    { "mask": "+44(1291)-######", "cc": "UK", "cd": "United Kingdom", "city": "Chepstow" },
    { "mask": "+44(1292)-######", "cc": "UK", "cd": "United Kingdom", "city": "Ayr" },
    { "mask": "+44(1293)-######", "cc": "UK", "cd": "United Kingdom", "city": "Crawley" },
    { "mask": "+44(1294)-######", "cc": "UK", "cd": "United Kingdom", "city": "Ardrossan" },
    { "mask": "+44(1295)-######", "cc": "UK", "cd": "United Kingdom", "city": "Banbury" },
    { "mask": "+44(1296)-######", "cc": "UK", "cd": "United Kingdom", "city": "Aylesbury" },
    { "mask": "+44(1297)-######", "cc": "UK", "cd": "United Kingdom", "city": "Axminster" },
    { "mask": "+44(1298)-######", "cc": "UK", "cd": "United Kingdom", "city": "Buxton" },
    { "mask": "+44(1299)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bewdley" },
    { "mask": "+44(1300)-######", "cc": "UK", "cd": "United Kingdom", "city": "Cerne Abbas" },
    { "mask": "+44(1301)-######", "cc": "UK", "cd": "United Kingdom", "city": "Arrochar" },
    { "mask": "+44(1302)-######", "cc": "UK", "cd": "United Kingdom", "city": "Doncaster" },
    { "mask": "+44(1303)-######", "cc": "UK", "cd": "United Kingdom", "city": "Folkestone" },
    { "mask": "+44(1304)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dover" },
    { "mask": "+44(1305)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dorchester" },
    { "mask": "+44(1306)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dorking" },
    { "mask": "+44(1307)-######", "cc": "UK", "cd": "United Kingdom", "city": "Forfar" },
    { "mask": "+44(1308)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bridport" },
    { "mask": "+44(1309)-######", "cc": "UK", "cd": "United Kingdom", "city": "Forres" },
    { "mask": "+44(131)-###-###", "cc": "UK", "cd": "United Kingdom", "city": "Edinburgh" },
    { "mask": "+44(1320)-######", "cc": "UK", "cd": "United Kingdom", "city": "Fort Augustus" },
    { "mask": "+44(1322)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dartford" },
    { "mask": "+44(1323)-######", "cc": "UK", "cd": "United Kingdom", "city": "Eastbourne" },
    { "mask": "+44(1324)-######", "cc": "UK", "cd": "United Kingdom", "city": "Falkirk" },
    { "mask": "+44(1325)-######", "cc": "UK", "cd": "United Kingdom", "city": "Darlington" },
    { "mask": "+44(1326)-######", "cc": "UK", "cd": "United Kingdom", "city": "Falmouth" },
    { "mask": "+44(1327)-######", "cc": "UK", "cd": "United Kingdom", "city": "Daventry" },
    { "mask": "+44(1328)-######", "cc": "UK", "cd": "United Kingdom", "city": "Fakenham" },
    { "mask": "+44(1329)-######", "cc": "UK", "cd": "United Kingdom", "city": "Fareham" },
    { "mask": "+44(1330)-######", "cc": "UK", "cd": "United Kingdom", "city": "Banchory" },
    { "mask": "+44(1332)-######", "cc": "UK", "cd": "United Kingdom", "city": "Derby" },
    { "mask": "+44(1333)-######", "cc": "UK", "cd": "United Kingdom", "city": "Peat Inn" },
    { "mask": "+44(1334)-######", "cc": "UK", "cd": "United Kingdom", "city": "St Andrews" },
    { "mask": "+44(1335)-######", "cc": "UK", "cd": "United Kingdom", "city": "Ashbourne" },
    { "mask": "+44(1337)-######", "cc": "UK", "cd": "United Kingdom", "city": "Ladybank" },
    { "mask": "+44(1339)-######", "cc": "UK", "cd": "United Kingdom", "city": "Aboyne(2,3,5,8), Ballater(4,6,7,9)" },
    { "mask": "+44(1340)-######", "cc": "UK", "cd": "United Kingdom", "city": "Craigellachie" },
    { "mask": "+44(1341)-######", "cc": "UK", "cd": "United Kingdom", "city": "Barmouth" },
    { "mask": "+44(1342)-######", "cc": "UK", "cd": "United Kingdom", "city": "East Grinstead" },
    { "mask": "+44(1343)-######", "cc": "UK", "cd": "United Kingdom", "city": "Elgin" },
    { "mask": "+44(1344)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bracknell" },
    { "mask": "+44(1346)-######", "cc": "UK", "cd": "United Kingdom", "city": "Fraserburgh" },
    { "mask": "+44(1347)-######", "cc": "UK", "cd": "United Kingdom", "city": "Easingwold" },
    { "mask": "+44(1348)-######", "cc": "UK", "cd": "United Kingdom", "city": "Fishguard" },
    { "mask": "+44(1349)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dingwall" },
    { "mask": "+44(1350)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dunkeld" },
    { "mask": "+44(1352)-######", "cc": "UK", "cd": "United Kingdom", "city": "Mold" },
    { "mask": "+44(1353)-######", "cc": "UK", "cd": "United Kingdom", "city": "Ely" },
    { "mask": "+44(1354)-######", "cc": "UK", "cd": "United Kingdom", "city": "Chatteris" },
    { "mask": "+44(1355)-######", "cc": "UK", "cd": "United Kingdom", "city": "East Kilbride" },
    { "mask": "+44(1356)-######", "cc": "UK", "cd": "United Kingdom", "city": "Brechin" },
    { "mask": "+44(1357)-######", "cc": "UK", "cd": "United Kingdom", "city": "Strathaven" },
    { "mask": "+44(1358)-######", "cc": "UK", "cd": "United Kingdom", "city": "Ellon" },
    { "mask": "+44(1359)-######", "cc": "UK", "cd": "United Kingdom", "city": "Pakenham" },
    { "mask": "+44(1360)-######", "cc": "UK", "cd": "United Kingdom", "city": "Killearn" },
    { "mask": "+44(1361)-######", "cc": "UK", "cd": "United Kingdom", "city": "Duns" },
    { "mask": "+44(1362)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dereham" },
    { "mask": "+44(1363)-######", "cc": "UK", "cd": "United Kingdom", "city": "Crediton" },
    { "mask": "+44(1364)-######", "cc": "UK", "cd": "United Kingdom", "city": "Ashburton" },
    { "mask": "+44(1366)-######", "cc": "UK", "cd": "United Kingdom", "city": "Downham Market" },
    { "mask": "+44(1367)-######", "cc": "UK", "cd": "United Kingdom", "city": "Faringdon" },
    { "mask": "+44(1368)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dunbar" },
    { "mask": "+44(1369)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dunoon" },
    { "mask": "+44(1371)-######", "cc": "UK", "cd": "United Kingdom", "city": "Great Dunmow" },
    { "mask": "+44(1372)-######", "cc": "UK", "cd": "United Kingdom", "city": "Esher" },
    { "mask": "+44(1373)-######", "cc": "UK", "cd": "United Kingdom", "city": "Frome" },
    { "mask": "+44(1375)-######", "cc": "UK", "cd": "United Kingdom", "city": "Grays Thurrock" },
    { "mask": "+44(1376)-######", "cc": "UK", "cd": "United Kingdom", "city": "Braintree" },
    { "mask": "+44(1377)-######", "cc": "UK", "cd": "United Kingdom", "city": "Driffield" },
    { "mask": "+44(1379)-######", "cc": "UK", "cd": "United Kingdom", "city": "Diss" },
    { "mask": "+44(1380)-######", "cc": "UK", "cd": "United Kingdom", "city": "Devizes" },
    { "mask": "+44(1381)-######", "cc": "UK", "cd": "United Kingdom", "city": "Fortrose" },
    { "mask": "+44(1382)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dundee" },
    { "mask": "+44(1383)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dunfermline" },
    { "mask": "+44(1384)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dudley" },
    { "mask": "+44(1386)-######", "cc": "UK", "cd": "United Kingdom", "city": "Evesham" },
    { "mask": "+44(1387)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dumfries" },
    { "mask": "+44(13873)-####[#]", "cc": "UK", "cd": "United Kingdom", "city": "Langholm" },
    { "mask": "+44(1388)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bishop Auckland(3,4,6,7,8,9), Stanhope(2,5)" },
    { "mask": "+44(1389)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dumbarton" },
    { "mask": "+44(1392)-######", "cc": "UK", "cd": "United Kingdom", "city": "Exeter" },
    { "mask": "+44(1394)-######", "cc": "UK", "cd": "United Kingdom", "city": "Felixstowe" },
    { "mask": "+44(1395)-######", "cc": "UK", "cd": "United Kingdom", "city": "Budleigh Salterton" },
    { "mask": "+44(1397)-######", "cc": "UK", "cd": "United Kingdom", "city": "Fort William" },
    { "mask": "+44(1398)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dulverton" },
    { "mask": "+44(1400)-######", "cc": "UK", "cd": "United Kingdom", "city": "Honington" },
    { "mask": "+44(1403)-######", "cc": "UK", "cd": "United Kingdom", "city": "Horsham" },
    { "mask": "+44(1404)-######", "cc": "UK", "cd": "United Kingdom", "city": "Honiton" },
    { "mask": "+44(1405)-######", "cc": "UK", "cd": "United Kingdom", "city": "Goole" },
    { "mask": "+44(1406)-######", "cc": "UK", "cd": "United Kingdom", "city": "Holbeach" },
    { "mask": "+44(1407)-######", "cc": "UK", "cd": "United Kingdom", "city": "Holyhead" },
    { "mask": "+44(1408)-######", "cc": "UK", "cd": "United Kingdom", "city": "Golspie" },
    { "mask": "+44(1409)-######", "cc": "UK", "cd": "United Kingdom", "city": "Holsworthy" },
    { "mask": "+44(141)-###-###", "cc": "UK", "cd": "United Kingdom", "city": "Glasgow" },
    { "mask": "+44(1420)-######", "cc": "UK", "cd": "United Kingdom", "city": "Alton" },
    { "mask": "+44(1422)-######", "cc": "UK", "cd": "United Kingdom", "city": "Halifax" },
    { "mask": "+44(1423)-######", "cc": "UK", "cd": "United Kingdom", "city": "Boroughbridge(3,4,9), Harrogate(2,5,6,7,8)" },
    { "mask": "+44(1424)-######", "cc": "UK", "cd": "United Kingdom", "city": "Hastings" },
    { "mask": "+44(1425)-######", "cc": "UK", "cd": "United Kingdom", "city": "Ringwood" },
    { "mask": "+44(1427)-######", "cc": "UK", "cd": "United Kingdom", "city": "Gainsborough" },
    { "mask": "+44(1428)-######", "cc": "UK", "cd": "United Kingdom", "city": "Haslemere" },
    { "mask": "+44(1429)-######", "cc": "UK", "cd": "United Kingdom", "city": "Hartlepool" },
    { "mask": "+44(1430)-######", "cc": "UK", "cd": "United Kingdom", "city": "Market Weighton(6,7,8,9), North Cave(2,3,4,5)" },
    { "mask": "+44(1431)-######", "cc": "UK", "cd": "United Kingdom", "city": "Helmsdale" },
    { "mask": "+44(1432)-######", "cc": "UK", "cd": "United Kingdom", "city": "Hereford" },
    { "mask": "+44(1433)-######", "cc": "UK", "cd": "United Kingdom", "city": "Hathersage" },
    { "mask": "+44(1434)-######", "cc": "UK", "cd": "United Kingdom", "city": "Bellingham(2,4,9), Haltwhistle(3,5), Hexham(6,7,8)" },
    { "mask": "+44(1435)-######", "cc": "UK", "cd": "United Kingdom", "city": "Heathfield" },
    { "mask": "+44(1436)-######", "cc": "UK", "cd": "United Kingdom", "city": "Helensburgh" },
    { "mask": "+44(1437)-######", "cc": "UK", "cd": "United Kingdom", "city": "Clynderwen(2,3,4,5), Haverfordwest(6,7,8,9)" },
    { "mask": "+44(1438)-######", "cc": "UK", "cd": "United Kingdom", "city": "Stevenage" },
    { "mask": "+44(1439)-######", "cc": "UK", "cd": "United Kingdom", "city": "Helmsley" },
    { "mask": "+44(1440)-######", "cc": "UK", "cd": "United Kingdom", "city": "Haverhill" },
    { "mask": "+44(1442)-######", "cc": "UK", "cd": "United Kingdom", "city": "Hemel Hempstead" },
    { "mask": "+44(1443)-######", "cc": "UK", "cd": "United Kingdom", "city": "Pontypridd" },
    { "mask": "+44(1444)-######", "cc": "UK", "cd": "United Kingdom", "city": "Haywards Heath" },
    { "mask": "+44(1445)-######", "cc": "UK", "cd": "United Kingdom", "city": "Gairloch" },
    { "mask": "+44(1446)-######", "cc": "UK", "cd": "United Kingdom", "city": "Barry" },
    { "mask": "+44(1449)-######", "cc": "UK", "cd": "United Kingdom", "city": "Stowmarket" },
    { "mask": "+44(1450)-######", "cc": "UK", "cd": "United Kingdom", "city": "Hawick" },
    { "mask": "+44(1451)-######", "cc": "UK", "cd": "United Kingdom", "city": "Stow-on-the-Wold" },
    { "mask": "+44(1452)-######", "cc": "UK", "cd": "United Kingdom", "city": "Gloucester" },
    { "mask": "+44(1453)-######", "cc": "UK", "cd": "United Kingdom", "city": "Dursley" },
    { "mask": "+44(1454)-######", "cc": "UK", "cd": "United Kingdom", "city": "Chipping Sodbury" },
    { "mask": "+44(1455)-######", "cc": "UK", "cd": "United Kingdom", "city": "Hinckley" },
    { "mask": "+44(1456)-######", "cc": "UK", "cd": "United Kingdom", "city": "Glenurquhart" }
//
//     //http://en.wikipedia.org/wiki/Telephone_numbers_in_the_United_Kingdom
// 01457	Glossop
// 01458	Glastonbury
// 01460	Chard
// 01461	Gretna
// 01462	Hitchin
// 01463	Inverness
// 01464	Insch
// 01465	Girvan
// 01466	Huntly
// 01467	Inverurie
// 01469	Killingholme
// 01470	Isle of Skye - Edinbane
// 01471	Isle of Skye - Broadford
// 01472	Grimsby
// 01473	Ipswich
// 01474	Gravesend
// 01475	Greenock
// 01476	Grantham
// 01477	Holmes Chapel
// 01478	Isle of Skye - Portree
// 01479	Grantown-on-Spey
// 01480	Huntingdon
// 01481	Guernsey
// 01482	Hull
// 01483	Guildford
// 01484	Huddersfield
// 01485	Hunstanton
// 01487	Warboys
// 01488	Hungerford
// 01489	Bishops Waltham
// 01490	Corwen
// 01491	Henley-on-Thames
// 01492	Colwyn Bay
// 01493	Great Yarmouth
// 01494	High Wycombe
// 01495	Pontypool
// 01496	Port Ellen
// 01497	Hay-on-Wye
// 01499	Inveraray
// 01501	Harthill
// 01502	Lowestoft
// 01503	Looe
// 01505	Johnstone
// 01506	Bathgate
// 01507	Alford (Lincs)(4,8,9), Louth (3,6,7), Spilsby(2,5)
// 01508	Brooke
// 01509	Loughborough
// 0151	Liverpool
// 01520	Lochcarron
// 01522	Lincoln
// 01524	Lancaster
// 015242	Hornby
// 01525	Leighton Buzzard
// 01526	Martin
// 01527	Redditch
// 01528	Laggan
// 01529	Sleaford
// 01530	Coalville
// 01531	Ledbury
// 01534	Jersey
// 01535	Keighley
// 01536	Kettering
// 01538	Ipstones
// 01539	Kendal
// 015394	Hawkshead
// 015395	Grange-over-Sands
// 015396	Sedbergh
// 01540	Kingussie
// 01542	Keith
// 01543	Cannock
// 01544	Kington
// 01545	Llanarth
// 01546	Lochgilphead
// 01547	Knighton
// 01548	Kingsbridge
// 01549	Lairg
// 01550	Llandovery
// 01553	Kings Lynn
// 01554	Llanelli
// 01555	Lanark
// 01556	Castle Douglas
// 01557	Kirkcudbright
// 01558	Llandeilo
// 01559	Llandysul
// 01560	Moscow
// 01561	Laurencekirk
// 01562	Kidderminster
// 01563	Kilmarnock
// 01564	Lapworth
// 01565	Knutsford
// 01566	Launceston
// 01567	Killin
// 01568	Leominster
// 01569	Stonehaven
// 01570	Lampeter
// 01571	Lochinver
// 01572	Oakham
// 01573	Kelso
// 01575	Kirriemuir
// 01576	Lockerbie
// 01577	Kinross
// 01578	Lauder
// 01579	Liskeard
// 01580	Cranbrook
// 01581	New Luce
// 01582	Luton
// 01583	Carradale
// 01584	Ludlow
// 01586	Campbeltown
// 01588	Bishops Castle
// 01590	Lymington
// 01591	Llanwrtyd Wells
// 01592	Kirkcaldy
// 01593	Lybster
// 01594	Lydney
// 01595	Lerwick, Foula(75), Fair Isle(76)
// 01597	Llandrindod Wells
// 01598	Lynton
// 01599	Kyle
// 01600	Monmouth
// 01603	Norwich
// 01604	Northampton
// 01606	Northwich
// 01608	Chipping Norton
// 01609	Northallerton
// 0161	Manchester
// 01620	North Berwick
// 01621	Maldon
// 01622	Maidstone
// 01623	Mansfield
// 01624	Isle of Man
// 01625	Macclesfield
// 01626	Newton Abbot
// 01628	Maidenhead
// 01629	Matlock
// 01630	Market Drayton
// 01631	Oban
// 01633	Newport
// 01634	Medway
// 01635	Newbury
// 01636	Newark
// 01637	Newquay
// 01638	Newmarket
// 01639	Neath
// 01641	Strathy
// 01642	Middlesbrough
// 01643	Minehead
// 01644	New Galloway
// 01646	Milford Haven
// 01647	Moretonhampstead
// 01650	Cemmaes Road
// 01651	Oldmeldrum
// 01652	Brigg
// 01653	Malton
// 01654	Machynlleth
// 01655	Maybole
// 01656	Bridgend
// 01659	Sanquhar
// 01661	Prudhoe
// 01663	New Mills
// 01664	Melton Mowbray
// 01665	Alnwick
// 01666	Malmesbury
// 01667	Nairn
// 01668	Bamburgh
// 01669	Rothbury
// 01670	Morpeth
// 01671	Newton Stewart
// 01672	Marlborough
// 01673	Market Rasen
// 01674	Montrose
// 01675	Coleshill
// 01676	Meriden
// 01677	Bedale
// 01678	Bala
// 01680	Isle of Mull - Craignure
// 01681	Isle of Mull - Fionnphort
// 01683	Moffat
// 01684	Malvern
// 01685	Merthyr Tydfil
// 01686	Llanidloes(2,3,4,7), Newtown(5,6,8,9)
// 01687	Mallaig
// 01688	Isle of Mull - Tobermory
// 01689	Orpington
// 01690	Betws-y-Coed
// 01691	Oswestry
// 01692	North Walsham
// 01694	Church Stretton
// 01695	Skelmersdale
// 01697	Brampton
// 016973	Wigton
// 016974	Raughton Head
// 01698	Motherwell
// 01700	Rothesay
// 01702	Southend-on-Sea
// 01704	Southport
// 01706	Rochdale
// 01707	Welwyn Garden City
// 01708	Romford
// 01709	Rotherham
// 01720	Isles of Scilly
// 01721	Peebles
// 01722	Salisbury
// 01723	Scarborough
// 01724	Scunthorpe
// 01725	Rockbourne
// 01726	St Austell
// 01727	St Albans
// 01728	Saxmundham
// 01729	Settle
// 01730	Petersfield
// 01732	Sevenoaks
// 01733	Peterborough
// 01736	Penzance
// 01737	Redhill
// 01738	Perth
// 01740	Sedgefield
// 01743	Shrewsbury
// 01744	St Helens
// 01745	Rhyl
// 01746	Bridgnorth
// 01747	Shaftesbury
// 01748	Richmond
// 01749	Shepton Mallet
// 01750	Selkirk
// 01751	Pickering
// 01752	Plymouth
// 01753	Slough
// 01754	Skegness
// 01756	Skipton
// 01757	Selby
// 01758	Pwllheli
// 01759	Pocklington
// 01760	Swaffham
// 01761	Temple Cloud
// 01763	Royston
// 01764	Crieff
// 01765	Ripon
// 01766	Porthmadog
// 01767	Sandy
// 01768	Penrith
// 017683	Appleby
// 017684	Pooley Bridge
// 017687	Keswick
// 01769	South Molton
// 01770	Isle of Arran
// 01771	Maud
// 01772	Preston
// 01773	Ripley
// 01775	Spalding
// 01776	Stranraer
// 01777	Retford
// 01778	Bourne
// 01779	Peterhead
// 01780	Stamford
// 01782	Stoke-on-Trent
// 01784	Staines
// 01785	Stafford
// 01786	Stirling
// 01787	Sudbury
// 01788	Rugby
// 01789	Stratford-upon-Avon
// 01790	Spilsby
// 01792	Swansea
// 01793	Swindon
// 01794	Romsey
// 01795	Sittingbourne
// 01796	Pitlochry
// 01797	Rye
// 01798	Pulborough
// 01799	Saffron Walden
// 01803	Torquay
// 01805	Torrington
// 01806	Shetland
// 01807	Ballindalloch
// 01808	Tomatin
// 01809	Tomdoun
// 01821	Kinrossie
// 01822	Tavistock
// 01823	Taunton
// 01824	Ruthin
// 01825	Uckfield
// 01827	Tamworth
// 01828	Coupar Angus
// 01829	Tarporley
// 01830	Kirkwhelpington
// 01832	Clopton
// 01833	Barnard Castle
// 01834	Narberth
// 01835	St Boswells
// 01837	Okehampton
// 01838	Dalmally
// 01840	Camelford
// 01841	Newquay
// 01842	Thetford
// 01843	Thanet
// 01844	Thame
// 01845	Thirsk
// 01847	Thurso(2,3,4,5,8), Tongue(6,7,9)
// 01848	Thornhill
// 01851	Great Bernera(4,6,9), Stornoway(2,3,5,7,8)
// 01852	Kilmelford
// 01854	Ullapool
// 01855	Ballachulish
// 01856	Orkney
// 01857	Sanday
// 01858	Market Harborough
// 01859	Harris
// 01862	Tain
// 01863	Ardgay
// 01864	Abington
// 01865	Oxford
// 01866	Kilchrenan
// 01869	Bicester
// 01870	Isle of Benbecula
// 01871	Castlebay
// 01872	Truro
// 01873	Abergavenny
// 01874	Brecon
// 01875	Tranent
// 01876	Lochmaddy
// 01877	Callander
// 01878	Lochboisdale
// 01879	Scarinish
// 01880	Tarbert
// 01882	Kinloch Rannoch
// 01883	Caterham
// 01884	Tiverton
// 01885	Pencombe
// 01886	Bromyard
// 01887	Aberfeldy
// 01888	Turriff
// 01889	Rugeley
// 01890	Ayton(5,6,7,9), Coldstream(2,3,4,8)
// 01892	Tunbridge Wells
// 01895	Uxbridge
// 01896	Galashiels
// 01899	Biggar
// 01900	Workington
// 01902	Wolverhampton
// 01903	Worthing
// 01904	York
// 01905	Worcester
// 01908	Milton Keynes
// 01909	Worksop
// 0191	Tyneside(2,4,6), Durham(3), Sunderland(5)
// 01920	Ware
// 01922	Walsall
// 01923	Watford
// 01924	Wakefield
// 01925	Warrington
// 01926	Warwick
// 01928	Runcorn
// 01929	Wareham
// 01931	Shap
// 01932	Weybridge
// 01933	Wellingborough
// 01934	Weston-super-Mare
// 01935	Yeovil
// 01937	Wetherby
// 01938	Welshpool
// 01939	Wem
// 01942	Wigan
// 01943	Guiseley
// 01944	West Heslerton
// 01945	Wisbech
// 01946	Whitehaven
// 019467	Gosforth
// 01947	Whitby
// 01948	Whitchurch
// 01949	Whatton
// 01950	Sandwick
// 01951	Colonsay
// 01952	Telford
// 01953	Wymondham
// 01954	Madingley
// 01955	Wick
// 01957	Mid Yell
// 01959	Westerham
// 01962	Winchester
// 01963	Wincanton
// 01964	Hornsea(2,5,8,9), Patrington(3,4,6,7)
// 01967	Strontian
// 01968	Penicuik
// 01969	Leyburn
// 01970	Aberystwyth
// 01971	Scourie
// 01972	Glenborrodale
// 01974	Llanon
// 01975	Alford (Aberdeen)(2,4,5,9), Strathdon(3,6,7,8)
// 01977	Pontefract
// 01978	Wrexham
// 01980	Amesbury
// 01981	Wormbridge
// 01982	Builth Wells
// 01983	Isle of Wight
// 01984	Watchet
// 01985	Warminster
// 01986	Bungay
// 01987	Ebbsfleet
// 01988	Wigtown
// 01989	Ross-on-Wye
// 01992	Lea Valley
// 01993	Witney
// 01994	St Clears
// 01995	Garstang
// 01997	Strathpeffer
// 020	London
// 023	Southampton(8X), Portsmouth(9X)
// 024	Coventry
// 028	Ballycastle(20), Martinstown(21), Ballymena(25), Ballymoney(27), Larne(28), Kilrea(29), Newry(30), Armagh(37), Portadown(38), Banbridge(40), Rostrevor(41), Kircubbin(42), Newcastle(Co. Down)(43), Downpatrick(44), Enniskillen(66), Lisnaskea(67), Kesh(68), Coleraine(70), Londonderry(71), Limavady(77), Magherafelt(79), Carrickmore(80), Newtownstewart(81), Omagh(82), Ballygawley(85), Cookstown(86), Dungannon(87), Fivemiletown(89), Belfast(90&95), Bangor(Co. Down)(91), Lisburn(92), Ballyclare(93), Antrim(94), Saintfield(97)
// 029	Cardiff
//
//

]
