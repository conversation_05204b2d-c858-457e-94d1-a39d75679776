{"name": "jquery.inputmask", "version": "3.3.1", "main": ["./dist/inputmask/inputmask.js"], "keywords": ["j<PERSON>y", "plugins", "input", "form", "inputmask", "mask"], "description": "jquery.inputmask is a jquery plugin which create an input mask.", "license": "http://opensource.org/licenses/mit-license.php", "ignore": ["**/*", "!dist/*", "!dist/inputmask/*", "!dist/min/*", "!dist/min/inputmask/*", "!extra/bindings/*", "!extra/dependencyLibs/*", "!extra/phone-codes/*"], "dependencies": {"jquery": ">=1.7"}, "authors": [{"name": "<PERSON>"}], "homepage": "http://robinherbots.github.io/jquery.inputmask", "_release": "3.3.1", "_resolution": {"type": "version", "tag": "3.3.1", "commit": "b24113a01b0ade15ba2bf9823f03e993e3b659c1"}, "_source": "https://github.com/RobinHerbots/jquery.inputmask.git", "_target": "^3.3.1", "_originalSource": "jquery.inputmask"}