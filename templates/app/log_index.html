<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志管理 - XCLabel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .log-content {
            background-color: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .log-line {
            padding: 2px 5px;
            border-bottom: 1px solid #333;
        }
        .log-line:hover {
            background-color: #2d2d30;
        }
        .log-error {
            color: #f48771;
        }
        .log-warning {
            color: #dcdcaa;
        }
        .log-info {
            color: #9cdcfe;
        }
        .file-size {
            font-size: 0.8em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-12">
                <h2>日志管理</h2>
                
                <!-- 日志配置信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>日志配置</h5>
                    </div>
                    <div class="card-body">
                        <div id="logConfig" class="row">
                            <!-- 配置信息将通过JavaScript加载 -->
                        </div>
                    </div>
                </div>
                
                <!-- 日志文件列表 -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>日志文件列表</h5>
                        <div>
                            <button class="btn btn-primary btn-sm" onclick="refreshLogList()">刷新</button>
                            <button class="btn btn-warning btn-sm" onclick="showCompressDialog()">压缩旧日志</button>
                            <button class="btn btn-danger btn-sm" onclick="showCleanDialog()">清理过期日志</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="logStats" class="mb-3">
                            <!-- 统计信息 -->
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>大小</th>
                                        <th>年龄(天)</th>
                                        <th>修改时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="logFileList">
                                    <!-- 文件列表将通过JavaScript加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 日志内容查看 -->
                <div class="card" id="logContentCard" style="display: none;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 id="logFileName">日志内容</h5>
                        <div>
                            <input type="text" id="searchInput" class="form-control d-inline-block" style="width: 200px;" placeholder="搜索...">
                            <button class="btn btn-secondary btn-sm" onclick="searchLog()">搜索</button>
                            <button class="btn btn-secondary btn-sm" onclick="hideLogContent()">关闭</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div id="logPagination">
                                <!-- 分页控件 -->
                            </div>
                            <div>
                                <span id="logInfo"></span>
                            </div>
                        </div>
                        <div id="logContent" class="log-content">
                            <!-- 日志内容 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 清理日志对话框 -->
    <div class="modal fade" id="cleanModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">清理过期日志</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="maxAgeDays" class="form-label">保留天数</label>
                        <input type="number" class="form-control" id="maxAgeDays" value="30" min="1">
                        <div class="form-text">超过此天数的日志文件将被删除</div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="dryRunClean" checked>
                        <label class="form-check-label" for="dryRunClean">
                            试运行（不实际删除文件）
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="cleanLogs()">执行清理</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 压缩日志对话框 -->
    <div class="modal fade" id="compressModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">压缩旧日志</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="daysThreshold" class="form-label">压缩阈值（天）</label>
                        <input type="number" class="form-control" id="daysThreshold" value="7" min="1">
                        <div class="form-text">超过此天数的日志文件将被压缩</div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="dryRunCompress" checked>
                        <label class="form-check-label" for="dryRunCompress">
                            试运行（不实际压缩文件）
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="compressLogs()">执行压缩</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentLogFile = '';
        let currentPage = 1;
        let currentSearch = '';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadLogConfig();
            refreshLogList();
        });

        // 加载日志配置
        function loadLogConfig() {
            fetch('/log/getLogConfig')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1000) {
                        const config = data.data;
                        document.getElementById('logConfig').innerHTML = `
                            <div class="col-md-3">
                                <strong>文件大小限制:</strong> ${config.max_file_size_mb} MB
                            </div>
                            <div class="col-md-3">
                                <strong>备份文件数:</strong> ${config.backup_count}
                            </div>
                            <div class="col-md-3">
                                <strong>日志级别:</strong> ${config.log_level}
                            </div>
                            <div class="col-md-3">
                                <strong>控制台输出:</strong> ${config.console_output ? '是' : '否'}
                            </div>
                        `;
                    }
                })
                .catch(error => console.error('Error loading log config:', error));
        }

        // 刷新日志文件列表
        function refreshLogList() {
            fetch('/log/getLogList')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1000) {
                        displayLogStats(data.data);
                        displayLogList(data.data.files);
                    } else {
                        alert('获取日志列表失败: ' + data.msg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取日志列表失败');
                });
        }

        // 显示日志统计信息
        function displayLogStats(stats) {
            document.getElementById('logStats').innerHTML = `
                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5>${stats.total_files}</h5>
                                <p>总文件数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5>${stats.total_size_mb} MB</h5>
                                <p>总大小</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5>活跃</h5>
                                <p>日志状态</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示日志文件列表
        function displayLogList(files) {
            const tbody = document.getElementById('logFileList');
            tbody.innerHTML = '';
            
            files.forEach(file => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${file.file}</td>
                    <td><span class="file-size">${file.size_mb} MB</span></td>
                    <td>${file.age_days}</td>
                    <td>${file.modified}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="viewLog('${file.file}')">查看</button>
                        <button class="btn btn-sm btn-secondary" onclick="downloadLog('${file.file}')">下载</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 查看日志文件
        function viewLog(filename) {
            currentLogFile = filename;
            currentPage = 1;
            currentSearch = '';
            document.getElementById('searchInput').value = '';
            loadLogContent();
        }

        // 加载日志内容
        function loadLogContent() {
            const url = `/log/getLogContent?filename=${encodeURIComponent(currentLogFile)}&page=${currentPage}&search=${encodeURIComponent(currentSearch)}`;
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1000) {
                        displayLogContent(data.data);
                    } else {
                        alert('获取日志内容失败: ' + data.msg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取日志内容失败');
                });
        }

        // 显示日志内容
        function displayLogContent(data) {
            document.getElementById('logFileName').textContent = `日志内容 - ${data.filename}`;
            document.getElementById('logInfo').textContent = `第 ${data.page} 页，共 ${data.total_pages} 页，总计 ${data.total_lines} 行`;
            
            const content = document.getElementById('logContent');
            content.innerHTML = '';
            
            data.lines.forEach((line, index) => {
                const div = document.createElement('div');
                div.className = 'log-line';
                
                // 根据日志级别添加样式
                if (line.includes('[ERROR]')) {
                    div.classList.add('log-error');
                } else if (line.includes('[WARNING]')) {
                    div.classList.add('log-warning');
                } else if (line.includes('[INFO]')) {
                    div.classList.add('log-info');
                }
                
                div.textContent = line;
                content.appendChild(div);
            });
            
            // 显示分页
            displayPagination(data);
            
            // 显示日志内容卡片
            document.getElementById('logContentCard').style.display = 'block';
        }

        // 显示分页控件
        function displayPagination(data) {
            const pagination = document.getElementById('logPagination');
            let html = '<nav><ul class="pagination pagination-sm">';
            
            // 上一页
            if (data.page > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${data.page - 1})">上一页</a></li>`;
            }
            
            // 页码
            const startPage = Math.max(1, data.page - 2);
            const endPage = Math.min(data.total_pages, data.page + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                const active = i === data.page ? 'active' : '';
                html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="changePage(${i})">${i}</a></li>`;
            }
            
            // 下一页
            if (data.page < data.total_pages) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${data.page + 1})">下一页</a></li>`;
            }
            
            html += '</ul></nav>';
            pagination.innerHTML = html;
        }

        // 切换页面
        function changePage(page) {
            currentPage = page;
            loadLogContent();
        }

        // 搜索日志
        function searchLog() {
            currentSearch = document.getElementById('searchInput').value;
            currentPage = 1;
            loadLogContent();
        }

        // 隐藏日志内容
        function hideLogContent() {
            document.getElementById('logContentCard').style.display = 'none';
        }

        // 下载日志文件
        function downloadLog(filename) {
            window.open(`/log/downloadLog?filename=${encodeURIComponent(filename)}`);
        }

        // 显示清理对话框
        function showCleanDialog() {
            new bootstrap.Modal(document.getElementById('cleanModal')).show();
        }

        // 显示压缩对话框
        function showCompressDialog() {
            new bootstrap.Modal(document.getElementById('compressModal')).show();
        }

        // 清理日志
        function cleanLogs() {
            const maxAgeDays = document.getElementById('maxAgeDays').value;
            const dryRun = document.getElementById('dryRunClean').checked;
            
            fetch('/log/cleanLogs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    max_age_days: parseInt(maxAgeDays),
                    dry_run: dryRun
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1000) {
                    const result = data.data;
                    const action = result.dry_run ? '将删除' : '已删除';
                    alert(`${action} ${result.deleted_count} 个文件，释放 ${result.total_size_freed_mb} MB 空间`);
                    
                    if (!result.dry_run) {
                        refreshLogList();
                    }
                } else {
                    alert('清理失败: ' + data.msg);
                }
                
                bootstrap.Modal.getInstance(document.getElementById('cleanModal')).hide();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('清理失败');
            });
        }

        // 压缩日志
        function compressLogs() {
            const daysThreshold = document.getElementById('daysThreshold').value;
            const dryRun = document.getElementById('dryRunCompress').checked;
            
            fetch('/log/compressLogs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    days_threshold: parseInt(daysThreshold),
                    dry_run: dryRun
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1000) {
                    const result = data.data;
                    const action = result.dry_run ? '将压缩' : '已压缩';
                    alert(`${action} ${result.compressed_count} 个文件，节省 ${result.total_size_saved_mb} MB 空间`);
                    
                    if (!result.dry_run) {
                        refreshLogList();
                    }
                } else {
                    alert('压缩失败: ' + data.msg);
                }
                
                bootstrap.Modal.getInstance(document.getElementById('compressModal')).hide();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('压缩失败');
            });
        }

        // 搜索框回车事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchLog();
            }
        });
    </script>
</body>
</html>
