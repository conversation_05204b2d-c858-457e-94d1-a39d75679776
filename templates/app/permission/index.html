{% extends "app/base_site.html" %}

{% block title %}权限管理{% endblock %}

{% block content %}
<div class="right_col" role="main">
    <div class="">
        <div class="page-title">
            <div class="title_left">
                <h3>权限管理</h3>
            </div>
        </div>

        <div class="clearfix"></div>

        <div class="row">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel">
            <div class="x_title">
                <h2>用户角色配置</h2>
                <div class="clearfix"></div>
            </div>
            <div class="x_content">
                <div class="row">
                    <div class="col-md-6">
                        <h4>角色说明</h4>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <strong>超级管理员：</strong>拥有所有权限，可以管理用户、任务、训练等
                            </li>
                            <li class="list-group-item">
                                <strong>管理员：</strong>可以管理任务、样本和训练，但不能管理用户
                            </li>
                            <li class="list-group-item">
                                <strong>标注员：</strong>只能使用标注工具进行标注，无法访问其他功能
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h4>用户角色分配</h4>
                        <form id="permission-form">
                            <div class="form-group">
                                <label for="username">选择用户：</label>
                                <select class="form-control" id="username" name="username">
                                    <option value="">请选择用户</option>
                                    {% for user in users %}
                                    <option value="{{ user.username }}">{{ user.username }} ({{ user.first_name }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="role">分配角色：</label>
                                <select class="form-control" id="role" name="role">
                                    <option value="">请选择角色</option>
                                    <option value="admin">超级管理员</option>
                                    <option value="manager">管理员</option>
                                    <option value="annotator">标注员</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">保存配置</button>
                        </form>
                    </div>
                </div>

                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-12">
                        <h4>当前用户角色</h4>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>角色</th>
                                    <th>权限</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="user-roles-table">
                                {% for user in users %}
                                <tr data-username="{{ user.username }}">
                                    <td>{{ user.username }}</td>
                                    <td id="role-{{ user.username }}">
                                        {% if user.is_superuser %}
                                        超级管理员
                                        {% else %}
                                        未分配
                                        {% endif %}
                                    </td>
                                    <td id="permissions-{{ user.username }}">
                                        {% if user.is_superuser %}
                                        所有权限
                                        {% else %}
                                        无
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if not user.is_superuser %}
                                        <button class="btn btn-sm btn-warning" onclick="editUserRole('{{ user.username }}')">编辑</button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 加载当前用户角色配置
    loadUserRoles();
    
    // 表单提交
    $('#permission-form').on('submit', function(e) {
        e.preventDefault();
        
        var username = $('#username').val();
        var role = $('#role').val();
        
        if (!username || !role) {
            alert('请选择用户和角色');
            return;
        }
        
        $.ajax({
            url: '/permission/update',
            type: 'POST',
            data: {
                'username': username,
                'role': role,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.code === 1000) {
                    alert('角色配置成功');
                    loadUserRoles();
                    $('#permission-form')[0].reset();
                } else {
                    alert('配置失败：' + response.msg);
                }
            },
            error: function() {
                alert('网络错误，请重试');
            }
        });
    });
});

function loadUserRoles() {
    // 这里可以通过AJAX加载当前的用户角色配置
    // 暂时使用静态数据
}

function editUserRole(username) {
    $('#username').val(username);
    // 可以预填充当前角色
}
</script>
{% endblock %}
