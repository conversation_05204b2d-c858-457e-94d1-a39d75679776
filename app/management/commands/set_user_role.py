from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from app.permissions import USER_ROLES, ROLES, ROLE_PERMISSIONS
from app.models import UserRole
import json

class Command(BaseCommand):
    help = '设置用户角色（支持数据库持久化）'

    def add_arguments(self, parser):
        parser.add_argument('username', type=str, nargs='?', help='用户名')
        parser.add_argument('role', type=str, nargs='?', choices=list(ROLES.keys()), help='角色')
        parser.add_argument('--list', action='store_true', help='列出所有用户和角色')
        parser.add_argument('--permissions', type=str, help='额外权限（JSON格式）')

    def handle(self, *args, **options):
        if options['list']:
            self.list_users_and_roles()
            return

        username = options['username']
        role = options['role']

        if not username or not role:
            self.stdout.write(self.style.ERROR('请提供用户名和角色'))
            self.stdout.write('使用方法: python manage.py set_user_role <username> <role>')
            self.stdout.write('或者使用 --list 查看所有用户')
            return

        # 检查用户是否存在
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'用户 "{username}" 不存在')
            )
            return

        # 处理额外权限
        extra_permissions = []
        if options['permissions']:
            try:
                extra_permissions = json.loads(options['permissions'])
            except json.JSONDecodeError:
                self.stdout.write(
                    self.style.ERROR('权限格式错误，请使用JSON格式，如：["permission1", "permission2"]')
                )
                return

        # 创建或更新用户角色
        user_role, created = UserRole.objects.get_or_create(
            user=user,
            defaults={
                'role': role,
                'permissions': json.dumps(extra_permissions),
                'is_active': True
            }
        )

        if not created:
            user_role.role = role
            user_role.permissions = json.dumps(extra_permissions)
            user_role.is_active = True
            user_role.save()

        action = "创建" if created else "更新"
        self.stdout.write(
            self.style.SUCCESS(
                f'成功{action}用户 "{username}" 的角色为 "{role}" ({ROLES[role]})'
            )
        )

        # 显示权限
        permissions = ROLE_PERMISSIONS.get(role, [])
        all_permissions = permissions + extra_permissions
        self.stdout.write(f'该用户拥有的权限: {", ".join(all_permissions)}')

    def list_users_and_roles(self):
        self.stdout.write(self.style.SUCCESS('=== 用户角色列表 ==='))

        # 显示所有用户
        users = User.objects.all()
        for user in users:
            # 从数据库获取角色
            try:
                user_role = UserRole.objects.get(user=user, is_active=True)
                role = user_role.role
                extra_perms = json.loads(user_role.permissions) if user_role.permissions else []
            except UserRole.DoesNotExist:
                # 兼容性：从代码配置获取
                role = USER_ROLES.get(user.username, '未分配')
                extra_perms = []

            role_desc = ROLES.get(role, '无角色') if role != '未分配' else '无角色'
            superuser_status = ' (超级管理员)' if user.is_superuser else ''

            permissions = ROLE_PERMISSIONS.get(role, []) + extra_perms
            perm_str = f" [{', '.join(permissions)}]" if permissions else ""

            self.stdout.write(f'{user.username}: {role} ({role_desc}){superuser_status}{perm_str}')

        self.stdout.write('\n=== 可用角色 ===')
        for role_key, role_name in ROLES.items():
            permissions = ROLE_PERMISSIONS.get(role_key, [])
            self.stdout.write(f'{role_key} ({role_name}): {", ".join(permissions)}')
