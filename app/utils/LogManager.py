#!/usr/bin/env python3
"""
日志管理工具
用于管理和清理日志文件
"""

import os
import glob
import time
from datetime import datetime, timedelta
from pathlib import Path


class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir, max_age_days=30, max_size_mb=20):
        """
        初始化日志管理器
        
        Args:
            log_dir: 日志目录路径
            max_age_days: 日志文件最大保留天数
            max_size_mb: 单个日志文件最大大小（MB）
        """
        self.log_dir = Path(log_dir)
        self.max_age_days = max_age_days
        self.max_size_bytes = max_size_mb * 1024 * 1024
        
    def get_log_files(self):
        """获取所有日志文件"""
        log_files = []
        patterns = ['*.log', '*.log.*']
        
        for pattern in patterns:
            log_files.extend(self.log_dir.glob(pattern))
        
        return sorted(log_files)
    
    def get_file_age_days(self, file_path):
        """获取文件年龄（天数）"""
        file_time = os.path.getmtime(file_path)
        current_time = time.time()
        age_seconds = current_time - file_time
        return age_seconds / (24 * 3600)
    
    def get_file_size_mb(self, file_path):
        """获取文件大小（MB）"""
        size_bytes = os.path.getsize(file_path)
        return size_bytes / (1024 * 1024)
    
    def clean_old_logs(self, dry_run=False):
        """
        清理过期的日志文件
        
        Args:
            dry_run: 是否为试运行（不实际删除文件）
        
        Returns:
            dict: 清理结果统计
        """
        log_files = self.get_log_files()
        deleted_files = []
        total_size_freed = 0
        
        for log_file in log_files:
            try:
                age_days = self.get_file_age_days(log_file)
                
                if age_days > self.max_age_days:
                    file_size = os.path.getsize(log_file)
                    
                    if not dry_run:
                        os.remove(log_file)
                    
                    deleted_files.append({
                        'file': str(log_file),
                        'age_days': round(age_days, 2),
                        'size_mb': round(file_size / (1024 * 1024), 2)
                    })
                    total_size_freed += file_size
                    
            except Exception as e:
                print(f"Error processing {log_file}: {e}")
        
        return {
            'deleted_count': len(deleted_files),
            'deleted_files': deleted_files,
            'total_size_freed_mb': round(total_size_freed / (1024 * 1024), 2),
            'dry_run': dry_run
        }
    
    def get_log_stats(self):
        """获取日志文件统计信息"""
        log_files = self.get_log_files()
        total_size = 0
        file_stats = []
        
        for log_file in log_files:
            try:
                size = os.path.getsize(log_file)
                age_days = self.get_file_age_days(log_file)
                
                file_stats.append({
                    'file': str(log_file.name),
                    'size_mb': round(size / (1024 * 1024), 2),
                    'age_days': round(age_days, 2),
                    'modified': datetime.fromtimestamp(os.path.getmtime(log_file)).strftime('%Y-%m-%d %H:%M:%S')
                })
                total_size += size
                
            except Exception as e:
                print(f"Error getting stats for {log_file}: {e}")
        
        # 按修改时间排序
        file_stats.sort(key=lambda x: x['modified'], reverse=True)
        
        return {
            'total_files': len(file_stats),
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'files': file_stats
        }
    
    def compress_old_logs(self, days_threshold=7, dry_run=False):
        """
        压缩旧的日志文件
        
        Args:
            days_threshold: 超过多少天的日志文件需要压缩
            dry_run: 是否为试运行
        
        Returns:
            dict: 压缩结果统计
        """
        import gzip
        import shutil
        
        log_files = self.get_log_files()
        compressed_files = []
        total_size_saved = 0
        
        for log_file in log_files:
            try:
                # 跳过已经压缩的文件
                if log_file.suffix == '.gz':
                    continue
                
                age_days = self.get_file_age_days(log_file)
                
                if age_days > days_threshold:
                    original_size = os.path.getsize(log_file)
                    compressed_path = log_file.with_suffix(log_file.suffix + '.gz')
                    
                    if not dry_run:
                        with open(log_file, 'rb') as f_in:
                            with gzip.open(compressed_path, 'wb') as f_out:
                                shutil.copyfileobj(f_in, f_out)
                        
                        # 删除原文件
                        os.remove(log_file)
                    
                    compressed_size = os.path.getsize(compressed_path) if not dry_run else original_size * 0.3  # 估算压缩率
                    size_saved = original_size - compressed_size
                    
                    compressed_files.append({
                        'original_file': str(log_file),
                        'compressed_file': str(compressed_path),
                        'original_size_mb': round(original_size / (1024 * 1024), 2),
                        'compressed_size_mb': round(compressed_size / (1024 * 1024), 2),
                        'size_saved_mb': round(size_saved / (1024 * 1024), 2),
                        'compression_ratio': round((1 - compressed_size / original_size) * 100, 1)
                    })
                    total_size_saved += size_saved
                    
            except Exception as e:
                print(f"Error compressing {log_file}: {e}")
        
        return {
            'compressed_count': len(compressed_files),
            'compressed_files': compressed_files,
            'total_size_saved_mb': round(total_size_saved / (1024 * 1024), 2),
            'dry_run': dry_run
        }


def main():
    """命令行工具主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='日志管理工具')
    parser.add_argument('--log-dir', default='./log', help='日志目录路径')
    parser.add_argument('--max-age', type=int, default=30, help='日志文件最大保留天数')
    parser.add_argument('--action', choices=['stats', 'clean', 'compress'], default='stats', help='执行的操作')
    parser.add_argument('--dry-run', action='store_true', help='试运行（不实际执行操作）')
    parser.add_argument('--compress-threshold', type=int, default=7, help='压缩阈值（天数）')
    
    args = parser.parse_args()
    
    log_manager = LogManager(args.log_dir, args.max_age)
    
    if args.action == 'stats':
        stats = log_manager.get_log_stats()
        print(f"日志文件统计:")
        print(f"  总文件数: {stats['total_files']}")
        print(f"  总大小: {stats['total_size_mb']} MB")
        print(f"\n文件详情:")
        for file_info in stats['files']:
            print(f"  {file_info['file']}: {file_info['size_mb']} MB, {file_info['age_days']} 天, {file_info['modified']}")
    
    elif args.action == 'clean':
        result = log_manager.clean_old_logs(dry_run=args.dry_run)
        action_text = "将删除" if result['dry_run'] else "已删除"
        print(f"{action_text} {result['deleted_count']} 个过期日志文件")
        print(f"释放空间: {result['total_size_freed_mb']} MB")
        
        if result['deleted_files']:
            print(f"\n{action_text}的文件:")
            for file_info in result['deleted_files']:
                print(f"  {file_info['file']}: {file_info['size_mb']} MB, {file_info['age_days']} 天")
    
    elif args.action == 'compress':
        result = log_manager.compress_old_logs(args.compress_threshold, dry_run=args.dry_run)
        action_text = "将压缩" if result['dry_run'] else "已压缩"
        print(f"{action_text} {result['compressed_count']} 个日志文件")
        print(f"节省空间: {result['total_size_saved_mb']} MB")
        
        if result['compressed_files']:
            print(f"\n{action_text}的文件:")
            for file_info in result['compressed_files']:
                print(f"  {file_info['original_file']} -> {file_info['compressed_file']}")
                print(f"    压缩率: {file_info['compression_ratio']}%, 节省: {file_info['size_saved_mb']} MB")


if __name__ == '__main__':
    main()
