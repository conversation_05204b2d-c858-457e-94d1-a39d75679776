import logging
import os
import uuid
import threading
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from datetime import datetime

# 线程本地存储，用于存储请求ID
_thread_local = threading.local()

def get_request_id():
    """获取当前请求的唯一ID"""
    if not hasattr(_thread_local, 'request_id'):
        _thread_local.request_id = str(uuid.uuid4())[:8]
    return _thread_local.request_id

def set_request_id(request_id=None):
    """设置当前请求的唯一ID"""
    if request_id is None:
        request_id = str(uuid.uuid4())[:8]
    _thread_local.request_id = request_id
    return request_id

def clear_request_id():
    """清除当前请求的ID"""
    if hasattr(_thread_local, 'request_id'):
        delattr(_thread_local, 'request_id')

class RequestFormatter(logging.Formatter):
    """自定义日志格式化器，包含请求ID"""
    def format(self, record):
        # 添加请求ID到日志记录
        record.request_id = get_request_id()
        return super().format(record)

def CreateLogger(filepath, is_show_console=False, max_bytes=20*1024*1024, backup_count=30):
    """
    创建日志记录器

    Args:
        filepath: 日志文件路径
        is_show_console: 是否在控制台显示日志
        max_bytes: 单个日志文件最大大小（字节），默认20MB
        backup_count: 保留的日志文件数量，默认30个
    """
    level = logging.INFO
    logger = logging.getLogger()

    # 清除现有的处理器，避免重复添加
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    logger.setLevel(level)

    # 自定义格式：包含请求ID、时间戳、行号、级别和消息
    formatter = RequestFormatter('[%(request_id)s] %(asctime)s %(lineno)s [%(levelname)s] %(message)s')

    # 使用RotatingFileHandler实现按大小轮转
    # maxBytes: 单个文件最大大小（20MB）
    # backupCount: 保留的备份文件数量（30个文件，约30天的日志）
    rotatingFileHandler = RotatingFileHandler(
        filepath,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )

    rotatingFileHandler.setLevel(level)
    rotatingFileHandler.setFormatter(formatter)
    logger.addHandler(rotatingFileHandler)

    # 控制台打印
    if is_show_console:
        streamHandler = logging.StreamHandler()
        streamHandler.setLevel(level)
        streamHandler.setFormatter(formatter)
        logger.addHandler(streamHandler)

    return logger

def log_request_start(logger, request):
    """记录请求开始"""
    request_id = set_request_id()
    method = request.method
    path = request.path
    user_agent = request.META.get('HTTP_USER_AGENT', 'Unknown')
    remote_addr = get_client_ip(request)

    logger.info(f"REQUEST_START - {method} {path} from {remote_addr} - User-Agent: {user_agent}")
    return request_id

def log_request_end(logger, request, response, start_time=None):
    """记录请求结束"""
    method = request.method
    path = request.path
    status_code = response.status_code

    duration_info = ""
    if start_time:
        duration = (datetime.now() - start_time).total_seconds() * 1000
        duration_info = f" - Duration: {duration:.2f}ms"

    logger.info(f"REQUEST_END - {method} {path} - Status: {status_code}{duration_info}")
    clear_request_id()

def get_client_ip(request):
    """获取客户端真实IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', 'Unknown')
    return ip

