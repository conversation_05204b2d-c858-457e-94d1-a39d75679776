import psutil
import shutil
import os
import json
import cv2
import zipfile
from datetime import datetime

class TrainUtils:
    def __init__(self,logger):
        self.logger = logger

    def getProcessInfoByName(self,processName):

        info = {
            "process_name": processName,  # 中文名
            "status": None,
            "pid": None,
            "create_date": None,
            "create_date_str": "00:00:00",
            "state": 0
        }

        for pid in psutil.pids():
            process = psutil.Process(pid)
            p_name_lower = process.name().lower()  # 进程实际名称，包含后缀
            if p_name_lower.endswith(".exe"):
                p_name_lower = p_name_lower[0:-4]

            if p_name_lower == processName:
                create_date = datetime.fromtimestamp(process.create_time())
                now_date = datetime.now()

                spend_seconds = (now_date - create_date).seconds # 检测到该进程时，该进程已经存货的时长（单位秒）
                if 0 <= spend_seconds <= 20:

                    info["status"] = process.status()
                    info["pid"] = pid
                    info["create_date"] = create_date
                    info["create_date_str"] = create_date.strftime("%Y-%m-%d %H:%M:%S")
                    # timeArray = time.localtime(int(process.create_time()))
                    # dateStr = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
                    # info["started"] = dateStr

                    info["state"] = 1


        return info
    def checkProcessByPid(self,pid):
        try:
            process = psutil.Process(pid)
            if process.status():
                return True
            else:
                return False
        except Exception as e:
            self.logger.error("checkProcessByPid %s: %s" % (str(e), str(e)))
            return False

    def stopProcessByPid(self,pid):
        try:
            process = psutil.Process(pid)
            if process.status():
                process.kill()
        except Exception as e:
            self.logger.error("stopProcessByPid %s: %s" % (str(e), str(e)))

    @staticmethod
    def detect2detect(src_detect_dir, dst_detect_dir, freq=5):
        src_detect_images_dir = os.path.join(src_detect_dir, "images")
        src_detect_labels_dir = os.path.join(src_detect_dir, "labels")

        dst_detect_images_dir = os.path.join(dst_detect_dir, "images")
        dst_detect_labels_dir = os.path.join(dst_detect_dir, "labels")

        if not os.path.exists(dst_detect_images_dir):
            os.makedirs(dst_detect_images_dir)
        if not os.path.exists(dst_detect_labels_dir):
            os.makedirs(dst_detect_labels_dir)

        train_count = 0
        valid_count = 0
        i = 0
        filenames = os.listdir(src_detect_images_dir)
        for filename in filenames:
            if i % freq == 0:
                if filename.endswith(".jpg"):
                    name = filename[0:-4]
                    src_image_path = os.path.join(src_detect_images_dir, name + ".jpg")
                    src_label_path = os.path.join(src_detect_labels_dir, name + ".txt")

                    dst_image_path = os.path.join(dst_detect_images_dir, name + ".jpg")
                    dst_label_path = os.path.join(dst_detect_labels_dir, name + ".txt")

                    try:
                        shutil.copyfile(src_image_path, dst_image_path)
                        shutil.copyfile(src_label_path, dst_label_path)

                        os.remove(src_image_path)
                        os.remove(src_label_path)
                        valid_count += 1
                    except Exception as e:
                        try:
                            os.remove(src_image_path)
                        except:
                            pass
                        try:
                            os.remove(src_label_path)
                        except:
                            pass
                        try:
                            os.remove(dst_image_path)
                        except:
                            pass
                        try:
                            os.remove(dst_label_path)
                        except:
                            pass
                else:
                    train_count += 1
            else:
                train_count += 1
            i += 1
        return train_count, valid_count

    @staticmethod
    def export_yolo_dataset(task, storage_dir, export_dir, train_ratio=0.8):
        """
        导出YOLO格式的数据集，按照标准目录结构

        Args:
            task: 任务对象
            storage_dir: 存储根目录
            export_dir: 导出目录
            train_ratio: 训练集比例，默认0.8 (80%训练，20%验证)

        Returns:
            tuple: (success, message, export_info)
        """
        try:
            # 获取标签信息
            names = []
            try:
                if task.labels:
                    labels_data = json.loads(task.labels)
                    if labels_data:
                        for label in labels_data:
                            names.append(label["labelName"])
            except Exception as e:
                # 如果没有标签信息，从标注数据中提取
                pass

            def get_label_index(label_name):
                """获取标签索引"""
                try:
                    return names.index(label_name)
                except ValueError:
                    # 如果标签不在列表中，添加到列表
                    if label_name == "未命名":
                        if "未命名" not in names:
                            names.append("未命名")
                        return names.index("未命名")
                    else:
                        if label_name not in names:
                            names.append(label_name)
                        return names.index(label_name)

            # 创建标准YOLO目录结构
            dataset_dir = os.path.join(export_dir, "dataset")

            # 图片目录
            train_images_dir = os.path.join(dataset_dir, "images", "train")
            val_images_dir = os.path.join(dataset_dir, "images", "val")

            # 标签目录
            train_labels_dir = os.path.join(dataset_dir, "labels", "train")
            val_labels_dir = os.path.join(dataset_dir, "labels", "val")

            # 创建所有必要的目录
            for dir_path in [train_images_dir, val_images_dir, train_labels_dir, val_labels_dir]:
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path)

            # 从数据库获取已标注的样本
            from app.views.ViewsBase import g_database, g_logger
            samples = g_database.select(
                "select * from xc_task_sample where task_code='%s' and annotation_state=1 order by id asc" % task.code
            )

            g_logger.info(f"导出任务 {task.code}: 找到 {len(samples)} 个已标注样本")

            if len(samples) == 0:
                # 尝试查询所有样本（包括未标注的）
                all_samples = g_database.select(
                    "select * from xc_task_sample where task_code='%s' order by id asc" % task.code
                )
                g_logger.info(f"任务 {task.code} 总共有 {len(all_samples)} 个样本")

                # 检查存储目录
                task_storage_dir = os.path.join(storage_dir, "task", task.code)
                g_logger.info(f"任务存储目录: {task_storage_dir}, 存在: {os.path.exists(task_storage_dir)}")

                return False, f"没有找到已标注的样本。任务总共有 {len(all_samples)} 个样本，请先完成标注。存储目录: {task_storage_dir}", None

            exported_count = 0
            skipped_count = 0
            train_count = 0
            val_count = 0

            # 计算训练集数量
            total_samples = len(samples)
            train_samples_count = int(total_samples * train_ratio)

            for i, sample in enumerate(samples):
                new_filename = sample["new_filename"]
                g_logger.info(f"处理样本: {new_filename}")

                if not new_filename.endswith(".jpg"):
                    g_logger.warning(f"跳过非JPG文件: {new_filename}")
                    skipped_count += 1
                    continue

                # 源图片路径
                src_image_path = os.path.join(storage_dir, "task", task.code, "sample", new_filename)
                g_logger.info(f"图片路径: {src_image_path}")

                if not os.path.exists(src_image_path):
                    g_logger.warning(f"图片文件不存在: {src_image_path}")
                    skipped_count += 1
                    continue

                # 决定是训练集还是验证集
                is_train = i < train_samples_count
                if is_train:
                    current_images_dir = train_images_dir
                    current_labels_dir = train_labels_dir
                    train_count += 1
                else:
                    current_images_dir = val_images_dir
                    current_labels_dir = val_labels_dir
                    val_count += 1

                # 读取图片获取尺寸
                try:
                    image = cv2.imread(src_image_path)
                    if image is None:
                        g_logger.warning(f"无法读取图片: {src_image_path}")
                        skipped_count += 1
                        continue

                    image_height, image_width, _ = image.shape
                    g_logger.info(f"图片尺寸: {image_width}x{image_height}")
                except Exception as e:
                    g_logger.error(f"读取图片失败: {src_image_path}, 错误: {str(e)}")
                    skipped_count += 1
                    continue

                # 解析标注内容
                try:
                    annotation_content = json.loads(sample["annotation_content"])
                    g_logger.info(f"标注内容: {len(annotation_content)} 个标注")
                except Exception as e:
                    g_logger.warning(f"解析标注内容失败: {str(e)}")
                    skipped_count += 1
                    continue

                # 生成YOLO格式标注
                yolo_lines = []
                for annotation in annotation_content:
                    content = annotation.get("content", [])
                    labels = annotation.get("labels", {})
                    content_type = annotation.get("contentType", "")
                    label_name = labels.get("labelName", "未命名")

                    if content_type == "rect" and len(content) == 4:
                        # 获取边界框坐标
                        x1 = float(content[0]["x"])
                        y1 = float(content[0]["y"])
                        x2 = float(content[2]["x"])
                        y2 = float(content[2]["y"])

                        # 确保坐标顺序正确
                        x_min = min(x1, x2)
                        x_max = max(x1, x2)
                        y_min = min(y1, y2)
                        y_max = max(y1, y2)

                        # 检查边界框是否在图像范围内
                        if (x_min >= 0 and y_min >= 0 and
                            x_max <= image_width and y_max <= image_height and
                            x_max > x_min and y_max > y_min):

                            # 转换为YOLO格式 (相对坐标)
                            x_center = (x_min + x_max) / 2.0 / image_width
                            y_center = (y_min + y_max) / 2.0 / image_height
                            width = (x_max - x_min) / image_width
                            height = (y_max - y_min) / image_height

                            label_index = get_label_index(label_name)
                            yolo_line = "%d %.6f %.6f %.6f %.6f\n" % (
                                label_index, x_center, y_center, width, height
                            )
                            yolo_lines.append(yolo_line)

                # 复制图片到对应目录（训练集或验证集）
                dst_image_path = os.path.join(current_images_dir, new_filename)
                try:
                    shutil.copy(src_image_path, dst_image_path)
                    g_logger.info(f"复制图片成功: {dst_image_path}")
                except Exception as e:
                    g_logger.error(f"复制图片失败: {str(e)}")
                    skipped_count += 1
                    continue

                # 写入YOLO格式标注文件到对应目录
                filename_prefix = new_filename[:-4]  # 去掉.jpg后缀
                dst_label_path = os.path.join(current_labels_dir, filename_prefix + ".txt")
                try:
                    with open(dst_label_path, "w") as f:
                        for line in yolo_lines:
                            f.write(line)
                    g_logger.info(f"写入标注文件成功: {dst_label_path}, {len(yolo_lines)} 个标注")
                except Exception as e:
                    g_logger.error(f"写入标注文件失败: {str(e)}")
                    skipped_count += 1
                    continue

                exported_count += 1

            # 生成YAML配置文件，使用任务名称作为文件名
            task_name_safe = "".join(c for c in task.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            task_name_safe = task_name_safe.replace(' ', '_').lower()
            yaml_filename = f"{task_name_safe}.yaml"
            yaml_path = os.path.join(export_dir, yaml_filename)

            with open(yaml_path, "w", encoding='utf-8') as f:
                f.write("# YOLO Dataset Configuration\n")
                f.write(f"# Task: {task.name} ({task.code})\n")
                f.write(f"# Generated from XCLabel export\n\n")
                f.write("path: ./dataset\n")
                f.write("train: images/train\n")
                f.write("val: images/val\n")
                f.write("nc: %d\n" % len(names))
                f.write("names: [%s]\n" % ",".join(map(lambda x: "'" + str(x) + "'", names)))

            export_info = {
                "exported_count": exported_count,
                "skipped_count": skipped_count,
                "total_samples": len(samples),
                "train_count": train_count,
                "val_count": val_count,
                "labels": names,
                "label_count": len(names),
                "yaml_filename": yaml_filename
            }

            return True, "导出成功", export_info

        except Exception as e:
            return False, f"导出失败: {str(e)}", None