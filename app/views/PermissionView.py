from app.permissions import require_superuser, USER_PERMISSIONS, PERMISSIONS, USER_ROLES, ROLES, ROLE_PERMISSIONS
from app.views.ViewsBase import *
from django.contrib.auth.models import User

@require_superuser
def index(request):
    """权限管理页面 - 重定向到用户管理"""
    from django.shortcuts import redirect
    return redirect('/user/index')

@require_superuser
def api_update_permission(request):
    """更新用户角色API - 重定向到用户管理API"""
    from app.views.UserView import api_update_role
    return api_update_role(request)