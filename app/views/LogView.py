"""
日志管理视图
提供日志查看、搜索、下载等功能
"""

import os
import json
import gzip
from datetime import datetime, timedelta
from django.http import HttpResponse, Http404
from django.shortcuts import render
from django.core.paginator import Paginator
from app.views.ViewsBase import *
from app.utils.LogManager import LogManager
from app.permissions import require_permission


@require_permission('SYSTEM_MANAGE')
def index(request):
    """日志管理主页"""
    context = {
        'user': readUser(request)
    }
    return render(request, 'app/log_index.html', context)


@require_permission('SYSTEM_MANAGE')
def api_getLogList(request):
    """获取日志文件列表"""
    ret = False
    msg = "未知错误"
    data = {}
    
    try:
        log_dir = os.path.join(BASE_DIR, "log")
        log_manager = LogManager(log_dir)
        
        # 获取日志文件统计信息
        stats = log_manager.get_log_stats()
        
        ret = True
        msg = "success"
        data = stats
        
    except Exception as e:
        msg = str(e)
        g_logger.error(f"LOG_LIST_ERROR - {msg}")
    
    res = {
        "code": 1000 if ret else 0,
        "msg": msg,
        "data": data
    }
    return HttpResponseJson(res)


@require_permission('SYSTEM_MANAGE')
def api_getLogContent(request):
    """获取日志文件内容"""
    ret = False
    msg = "未知错误"
    data = {}
    
    if request.method == 'GET':
        params = parse_get_params(request)
        filename = params.get("filename", "").strip()
        page = int(params.get("page", 1))
        page_size = int(params.get("page_size", 100))
        search_term = params.get("search", "").strip()
        
        try:
            log_dir = os.path.join(BASE_DIR, "log")
            file_path = os.path.join(log_dir, filename)
            
            # 安全检查：确保文件在日志目录内
            if not os.path.abspath(file_path).startswith(os.path.abspath(log_dir)):
                raise Exception("Invalid file path")
            
            if not os.path.exists(file_path):
                raise Exception("Log file not found")
            
            # 读取文件内容
            lines = []
            if filename.endswith('.gz'):
                with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                    lines = f.readlines()
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            
            # 搜索过滤
            if search_term:
                lines = [line for line in lines if search_term.lower() in line.lower()]
            
            # 分页
            paginator = Paginator(lines, page_size)
            page_obj = paginator.get_page(page)
            
            data = {
                "filename": filename,
                "total_lines": len(lines),
                "page": page,
                "page_size": page_size,
                "total_pages": paginator.num_pages,
                "lines": [line.rstrip() for line in page_obj.object_list],
                "search_term": search_term
            }
            
            ret = True
            msg = "success"
            
        except Exception as e:
            msg = str(e)
            g_logger.error(f"LOG_CONTENT_ERROR - {msg}")
    else:
        msg = "request method not supported"
    
    res = {
        "code": 1000 if ret else 0,
        "msg": msg,
        "data": data
    }
    return HttpResponseJson(res)


@require_permission('SYSTEM_MANAGE')
def api_downloadLog(request):
    """下载日志文件"""
    if request.method == 'GET':
        params = parse_get_params(request)
        filename = params.get("filename", "").strip()
        
        try:
            log_dir = os.path.join(BASE_DIR, "log")
            file_path = os.path.join(log_dir, filename)
            
            # 安全检查
            if not os.path.abspath(file_path).startswith(os.path.abspath(log_dir)):
                raise Http404("Invalid file path")
            
            if not os.path.exists(file_path):
                raise Http404("Log file not found")
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # 设置响应头
            response = HttpResponse(content, content_type='application/octet-stream')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            g_logger.info(f"LOG_DOWNLOAD - File: {filename}")
            return response
            
        except Exception as e:
            g_logger.error(f"LOG_DOWNLOAD_ERROR - {str(e)}")
            raise Http404("File not found")
    
    raise Http404("Invalid request")


@require_permission('SYSTEM_MANAGE')
def api_cleanLogs(request):
    """清理过期日志"""
    ret = False
    msg = "未知错误"
    data = {}
    
    if request.method == 'POST':
        params = parse_post_params(request)
        max_age_days = int(params.get("max_age_days", 30))
        dry_run = params.get("dry_run", False)
        
        try:
            log_dir = os.path.join(BASE_DIR, "log")
            log_manager = LogManager(log_dir, max_age_days)
            
            # 执行清理
            result = log_manager.clean_old_logs(dry_run=dry_run)
            
            ret = True
            msg = "success"
            data = result
            
            if not dry_run and result['deleted_count'] > 0:
                g_logger.info(f"LOG_CLEANUP - Deleted {result['deleted_count']} files, freed {result['total_size_freed_mb']} MB")
            
        except Exception as e:
            msg = str(e)
            g_logger.error(f"LOG_CLEANUP_ERROR - {msg}")
    else:
        msg = "request method not supported"
    
    res = {
        "code": 1000 if ret else 0,
        "msg": msg,
        "data": data
    }
    return HttpResponseJson(res)


@require_permission('SYSTEM_MANAGE')
def api_compressLogs(request):
    """压缩旧日志"""
    ret = False
    msg = "未知错误"
    data = {}
    
    if request.method == 'POST':
        params = parse_post_params(request)
        days_threshold = int(params.get("days_threshold", 7))
        dry_run = params.get("dry_run", False)
        
        try:
            log_dir = os.path.join(BASE_DIR, "log")
            log_manager = LogManager(log_dir)
            
            # 执行压缩
            result = log_manager.compress_old_logs(days_threshold, dry_run=dry_run)
            
            ret = True
            msg = "success"
            data = result
            
            if not dry_run and result['compressed_count'] > 0:
                g_logger.info(f"LOG_COMPRESS - Compressed {result['compressed_count']} files, saved {result['total_size_saved_mb']} MB")
            
        except Exception as e:
            msg = str(e)
            g_logger.error(f"LOG_COMPRESS_ERROR - {msg}")
    else:
        msg = "request method not supported"
    
    res = {
        "code": 1000 if ret else 0,
        "msg": msg,
        "data": data
    }
    return HttpResponseJson(res)


@require_permission('SYSTEM_MANAGE')
def api_getLogConfig(request):
    """获取日志配置"""
    ret = False
    msg = "未知错误"
    data = {}
    
    try:
        data = g_config.logging
        ret = True
        msg = "success"
        
    except Exception as e:
        msg = str(e)
        g_logger.error(f"LOG_CONFIG_GET_ERROR - {msg}")
    
    res = {
        "code": 1000 if ret else 0,
        "msg": msg,
        "data": data
    }
    return HttpResponseJson(res)
