import os
from django.http import HttpResponse
from django.shortcuts import render
from app.views.ViewsBase import *
from app.models import *
import random
from app.utils.UploadUtils import UploadUtils

def list_files_in_directory(directory):
    files_and_dirs = []

    for root, dirs, files in os.walk(directory):
        for name in files:
            file_path = os.path.join(root, name)
            files_and_dirs.append(f"File: {file_path}")
        for name in dirs:
            dir_path = os.path.join(root, name)
            files_and_dirs.append(f"Directory: {dir_path}")

    return files_and_dirs


def directory_list_view(request):
    context = {

    }

    params = parse_get_params(request)

    train_code = params.get("code")
    # params = parse_post_params(request)
    # train_code = params.get("code", "").strip()
    train_dir = "%s/train/%s" % (g_config.storageDir, train_code)
    if not os.path.exists(train_dir):
        os.makedirs(train_dir)

    # train_best_model_filepath = os.path.join(train_dir, "train/weights/best.pt")
    # if not os.path.exists(train_best_model_filepath):
    #     raise Exception("该训练任务暂无模型！")

    file = request.FILES.get("file0")
    directory = os.path.join(train_dir,"train")  #'/path/to/your/folder'  # 替换为实际的文件夹路径
    files_and_dirs = list_files_in_directory(directory)
    list=[]
    for it in files_and_dirs:
        list.append(it.replace("File: ","").replace("'",""))

    # response_content = '<br>'.join(files_and_dirs)
    #
    #
    # return HttpResponse(response_content)
    # context["pagesize"] = files_and_dirs.count()
    context["handle"] = "result"
    context["storageDir_www"] = g_config.storageDir_www
    context["train_code"] = train_code
    context["filelist"] = list   # files_and_dirs
    return render(request, 'app/train/files.html', context)