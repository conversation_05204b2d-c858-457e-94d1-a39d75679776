from django import template
from app.permissions import has_permission, is_annotator_only

register = template.Library()

@register.filter
def has_task_manage_permission(user):
    """检查用户是否有任务管理权限"""
    return has_permission(user, 'task_manage')

@register.filter
def has_sample_manage_permission(user):
    """检查用户是否有样本管理权限"""
    return has_permission(user, 'sample_manage')

@register.filter
def has_train_manage_permission(user):
    """检查用户是否有训练管理权限"""
    return has_permission(user, 'train_manage')

@register.filter
def has_user_manage_permission(user):
    """检查用户是否有用户管理权限"""
    return has_permission(user, 'user_manage')

@register.filter
def has_annotation_only_permission(user):
    """检查用户是否有标注权限"""
    return has_permission(user, 'annotation_only')

@register.filter
def has_annotation_access(user):
    """检查用户是否可以访问标注功能（标注员或样本管理员）"""
    return has_permission(user, 'annotation_only') or has_permission(user, 'sample_manage')

@register.filter
def has_task_access(user):
    """检查用户是否可以访问任务功能（标注员、管理员或任务管理员）"""
    return (has_permission(user, 'annotation_only') or
            has_permission(user, 'task_manage') or
            has_permission(user, 'sample_manage'))

@register.filter
def is_annotator(user):
    """检查用户是否为纯标注员"""
    return is_annotator_only(user)
