# Generated by Django 5.0.4 on 2025-07-25 12:03

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.IntegerField(verbose_name='用户')),
                ('username', models.CharField(max_length=100, verbose_name='用户名')),
                ('sort', models.IntegerField(verbose_name='排序')),
                ('code', models.CharField(max_length=50, verbose_name='编号')),
                ('name', models.CharField(max_length=50, verbose_name='任务名称')),
                ('task_type', models.IntegerField(verbose_name='任务类型')),
                ('remark', models.TextField(verbose_name='扩展参数')),
                ('sample_annotation_count', models.IntegerField(verbose_name='样本已标注数量')),
                ('sample_count', models.IntegerField(verbose_name='样本总数量')),
                ('labels', models.TextField(verbose_name='标签')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('create_timestamp', models.IntegerField(verbose_name='创建时间戳')),
                ('last_update_time', models.DateTimeField(auto_now_add=True, verbose_name='更新时间')),
                ('state', models.IntegerField(verbose_name='状态')),
            ],
            options={
                'verbose_name': '任务',
                'verbose_name_plural': '任务',
                'db_table': 'xc_task',
            },
        ),
        migrations.CreateModel(
            name='TaskSample',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort', models.IntegerField(verbose_name='排序')),
                ('code', models.CharField(max_length=50, verbose_name='编号')),
                ('user_id', models.IntegerField(verbose_name='用户')),
                ('username', models.CharField(max_length=100, verbose_name='用户名')),
                ('task_type', models.IntegerField(verbose_name='任务类型')),
                ('task_code', models.CharField(max_length=50, verbose_name='任务编号')),
                ('old_filename', models.CharField(max_length=200, verbose_name='原名称')),
                ('new_filename', models.CharField(max_length=200, verbose_name='新名称')),
                ('remark', models.CharField(max_length=100, verbose_name='备注')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('state', models.IntegerField(verbose_name='状态')),
                ('annotation_user_id', models.IntegerField(verbose_name='标注用户')),
                ('annotation_username', models.CharField(max_length=100, verbose_name='标注用户名')),
                ('annotation_time', models.DateTimeField(verbose_name='标注时间')),
                ('annotation_content', models.TextField(verbose_name='标注内容')),
                ('annotation_state', models.IntegerField(verbose_name='标注状态')),
            ],
            options={
                'verbose_name': '样本',
                'verbose_name_plural': '样本',
                'db_table': 'xc_task_sample',
            },
        ),
        migrations.CreateModel(
            name='TaskTrain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort', models.IntegerField(verbose_name='排序')),
                ('code', models.CharField(max_length=50, verbose_name='编号')),
                ('user_id', models.IntegerField(verbose_name='用户')),
                ('username', models.CharField(max_length=100, verbose_name='用户名')),
                ('task_code', models.CharField(max_length=50, verbose_name='任务编号')),
                ('algorithm_code', models.CharField(max_length=50, verbose_name='算法编号')),
                ('device', models.CharField(max_length=50, verbose_name='设备')),
                ('imgsz', models.IntegerField(verbose_name='输入尺寸')),
                ('epochs', models.IntegerField(verbose_name='训练周期')),
                ('batch', models.IntegerField(verbose_name='训练批次')),
                ('save_period', models.IntegerField(verbose_name='保存周期')),
                ('sample_ratio', models.IntegerField(verbose_name='训练验证比例')),
                ('extra', models.TextField(verbose_name='其他参数')),
                ('create_time', models.DateTimeField(verbose_name='create_time')),
                ('train_datasets', models.CharField(max_length=200, verbose_name='train_datasets')),
                ('train_datasets_remark', models.CharField(max_length=200, verbose_name='train_datasets_remark')),
                ('train_datasets_time', models.DateTimeField(verbose_name='train_datasets_time')),
                ('train_command', models.CharField(max_length=200, verbose_name='train_command')),
                ('train_process_name', models.CharField(max_length=100, verbose_name='train_process_name')),
                ('train_pid', models.IntegerField(verbose_name='train_pid')),
                ('train_count', models.IntegerField(verbose_name='train_count')),
                ('train_state', models.IntegerField(verbose_name='train_state')),
                ('train_start_time', models.DateTimeField(verbose_name='train_start_time')),
                ('train_stop_time', models.DateTimeField(verbose_name='train_stop_time')),
                ('train_remark', models.CharField(max_length=200, verbose_name='train_remark')),
            ],
            options={
                'verbose_name': '训练',
                'verbose_name_plural': '训练',
                'db_table': 'xc_task_train',
            },
        ),
        migrations.CreateModel(
            name='TaskTrainTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort', models.IntegerField(verbose_name='排序')),
                ('code', models.CharField(max_length=50, verbose_name='编号')),
                ('user_id', models.IntegerField(verbose_name='用户')),
                ('username', models.CharField(max_length=100, verbose_name='用户名')),
                ('task_code', models.CharField(max_length=50, verbose_name='任务编号')),
                ('train_code', models.CharField(max_length=50, verbose_name='训练编号')),
                ('file_name', models.CharField(max_length=100, verbose_name='文件名称')),
                ('file_size', models.IntegerField(verbose_name='文件大小')),
                ('file_type', models.IntegerField(verbose_name='文件类型')),
                ('calcu_seconds', models.FloatField(verbose_name='计算耗时')),
                ('create_time', models.DateTimeField(verbose_name='create_time')),
            ],
            options={
                'verbose_name': '测试记录',
                'verbose_name_plural': '测试记录',
                'db_table': 'xc_task_train_test',
            },
        ),
    ]
