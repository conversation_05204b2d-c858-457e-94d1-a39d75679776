import time
import json
from datetime import datetime
from django.db import transaction
from django.http import HttpResponseRedirect, HttpResponse
from django.contrib.sessions.backends.base import UpdateError
from django.contrib.sessions.middleware import SessionMiddleware
from app.utils.Logger import log_request_start, log_request_end, get_client_ip

try:
    from django.utils.deprecation import MiddlewareMixin  # Django 1.10.x
except ImportError:
    MiddlewareMixin = object  # Django 1.4.x - Django 1.9.x


class SimpleMiddleware(MiddlewareMixin):
    def process_request(self, request):
        path = request.path_info.lstrip('/')

        # print("process_request:path=%s"%path,request.session.keys(),request.session.get("user"))

        if request.session.has_key("user"):
            request.session["user"] = request.session["user"]
            if path.startswith("login"):
                return HttpResponseRedirect("/")
            else:
                return None
        else:
            if path.startswith("login"):
                # 未登录状态下，需要放开的路由
                return None
            else:
                return HttpResponseRedirect("/login")

    def process_response(self, request, response):
        # print("process_response")
        # return response
        # 简单请求
        response['Access-Control-Allow-Origin'] = '*'  # 允许所有客户端
        # 非简单请求
        if request.method == 'OPTIONS':
            response['Access-Control-Allow-Methods'] = '*'
            response['Access-Control-Allow-Headers'] = '*'
        return response

class RequestLoggingMiddleware(MiddlewareMixin):
    """请求日志记录中间件"""

    def process_request(self, request):
        """处理请求开始"""
        # 记录请求开始时间
        request._start_time = datetime.now()

        # 获取日志记录器和配置（从ViewsBase导入）
        from app.views.ViewsBase import g_logger, g_config

        # 检查是否启用请求日志记录
        if not g_config.logging["request_logging"]:
            return None

        # 记录请求开始
        request_id = log_request_start(g_logger, request)
        request._request_id = request_id

        # 记录请求参数（如果有的话）
        if request.method == 'GET' and request.GET:
            g_logger.info(f"REQUEST_PARAMS - GET: {dict(request.GET)}")
        elif request.method == 'POST':
            # 记录POST参数，但要小心敏感信息
            post_data = {}
            for key, value in request.POST.items():
                # 隐藏密码等敏感信息
                if 'password' in key.lower() or 'pwd' in key.lower():
                    post_data[key] = '***'
                else:
                    post_data[key] = value

            if post_data:
                g_logger.info(f"REQUEST_PARAMS - POST: {post_data}")

            # 如果是JSON请求，也记录JSON数据
            if request.content_type == 'application/json':
                try:
                    body = request.body.decode('utf-8')
                    if body:
                        json_data = json.loads(body)
                        # 同样隐藏敏感信息
                        if isinstance(json_data, dict):
                            for key in json_data:
                                if 'password' in key.lower() or 'pwd' in key.lower():
                                    json_data[key] = '***'
                        g_logger.info(f"REQUEST_PARAMS - JSON: {json_data}")
                except (json.JSONDecodeError, UnicodeDecodeError):
                    g_logger.info("REQUEST_PARAMS - JSON: [Invalid JSON data]")

        return None

    def process_response(self, request, response):
        """处理请求结束"""
        # 获取日志记录器和配置
        from app.views.ViewsBase import g_logger, g_config

        # 检查是否启用请求日志记录
        if not g_config.logging["request_logging"]:
            return response

        # 记录请求结束
        start_time = getattr(request, '_start_time', None)
        log_request_end(g_logger, request, response, start_time)

        # 如果响应是JSON，记录响应数据（仅记录状态码和基本信息）
        if hasattr(response, 'content') and response.get('Content-Type', '').startswith('application/json'):
            try:
                content = response.content.decode('utf-8')
                if content:
                    json_data = json.loads(content)
                    if isinstance(json_data, dict) and 'code' in json_data:
                        g_logger.info(f"RESPONSE_DATA - Code: {json_data.get('code')}, Msg: {json_data.get('msg', 'N/A')}")
            except (json.JSONDecodeError, UnicodeDecodeError):
                pass

        return response

    def process_exception(self, request, exception):
        """处理异常"""
        from app.views.ViewsBase import g_logger

        g_logger.error(f"REQUEST_EXCEPTION - {request.method} {request.path} - Exception: {str(exception)}")
        return None

class ResponseSessionMiddleware(SessionMiddleware):
    def process_response(self, request, response):
        return super().process_response(request, response)