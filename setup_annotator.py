#!/usr/bin/env python
"""
快速设置标注员权限的脚本
使用方法：
1. python setup_annotator.py <username>  # 设置单个用户为标注员
2. python setup_annotator.py --list      # 列出所有用户
3. python setup_annotator.py --help      # 显示帮助
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'framework.settings')
django.setup()

from django.contrib.auth.models import User
from app.models import UserRole
from app.permissions import ROLES, ROLE_PERMISSIONS
import json

def setup_annotator(username):
    """设置用户为标注员"""
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        print(f"❌ 错误：用户 '{username}' 不存在")
        return False
    
    # 创建或更新用户角色
    user_role, created = UserRole.objects.get_or_create(
        user=user,
        defaults={
            'role': 'annotator',
            'permissions': '[]',
            'is_active': True
        }
    )
    
    if not created:
        user_role.role = 'annotator'
        user_role.is_active = True
        user_role.save()
    
    action = "创建" if created else "更新"
    permissions = ROLE_PERMISSIONS.get('annotator', [])
    
    print(f"✅ 成功{action}用户 '{username}' 为标注员")
    print(f"📋 权限列表: {', '.join(permissions)}")
    print(f"🔗 现在用户可以访问标注界面了")
    
    return True

def list_users():
    """列出所有用户及其角色"""
    print("👥 用户列表:")
    print("-" * 60)
    
    users = User.objects.all()
    if not users:
        print("没有找到任何用户")
        return
    
    for user in users:
        # 获取用户角色
        try:
            user_role = UserRole.objects.get(user=user, is_active=True)
            role = user_role.role
            role_desc = ROLES.get(role, '未知角色')
        except UserRole.DoesNotExist:
            role = '未分配'
            role_desc = '无角色'
        
        # 状态标识
        status = []
        if user.is_superuser:
            status.append("超级管理员")
        if not user.is_active:
            status.append("已禁用")
        
        status_str = f" ({', '.join(status)})" if status else ""
        
        print(f"👤 {user.username:<15} | {role:<10} | {role_desc}{status_str}")

def show_help():
    """显示帮助信息"""
    print("""
🚀 标注员权限设置工具

📖 使用方法:
  python setup_annotator.py <username>     设置指定用户为标注员
  python setup_annotator.py --list         列出所有用户及其角色
  python setup_annotator.py --help         显示此帮助信息

📝 示例:
  python setup_annotator.py john           # 设置用户john为标注员
  python setup_annotator.py --list         # 查看所有用户

🔧 高级设置:
  如需更复杂的权限配置，请使用:
  python manage.py set_user_role <username> <role>

💡 提示:
  - 标注员角色拥有 'annotation_only' 权限
  - 设置后用户可以访问标注界面和任务列表
  - 权限配置会持久化到数据库，服务重启后不会丢失
""")

def main():
    if len(sys.argv) < 2:
        show_help()
        return
    
    arg = sys.argv[1]
    
    if arg == '--help' or arg == '-h':
        show_help()
    elif arg == '--list' or arg == '-l':
        list_users()
    else:
        username = arg
        if setup_annotator(username):
            print(f"\n🎯 下一步:")
            print(f"1. 确保用户 '{username}' 已激活")
            print(f"2. 用户可以登录并访问标注功能")
            print(f"3. 如有问题，请检查用户权限配置")

if __name__ == '__main__':
    main()
