#!/usr/bin/env python3
"""
测试日志系统
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'framework.settings')
django.setup()

from app.utils.Logger import CreateLogger, set_request_id, log_request_start, log_request_end
from app.utils.LogManager import LogManager
from framework.settings import BASE_DIR
import time
from datetime import datetime


def test_basic_logging():
    """测试基本日志功能"""
    print("测试基本日志功能...")
    
    # 创建测试日志记录器
    log_dir = os.path.join(BASE_DIR, "log")
    test_logger = CreateLogger(
        filepath=os.path.join(log_dir, "test.log"),
        is_show_console=True,
        max_bytes=1024*1024,  # 1MB for testing
        backup_count=5
    )
    
    # 设置请求ID
    request_id = set_request_id("TEST001")
    print(f"设置请求ID: {request_id}")
    
    # 测试不同级别的日志
    test_logger.info("这是一条INFO级别的测试日志")
    test_logger.warning("这是一条WARNING级别的测试日志")
    test_logger.error("这是一条ERROR级别的测试日志")
    
    # 测试业务日志
    test_logger.info("LOGIN_ATTEMPT - Username: test_user")
    test_logger.info("DB_SELECT - SQL: SELECT * FROM test_table")
    test_logger.info("REQUEST_START - GET /test from 127.0.0.1")
    
    print("基本日志测试完成")


def test_log_manager():
    """测试日志管理器"""
    print("\n测试日志管理器...")
    
    log_dir = os.path.join(BASE_DIR, "log")
    log_manager = LogManager(log_dir)
    
    # 获取日志统计
    stats = log_manager.get_log_stats()
    print(f"日志文件统计:")
    print(f"  总文件数: {stats['total_files']}")
    print(f"  总大小: {stats['total_size_mb']} MB")
    
    if stats['files']:
        print(f"  文件列表:")
        for file_info in stats['files'][:3]:  # 只显示前3个文件
            print(f"    {file_info['file']}: {file_info['size_mb']} MB, {file_info['age_days']} 天")
    
    print("日志管理器测试完成")


def test_log_rotation():
    """测试日志轮转"""
    print("\n测试日志轮转...")
    
    log_dir = os.path.join(BASE_DIR, "log")
    test_logger = CreateLogger(
        filepath=os.path.join(log_dir, "rotation_test.log"),
        is_show_console=False,
        max_bytes=1024,  # 1KB for quick rotation testing
        backup_count=3
    )
    
    # 写入大量日志触发轮转
    for i in range(100):
        test_logger.info(f"这是第 {i+1} 条测试日志，用于测试日志轮转功能。" + "X" * 50)
    
    # 检查是否生成了轮转文件
    rotation_files = []
    for file in os.listdir(log_dir):
        if file.startswith("rotation_test.log"):
            rotation_files.append(file)
    
    print(f"生成的轮转文件: {sorted(rotation_files)}")
    print("日志轮转测试完成")


def test_config_loading():
    """测试配置加载"""
    print("\n测试配置加载...")
    
    from app.utils.Config import Config
    
    config = Config(filepath=os.path.join(BASE_DIR, "config.json"))
    logging_config = config.logging
    
    print(f"日志配置:")
    print(f"  最大文件大小: {logging_config['max_file_size_mb']} MB")
    print(f"  备份文件数: {logging_config['backup_count']}")
    print(f"  日志级别: {logging_config['log_level']}")
    print(f"  控制台输出: {logging_config['console_output']}")
    print(f"  请求日志: {logging_config['request_logging']}")
    print(f"  数据库日志: {logging_config['db_logging']}")
    
    print("配置加载测试完成")


def main():
    """主测试函数"""
    print("开始测试XCLabel日志系统...")
    print("=" * 50)
    
    try:
        test_basic_logging()
        test_log_manager()
        test_log_rotation()
        test_config_loading()
        
        print("\n" + "=" * 50)
        print("所有测试完成！日志系统工作正常。")
        
        # 显示日志文件位置
        log_dir = os.path.join(BASE_DIR, "log")
        print(f"\n日志文件位置: {log_dir}")
        print("你可以查看以下文件:")
        for file in os.listdir(log_dir):
            if file.endswith('.log') or '.log.' in file:
                file_path = os.path.join(log_dir, file)
                size_kb = os.path.getsize(file_path) / 1024
                print(f"  {file} ({size_kb:.1f} KB)")
        
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
